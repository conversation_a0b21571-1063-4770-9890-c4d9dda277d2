{"name": "revmigrate", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test"}, "engines": {"node": "20.x"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@ng-select/ng-select": "11", "cliui": "7.0.4", "cytoscape": "^3.31.1", "cytoscape-cose-bilkent": "^4.1.0", "cytoscape-dagre": "^2.5.0", "cytoscape-fcose": "^2.2.0", "highlight.js": "^11.11.1", "marked": "^15.0.8", "ngx-highlightjs": "11", "ngx-markdown": "^19.1.1", "rippleui": "^1.12.1", "rxjs": "~7.8.0", "source-map-loader": "^5.0.0", "string-width": "4.2.0", "strip-ansi": "6.0.1", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.7", "@angular/cli": "^16.2.7", "@angular/compiler-cli": "^16.2.0", "@types/cytoscape-dagre": "^2.3.3", "@types/cytoscape-fcose": "^2.2.4", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "tailwindcss": "3", "typescript": "~5.1.3"}}
.step-done .step-circle,
.step-done.step-primary .step-circle {
  --tw-border-opacity: 1;
  --tw-bg-opacity: 1;
  --tw-text-opacity: 1;
  background-color: #ececff;
  border: none;
  color: rgb(var(--primary) / var(--tw-text-opacity));
}
.step-done.step-primary:after {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--primary) / var(--tw-bg-opacity));
}
.step,
.steps-horizontal {
  width: auto;
}
.text-capitalize {
  text-transform: capitalize !important;
}

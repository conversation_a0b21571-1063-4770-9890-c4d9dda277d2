import { Component, OnInit } from '@angular/core';
import { Observable, of } from 'rxjs';
import { take } from 'rxjs/operators';
import { UserManagementService } from '../../core/services/user-management/user-management.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthService } from '../../core/auth/auth.service';
import { Router } from '@angular/router';
export interface Privilege {
  id: number;
  name: string;
}
export interface Organization {
  id?: string;
  name: string;
  email: string;
  domain?: string;
  type: string;
  status: string;
  description?: string;
  website?: string;
  address?: string;
}
@Component({
  selector: 'app-user-management',
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.css'],
})
export class UserManagementComponent implements OnInit {
  privileges$!: Observable<Privilege[]>; // assuming this comes from a service
  selectedPrivileges: number[] = [];
  organizationForm!: FormGroup;
  superUserForm!: FormGroup;
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  activeTab: string = 'organization';
  superUserExists = false;
  selectedOrgId: string | undefined = undefined;
  selectedOrgName: string = '';

  userForm!: FormGroup;
  roleForm!: FormGroup;

  // Admin form
  adminForm!: FormGroup;

  // Current user information
  currentUser: any = null;
  isSuperUser: boolean = false;
  isOrgAdmin: boolean = false;
  userOrgId: string = '';
  isRevUpAIOrg: boolean = false;

  constructor(
    private userManagementService: UserManagementService,
    private authService: AuthService,
    private fb: FormBuilder,
    private router: Router
  ) {
    this.initOrganizationForm();
    this.initSuperUserForm();
    this.initUserForm();
    this.initRoleForm();
    this.initAdminForm();
  }

  // Navigate to organization details page
  navigateToOrganizationDetails(organizationId: string) {
    if (!organizationId) {
      this.errorMessage = 'Invalid organization ID';
      return;
    }

    console.log('Navigating to organization details:', organizationId);
    this.router.navigate(['/admin/organization', organizationId]);
  }

  initUserForm() {
    this.userForm = this.fb.group(
      {
        full_name: ['', [Validators.required]],
        email: ['', [Validators.required, Validators.email]],
        password: ['', [Validators.required, Validators.minLength(8)]],
        confirm_password: ['', [Validators.required]],
        role_id: ['', [Validators.required]],
        send_welcome_email: [true],
      },
      {
        validators: this.passwordMatchValidator,
      }
    );
  }

  initRoleForm() {
    this.roleForm = this.fb.group({
      name: [{ value: '', disabled: false }, [Validators.required]],
      description: [{ value: '', disabled: false }, [Validators.required]],
      is_sharable: [{ value: true, disabled: false }],
      permissions: [{}],
    });
  }

  initOrganizationForm() {
    this.organizationForm = this.fb.group({
      name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      domain: [''],
      type: ['Enterprise', [Validators.required]],
      status: ['Active', [Validators.required]],
      website: [''],
      address: [''],
      description: [''],
      send_welcome_email: [false],
    });
  }

  initSuperUserForm() {
    this.superUserForm = this.fb.group(
      {
        full_name: ['', [Validators.required]],
        email: ['', [Validators.required, Validators.email]],
        password: ['', [Validators.required, Validators.minLength(8)]],
        confirm_password: ['', [Validators.required]],
      },
      {
        validators: this.passwordMatchValidator,
      }
    );
  }

  initAdminForm() {
    this.adminForm = this.fb.group(
      {
        full_name: ['', [Validators.required]],
        email: ['', [Validators.required, Validators.email]],
        password: ['', [Validators.required, Validators.minLength(8)]],
        confirm_password: ['', [Validators.required]],
        send_welcome_email: [true],
      },
      {
        validators: this.passwordMatchValidator,
      }
    );
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password')?.value;
    const confirmPassword = form.get('confirm_password')?.value;

    if (password && confirmPassword && password !== confirmPassword) {
      form.get('confirm_password')?.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    return null;
  }

  createSuperUser() {
    if (this.superUserForm.invalid) {
      this.markFormGroupTouched(this.superUserForm);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const formData = this.superUserForm.value;

    // Prepare data for API - ensure exact format matching API documentation
    const userData = {
      email: formData.email,
      password: formData.password,
      full_name: formData.full_name,
    };

    console.log('Creating super user with data:', {
      ...userData,
      password: '********',
    });

    const apiUrl =
      this.userManagementService['apiBaseUrl'] + '/auth/create-superuser';
    console.log('API URL:', apiUrl);

    // Debug with direct fetch for comparison
    const token = localStorage.getItem('token');
    console.log('Attempting direct fetch for debugging...');
    fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(userData),
    })
      .then((response) => {
        console.log('Direct fetch response status:', response.status);
        return response.text().then((text) => {
          try {
            return text ? JSON.parse(text) : {};
          } catch (e) {
            console.log('Raw response:', text);
            return {};
          }
        });
      })
      .then((data) => console.log('Direct fetch response data:', data))
      .catch((error) => console.error('Direct fetch error:', error));

    this.userManagementService.createSuperUser(userData).subscribe({
      next: () => {
        this.successMessage = 'Super user created successfully!';
        this.resetSuperUserForm();
        this.isLoading = false;
        // Close modal
        const modalCheckbox = document.getElementById(
          'AddSuperUserModal'
        ) as HTMLInputElement;
        if (modalCheckbox) {
          modalCheckbox.checked = false;
        }
      },
      error: (error) => {
        console.error('Error creating super user:', error);

        // Extract detailed error information
        let detailedError = '';
        if (error.error) {
          if (typeof error.error === 'object') {
            // Try to extract structured error information
            if (error.error.detail) {
              detailedError = error.error.detail;
            } else if (error.error.message) {
              detailedError = error.error.message;
            } else {
              // If there are validation errors, format them
              const validationErrors = Object.entries(error.error)
                .map(([field, errors]) => `${field}: ${errors}`)
                .join(', ');
              if (validationErrors) {
                detailedError = `Validation errors: ${validationErrors}`;
              }
            }
          } else if (typeof error.error === 'string') {
            // Handle string error
            detailedError = error.error;
          }
        }

        if (detailedError) {
          // Check for the specific "Superuser already exists" error
          if (detailedError.includes('Superuser already exists')) {
            this.errorMessage =
              'A super user already exists in the system. You cannot create another one.';
          } else {
            this.errorMessage = `Failed to create super user: ${detailedError}`;
          }
        } else {
          this.errorMessage = `Failed to create super user. Server returned status ${error.status}. Please try again.`;
        }

        this.isLoading = false;
      },
    });
  }

  resetSuperUserForm() {
    this.superUserForm.reset();
    this.errorMessage = '';
    this.successMessage = '';
  }

  resetUserForm() {
    this.userForm.reset();
    this.userForm.patchValue({
      send_welcome_email: true,
    });
    this.errorMessage = '';
    this.successMessage = '';
  }

  resetAdminForm() {
    this.adminForm.reset();
    this.adminForm.patchValue({
      send_welcome_email: true,
    });
    this.errorMessage = '';
    this.successMessage = '';
  }

  resetRoleForm() {
    this.roleForm.reset();
    this.roleForm.patchValue({
      is_sharable: true,
      permissions: {},
    });
    this.selectedPrivileges = [];
    this.errorMessage = '';
    this.successMessage = '';
  }

  // This method is renamed to avoid duplication with the one at line 890
  resetOrgForm() {
    this.organizationForm.reset();
    this.organizationForm.patchValue({
      type: 'Enterprise',
      status: 'Active',
      send_welcome_email: false,
    });
    this.errorMessage = '';
    this.successMessage = '';
  }

  createUser() {
    // Check if user has permission to create users
    if (!this.canCreateUsers()) {
      this.errorMessage = 'You do not have permission to create users.';
      return;
    }

    if (this.userForm.invalid) {
      this.markFormGroupTouched(this.userForm);
      return;
    }

    if (!this.selectedOrgId) {
      this.errorMessage =
        'No organization selected. Please select an organization first.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const formData = this.userForm.value;

    // Prepare data for API
    const userData = {
      email: formData.email,
      full_name: formData.full_name,
      password: formData.password,
      role_id: formData.role_id,
      send_welcome_email: formData.send_welcome_email,
    };

    console.log('Creating user with data:', {
      ...userData,
      password: '********',
      organization_id: this.selectedOrgId,
    });

    this.userManagementService
      .createOrganizationUser(this.selectedOrgId, userData)
      .subscribe({
        next: (_) => {
          this.successMessage = 'User created successfully!';
          this.resetUserForm();
          this.loadUsers(); // Reload the users list
          this.isLoading = false;

          // Close modal
          const modalCheckbox = document.getElementById(
            'AddUserModal'
          ) as HTMLInputElement;
          if (modalCheckbox) {
            modalCheckbox.checked = false;
          }
        },
        error: (error) => {
          console.error('Error creating user:', error);

          let detailedError = '';
          if (error.error) {
            if (typeof error.error === 'string') {
              detailedError = error.error;
            } else if (error.error.detail) {
              detailedError = error.error.detail;
            } else if (error.error.message) {
              detailedError = error.error.message;
            }
          }

          if (detailedError) {
            this.errorMessage = `Failed to create user: ${detailedError}`;
          } else {
            this.errorMessage = `Failed to create user. Server returned status ${error.status}. Please try again.`;
          }

          this.isLoading = false;
        },
      });
  }

  createRole() {
    // Check if user has permission to create roles
    if (!this.canCreateRoles()) {
      this.errorMessage = 'You do not have permission to create roles.';
      return;
    }

    if (this.roleForm.invalid) {
      this.markFormGroupTouched(this.roleForm);
      return;
    }

    if (!this.selectedOrgId) {
      this.errorMessage =
        'No organization selected. Please select an organization first.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const formData = this.roleForm.value;

    // Log the selected privileges before processing
    console.log(
      'Selected privileges before processing:',
      this.selectedPrivileges
    );

    // Create a promise to ensure we wait for the privileges to be processed
    const getPermissionsPromise = new Promise<Record<string, boolean>>(
      (resolve) => {
        // Convert selected privileges to permissions object
        const permissions: Record<string, boolean> = {};

        if (this.privileges$ && this.selectedPrivileges.length > 0) {
          // Get the actual privilege names from the privileges$ observable
          this.privileges$.pipe(take(1)).subscribe((privileges) => {
            console.log('Available privileges:', privileges);

            this.selectedPrivileges.forEach((id) => {
              const privilege = privileges.find((p) => p.id === id);
              if (privilege) {
                // Convert spaces to underscores for API format
                const permissionKey = privilege.name.replace(/ /g, '_');
                permissions[permissionKey] = true;
                console.log(
                  `Mapped privilege ID ${id} (${privilege.name}) to permission key ${permissionKey}`
                );
              } else {
                console.warn(`Could not find privilege with ID ${id}`);
              }
            });

            console.log('Final permissions object:', permissions);
            resolve(permissions);
          });
        } else {
          console.warn(
            'No privileges selected or privileges$ is not available'
          );
          resolve(permissions);
        }
      }
    );

    // Wait for permissions to be processed, then send the API request
    getPermissionsPromise.then((permissions) => {
      // Prepare data for API
      const roleData = {
        name: formData.name,
        description: formData.description,
        is_sharable: formData.is_sharable,
        permissions: permissions,
      };

      console.log('Creating role with data:', roleData);

      if (!this.selectedOrgId) {
        this.errorMessage =
          'No organization selected. Please select an organization first.';
        this.isLoading = false;
        return;
      }

      this.userManagementService
        .createOrganizationRole(this.selectedOrgId, roleData)
        .subscribe({
          next: (response) => {
            console.log('Role creation response:', response);
            this.successMessage = 'Role created successfully!';

            // If the API returned a role with empty permissions, update it
            // But use a different approach to avoid infinite loops
            if (
              response &&
              response.id &&
              (!response.permissions ||
                Object.keys(response.permissions).length === 0)
            ) {
              console.log(
                'Created role has empty permissions, updating with selected permissions'
              );

              // Update the role with the permissions we just sent
              if (this.selectedOrgId) {
                // Store the role ID and permissions for later use
                const roleId = response.id.toString();
                const roleName = response.name;

                // Add the role to our local array with the permissions we intended to set
                // This avoids having to reload the entire list and potentially causing loops
                if (this.roles) {
                  const newRole = {
                    id: roleId,
                    role: roleName,
                    description: roleData.description,
                    is_sharable: roleData.is_sharable,
                    privileges: this.formatPermissions(roleData.permissions),
                    permissions: roleData.permissions,
                  };

                  // Add to the beginning of the array
                  this.roles.unshift(newRole);

                  // Update the role ID to name mapping
                  this.roleIdToNameMap.set(roleId, roleName);

                  // Update total entries
                  this.totalEntries = this.roles.length;

                  console.log('Added new role to local array:', newRole);
                }

                // Update the role permissions in the background without reloading
                this.userManagementService
                  .updateRolePermissions(this.selectedOrgId, roleId, roleData)
                  .subscribe({
                    next: (updatedRole) => {
                      console.log(
                        'Updated role with permissions in background:',
                        updatedRole
                      );
                    },
                    error: (err) => {
                      console.error(
                        'Error updating role permissions in background:',
                        err
                      );
                    },
                  });
              }

              this.resetRoleForm();
            } else {
              this.resetRoleForm();
              this.loadRoles(); // Only reload if permissions were already set correctly
            }

            this.isLoading = false;

            // Close modal
            const modalCheckbox = document.getElementById(
              'AddRoleModal'
            ) as HTMLInputElement;
            if (modalCheckbox) {
              modalCheckbox.checked = false;
            }
          },
          error: (error) => {
            console.error('Error creating role:', error);

            let detailedError = '';
            if (error.error) {
              if (typeof error.error === 'string') {
                detailedError = error.error;
              } else if (error.error.detail) {
                detailedError = error.error.detail;
              } else if (error.error.message) {
                detailedError = error.error.message;
              }
            }

            if (detailedError) {
              this.errorMessage = `Failed to create role: ${detailedError}`;
            } else {
              this.errorMessage = `Failed to create role. Server returned status ${error.status}. Please try again.`;
            }

            this.isLoading = false;
          },
        });
    });
  }

  ngOnInit() {
    // Load current user information first
    this.loadCurrentUser();

    // Load permissions from API
    this.loadPermissions();

    // Check if super user exists
    this.checkSuperUserExists();
  }

  loadCurrentUser() {
    // Get detailed user role information from the dedicated endpoint
    this.authService.getUserRoleDetails().subscribe({
      next: (roleDetails) => {
        this.currentUser = roleDetails;
        console.log('Current user role details:', roleDetails);

        // Set basic user information
        this.isSuperUser = roleDetails.is_superuser || false;

        // Case-insensitive check for Admin role
        const roleName = roleDetails.role?.name || roleDetails.role?.role || '';
        const hasRoleId =
          roleDetails.role_id !== undefined && roleDetails.role_id !== null;

        // Consider user an admin if they have Admin role name OR they have a role_id
        this.isOrgAdmin = roleName.toLowerCase() === 'admin' || hasRoleId;

        this.userOrgId = roleDetails.organization?.id || '';
        this.isRevUpAIOrg =
          roleDetails.organization?.name === 'RevUp AI' || false;

        console.log('User role info:', {
          isSuperUser: this.isSuperUser,
          isOrgAdmin: this.isOrgAdmin,
          role: roleDetails.role?.name,
          roleLowerCase: roleName.toLowerCase(),
          hasRoleId: hasRoleId,
          roleId: roleDetails.role_id,
          organizationId: this.userOrgId,
          organizationName: roleDetails.organization?.name,
        });

        // Log permissions for debugging
        if (roleDetails.role && roleDetails.role.permissions) {
          // Permissions are always in array format from the user-role endpoint
          console.log('User permissions:', roleDetails.role.permissions);
        }

        // For non-super users, redirect directly to their organization's details page
        if (!this.isSuperUser && this.userOrgId) {
          console.log(
            'Non-super user detected, redirecting to organization details:',
            this.userOrgId
          );
          this.navigateToOrganizationDetails(this.userOrgId);
          return;
        } else if (!this.isSuperUser) {
          // If no organization ID is available, set the active tab to 'user'
          this.activeTab = 'user';
        }

        // Load organizations based on user permissions
        this.loadOrganizations();
      },
      error: (error) => {
        console.error('Error loading current user role details:', error);

        // Fallback to getUserDetails if getUserRoleDetails fails
        this.authService.getUserDetails().subscribe({
          next: (user) => {
            this.currentUser = user;
            console.log('Fallback to user details:', user);

            // Set basic user information
            this.isSuperUser = user.is_superuser || false;

            // Case-insensitive check for Admin role
            const roleName = user.role?.name || '';
            const hasRoleId =
              user.role_id !== undefined && user.role_id !== null;

            // Consider user an admin if they have Admin role name OR they have a role_id
            this.isOrgAdmin = roleName.toLowerCase() === 'admin' || hasRoleId;

            this.userOrgId = user.organization_id || '';
            this.isRevUpAIOrg = user.organization?.name === 'RevUp AI' || false;

            console.log('User role info (fallback):', {
              isSuperUser: this.isSuperUser,
              isOrgAdmin: this.isOrgAdmin,
              role: user.role?.name,
              roleLowerCase: roleName.toLowerCase(),
              hasRoleId: hasRoleId,
              roleId: user.role_id,
              organizationId: this.userOrgId,
            });

            // For non-super users, redirect directly to their organization's details page
            if (!this.isSuperUser && this.userOrgId) {
              console.log(
                'Non-super user detected (fallback), redirecting to organization details:',
                this.userOrgId
              );
              this.navigateToOrganizationDetails(this.userOrgId);
              return;
            } else if (!this.isSuperUser) {
              // If no organization ID is available, set the active tab to 'user'
              this.activeTab = 'user';
            }

            // Load organizations based on user permissions
            this.loadOrganizations();
          },
          error: (err) => {
            console.error('Error in fallback user details:', err);
            // Default to no special permissions
            this.isSuperUser = false;
            this.isOrgAdmin = false;
            this.userOrgId = '';
            this.isRevUpAIOrg = false;

            // Load organizations with default permissions
            this.loadOrganizations();
          },
        });
      },
    });
  }

  // Check if user can create organizations
  canCreateOrganization(): boolean {
    // Only superusers or users from RevUp AI organization can create organizations
    return this.isSuperUser || this.isRevUpAIOrg;
  }

  // Check if user can create users
  canCreateUsers(): boolean {
    // Superusers and organization admins can create users
    // isOrgAdmin is now set to true if user has either Admin role name or a role_id
    const hasRoleId =
      this.currentUser?.role_id !== undefined &&
      this.currentUser?.role_id !== null;
    console.log('Can create users check:', {
      isSuperUser: this.isSuperUser,
      isOrgAdmin: this.isOrgAdmin,
      hasRoleId: hasRoleId,
    });
    return this.isSuperUser || this.isOrgAdmin || hasRoleId;
  }

  // Check if user can create admins
  canCreateAdmins(): boolean {
    // Only superusers can create organization admins
    return this.isSuperUser;
  }

  // Check if user can create roles
  canCreateRoles(): boolean {
    // Only super users can create roles by default
    // Organization admins need specific permission
    if (this.isSuperUser) {
      return true;
    }

    // Check if user has create_roles permission
    if (this.currentUser?.role?.permissions) {
      // From user-role endpoint, permissions are always in array format
      if (Array.isArray(this.currentUser.role.permissions)) {
        return this.currentUser.role.permissions.includes('create_roles');
      } else {
        // For backward compatibility with getUserDetails
        return this.currentUser.role.permissions['create_roles'] === true;
      }
    }

    return false;
  }

  loadPermissions() {
    this.userManagementService.getAllPermissions().subscribe({
      next: (permissions) => {
        // Convert permissions to the format expected by the UI
        this.privileges$ = of(
          permissions.map((permission, index) => ({
            id: index + 1,
            name: permission,
          }))
        );
      },
      error: (error) => {
        console.error('Error loading permissions:', error);
        // Fallback to sample data
        this.privileges$ = of([
          { id: 1, name: 'Read Access' },
          { id: 2, name: 'Write Access' },
          { id: 3, name: 'Execute Access' },
        ]);
      },
    });
  }

  // Role ID to name mapping
  roleIdToNameMap: Map<string, string> = new Map();

  /**
   * Get role name by ID
   * @param roleId Role ID to look up
   * @returns Role name or 'Unknown Role' if not found
   */
  getRoleNameById(roleId?: string): string {
    if (!roleId) return 'No Role';
    return this.roleIdToNameMap.get(roleId) || 'Unknown Role';
  }

  loadRoles() {
    if (!this.selectedOrgId) {
      console.log('No organization selected, cannot load roles');
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    this.userManagementService
      .getOrganizationRoles(this.selectedOrgId)
      .subscribe({
        next: (data) => {
          // Log the raw data from the API for debugging
          console.log('Raw roles data from API:', data);

          // Map API response to our roles array
          this.roles = data.map((role: any) => {
            // Ensure permissions is not null or undefined
            let permissions = role.permissions || {};

            // Log each role's permissions for debugging
            console.log(
              `Role ${role.name} permissions before processing:`,
              permissions
            );

            // If permissions is empty, use a placeholder for display purposes
            // but don't try to update the role here as it can cause infinite loops
            if (Object.keys(permissions).length === 0) {
              console.log(
                `Role ${role.name} has empty permissions, using placeholder for display`
              );

              // Just use an empty object for display - we'll handle updates elsewhere
              permissions = {};

              // For system roles, we can use predefined permissions for display only
              if (this.isPredefinedRole(role.name)) {
                console.log(
                  `Using predefined permissions for system role ${role.name}`
                );
                // This is just for display and doesn't trigger API calls
              }
            }

            return {
              id: role.id,
              role: role.name,
              description: role.description,
              is_sharable: role.is_sharable,
              privileges: this.formatPermissions(permissions),
              permissions: permissions,
            };
          });

          // Build role ID to name mapping
          this.roleIdToNameMap.clear();
          this.roles.forEach((role) => {
            this.roleIdToNameMap.set(role.id, role.role);
          });

          // Update user roles if users are already loaded
          if (this.users.length > 0) {
            this.updateUserRoleNames();
          }

          this.totalEntries = this.roles.length;
          this.isLoading = false;

          // Update pagination if we're on the role tab
          if (this.activeTab === 'role') {
            this.currentPage = 1;
          }

          console.log('Loaded roles:', this.roles);
          console.log(
            'Role ID to name mapping:',
            Object.fromEntries(this.roleIdToNameMap)
          );
        },
        error: (error) => {
          console.error('Error loading roles:', error);
          this.errorMessage = 'Failed to load roles. Please try again.';
          this.isLoading = false;

          // Fallback to predefined roles if API fails
          this.userManagementService.getAvailableRoles().subscribe((roles) => {
            const roleNames = Object.keys(roles);
            this.roles = roleNames.map((roleName, index) => {
              const permissions: Record<string, boolean> = {};
              const rolePermissions = roles[roleName];

              // Create permissions object
              this.userManagementService
                .getAllPermissions()
                .pipe(take(1))
                .subscribe((allPermissions) => {
                  allPermissions.forEach((permission) => {
                    permissions[permission] =
                      rolePermissions.includes(permission);
                  });
                });

              const roleId = (index + 1).toString();
              // Add to role ID to name mapping
              this.roleIdToNameMap.set(roleId, roleName);

              return {
                id: roleId,
                role: roleName,
                description: `${roleName} role with ${rolePermissions.length} permissions`,
                is_sharable: true,
                privileges: rolePermissions.join(', '),
                permissions: permissions,
              };
            });

            // Update user roles if users are already loaded
            if (this.users.length > 0) {
              this.updateUserRoleNames();
            }

            this.totalEntries = this.roles.length;
            console.log('Using predefined roles:', this.roles);
          });
        },
      });
  }

  formatPermissions(permissions: Record<string, boolean> | string[]): string {
    if (!permissions) return 'No permissions';

    let activePermissions: string[] = [];

    // Handle array format
    if (Array.isArray(permissions)) {
      console.log('Formatting array permissions:', permissions);
      activePermissions = permissions.map((permission) =>
        this.formatPermissionName(permission)
      );
    } else {
      // Handle object format
      console.log('Formatting object permissions:', permissions);
      activePermissions = Object.entries(permissions)
        .filter(([_, value]) => value)
        .map(([key, _]) => this.formatPermissionName(key));
    }

    console.log('Active permissions after formatting:', activePermissions);
    return activePermissions.length > 0
      ? activePermissions.join(', ')
      : 'No permissions';
  }

  /**
   * Format a permission name to be more readable
   * @param permission Permission name (e.g., create_connection)
   * @returns Formatted permission name (e.g., Create Connection)
   */
  formatPermissionName(permission: string): string {
    if (!permission) return '';

    // Replace underscores with spaces and capitalize each word
    return permission
      .replace(/_/g, ' ')
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Check if a role is a predefined system role
   * @param roleName Role name to check
   * @returns True if the role is predefined, false otherwise
   */
  isPredefinedRole(roleName?: string): boolean {
    if (!roleName) return false;

    const predefinedRoles = [
      'Admin',
      'Developer',
      'Tester',
      'Analyst',
      'Deployment',
    ];
    return predefinedRoles.includes(roleName);
  }

  /**
   * Get a list of permissions that are enabled for a role
   * @param permissions Permissions object with permission names as keys and boolean values or array of permission strings
   * @returns Array of permission names that are enabled
   */
  getPermissionsList(
    permissions?: Record<string, boolean> | string[]
  ): string[] {
    if (!permissions) return [];

    // Handle array format
    if (Array.isArray(permissions)) {
      console.log('Getting permissions list from array:', permissions);
      return permissions;
    }

    // Handle object format
    console.log('Getting permissions list from object:', permissions);
    const permissionsList = Object.entries(permissions)
      .filter(([_, value]) => value)
      .map(([key, _]) => key);

    console.log('Filtered permissions list:', permissionsList);
    return permissionsList;
  }

  /**
   * Get the top N permissions from a permissions object or array
   * @param permissions Permissions object with permission names as keys and boolean values or array of permission strings
   * @param count Number of permissions to return
   * @returns Array of permission names
   */
  getTopPermissions(
    permissions?: Record<string, boolean> | string[],
    count: number = 3
  ): string[] {
    if (!permissions) return [];

    const enabledPermissions = this.getPermissionsList(permissions);
    return enabledPermissions.slice(0, count);
  }

  /**
   * Count the number of enabled permissions in a permissions object or array
   * @param permissions Permissions object with permission names as keys and boolean values or array of permission strings
   * @returns Number of enabled permissions
   */
  countPermissions(permissions?: Record<string, boolean> | string[]): number {
    if (!permissions) return 0;

    return this.getPermissionsList(permissions).length;
  }

  /**
   * Open the view role modal to see role details
   * @param role Role to view
   */
  openViewRoleModal(role: any) {
    this.selectedRole = role;
    this.errorMessage = '';
    this.successMessage = '';

    // Open the modal
    const modalCheckbox = document.getElementById(
      'ViewRoleModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  /**
   * Open the delete role modal to confirm deletion
   * @param role Role to delete
   */
  openDeleteRoleModal(role: any) {
    this.selectedRole = role;
    this.errorMessage = '';
    this.successMessage = '';

    // Open the modal
    const modalCheckbox = document.getElementById(
      'DeleteRoleModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  /**
   * Delete the selected role
   */
  deleteRole() {
    if (!this.selectedRole || !this.selectedRole.id) {
      this.errorMessage = 'No role selected for deletion';
      return;
    }

    if (!this.canDeleteRole(this.selectedRole)) {
      this.errorMessage = 'You do not have permission to delete this role';
      return;
    }

    if (!this.selectedOrgId) {
      this.errorMessage = 'No organization selected';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.userManagementService
      .deleteRole(this.selectedOrgId, this.selectedRole.id)
      .subscribe({
        next: () => {
          this.successMessage = 'Role deleted successfully!';

          // Remove the role from our local array instead of reloading
          if (this.selectedRole && this.selectedRole.id) {
            const roleIndex = this.roles.findIndex(
              (r) => r.id === this.selectedRole.id
            );
            if (roleIndex !== -1) {
              // Remove the role from the array
              this.roles.splice(roleIndex, 1);

              // Remove from the role ID to name mapping
              this.roleIdToNameMap.delete(this.selectedRole.id);

              // Update total entries
              this.totalEntries = this.roles.length;

              console.log('Removed role from local array');
            } else {
              // If we can't find the role, reload the list
              console.log('Could not find role in local array, reloading');
              this.loadRoles();
            }
          } else {
            // Fallback to reloading if something went wrong
            this.loadRoles();
          }

          this.isLoading = false;

          // Close modal
          const modalCheckbox = document.getElementById(
            'DeleteRoleModal'
          ) as HTMLInputElement;
          if (modalCheckbox) {
            modalCheckbox.checked = false;
          }
        },
        error: (error) => {
          console.error('Error deleting role:', error);
          this.errorMessage = 'Failed to delete role. Please try again.';
          this.isLoading = false;
        },
      });
  }

  /**
   * Check if the current user can edit a role
   * @param role Role to check
   * @returns True if the user can edit the role, false otherwise
   */
  canEditRole(role?: any): boolean {
    if (!role) return false;

    // Predefined roles can only be edited by superusers
    if (this.isPredefinedRole(role.role) && !this.isSuperUser) return false;

    // Superusers can edit any role
    if (this.isSuperUser) return true;

    // Organization admins can only edit custom roles, not predefined ones
    if (this.isOrgAdmin && !this.isPredefinedRole(role.role)) {
      return true;
    }

    return false;
  }

  /**
   * Check if the current user can delete a role
   * @param role Role to check
   * @returns True if the user can delete the role, false otherwise
   */
  canDeleteRole(role?: any): boolean {
    if (!role) return false;

    // Predefined roles cannot be deleted by anyone
    if (this.isPredefinedRole(role.role)) return false;

    // Superusers can delete any custom role
    if (this.isSuperUser) return true;

    // Organization admins can delete custom roles they created
    if (this.isOrgAdmin && !this.isPredefinedRole(role.role)) {
      return true;
    }

    return false;
  }

  activateUser(userId: string) {
    if (!userId) {
      this.errorMessage = 'No user ID provided';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    this.userManagementService.activateUser(userId).subscribe({
      next: () => {
        this.successMessage = 'User activated successfully';
        this.loadUsers(); // Reload the users list
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error activating user:', error);
        this.errorMessage = 'Failed to activate user. Please try again.';
        this.isLoading = false;
      },
    });
  }

  deactivateUser(userId: string) {
    if (!userId) {
      this.errorMessage = 'No user ID provided';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    this.userManagementService.deactivateUser(userId).subscribe({
      next: () => {
        this.successMessage = 'User deactivated successfully';
        this.loadUsers(); // Reload the users list
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error deactivating user:', error);
        this.errorMessage = 'Failed to deactivate user. Please try again.';
        this.isLoading = false;
      },
    });
  }

  changeUserRole(userId: string, roleId: string) {
    if (!userId || !roleId) {
      this.errorMessage = 'User ID and role ID are required';
      return;
    }

    if (!this.selectedOrgId) {
      this.errorMessage = 'No organization selected';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    this.userManagementService
      .changeUserRole(this.selectedOrgId, userId, roleId)
      .subscribe({
        next: (response) => {
          console.log('User role update response:', response);
          this.successMessage = 'User role updated successfully';
          this.loadUsers(); // Reload the users list
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error changing user role:', error);
          this.errorMessage = 'Failed to change user role. Please try again.';
          this.isLoading = false;
        },
      });
  }

  selectOrganization(orgId: string, orgName: string) {
    this.selectedOrgId = orgId;
    this.selectedOrgName = orgName;
    this.loadUsers();
    this.loadRoles();

    // Switch to the user tab to show users of the selected organization
    this.switchTab('user');
  }

  switchTab(tab: string) {
    // Only super users can access the organization tab
    if (tab === 'organization' && !this.isSuperUser) {
      this.errorMessage =
        'Only super users can access the organization management.';
      return;
    }

    // If trying to switch to user or role tab without an organization selected, stay on organization tab
    if ((tab === 'user' || tab === 'role') && !this.selectedOrgId) {
      this.errorMessage = 'Please select an organization first.';

      // If user is not a super user, try to auto-select their organization
      if (!this.isSuperUser && this.userOrgId) {
        const org = this.organizations.find((o) => o.id === this.userOrgId);
        if (org) {
          this.selectedOrgId = org.id;
          this.selectedOrgName = org.name;
          this.loadUsers();
          this.loadRoles();
          this.activeTab = tab;
          return;
        }
      } else {
        this.activeTab = 'organization';
        return;
      }
    }

    this.activeTab = tab;

    // Reset pagination when switching tabs
    this.currentPage = 1;

    // Clear messages
    this.errorMessage = '';
    this.successMessage = '';

    // Update total entries based on the active tab
    if (tab === 'user') {
      this.totalEntries = this.users.length;
    } else if (tab === 'role') {
      this.totalEntries = this.roles.length;
    } else if (tab === 'organization') {
      this.totalEntries = this.organizations.length;
    }
  }

  /**
   * Update user role names based on role IDs
   * This is called after roles are loaded to ensure users display the correct role names
   */
  updateUserRoleNames() {
    this.users.forEach((user) => {
      if (
        user.roleId &&
        (!user.role || user.role === 'No Role' || user.role === 'Unknown Role')
      ) {
        user.role = this.getRoleNameById(user.roleId);
      }
    });
    console.log('Updated user roles with names from role mapping');
  }

  loadUsers() {
    if (!this.selectedOrgId && this.organizations.length > 0) {
      // If no organization is selected but we have organizations, use the first one
      this.selectedOrgId = this.organizations[0]?.id;
      this.selectedOrgName = this.organizations[0]?.name || '';
    }

    if (!this.selectedOrgId) {
      // If still no organization ID, we can't load users yet
      console.log('No organization selected, cannot load users');
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    this.userManagementService
      .listOrganizationUsers(this.selectedOrgId)
      .subscribe({
        next: (data) => {
          // Map API response to our users array
          this.users = data.map((user: any) => {
            // Check if we have role_id but no role name
            let roleName = user.role?.name || user.role?.role;
            const roleId = user.role_id || user.role?.id;

            // If we have a role ID but no role name, try to get it from our mapping
            if (roleId && !roleName && this.roleIdToNameMap.size > 0) {
              roleName = this.getRoleNameById(roleId);
            }

            return {
              id: user.id,
              displayName: user.full_name,
              email: user.email,
              role: roleName || 'No Role',
              roleId: roleId,
              isActive: user.is_active,
            };
          });

          // If we have roles loaded but some users still don't have role names, update them
          if (this.roleIdToNameMap.size > 0) {
            this.updateUserRoleNames();
          }

          this.totalEntries = this.users.length;
          this.isLoading = false;

          // Update pagination if we're on the user tab
          if (this.activeTab === 'user') {
            this.currentPage = 1;
          }

          console.log('Loaded users with roles:', this.users);
        },
        error: (error) => {
          console.error('Error loading users:', error);
          this.errorMessage = 'Failed to load users. Please try again.';
          this.isLoading = false;

          // Fallback to sample data if API fails
          this.users = [
            {
              id: '1',
              displayName: 'Nipun',
              email: '<EMAIL>',
              role: 'Admin',
              roleId: '1',
              isActive: true,
            },
            {
              id: '2',
              displayName: 'Sahil',
              email: '<EMAIL>',
              role: 'Tester',
              roleId: '3',
              isActive: true,
            },
            {
              id: '3',
              displayName: 'Harsh',
              email: '<EMAIL>',
              role: 'Tester',
              roleId: '3',
              isActive: false,
            },
            {
              id: '4',
              displayName: 'Jagu',
              email: '<EMAIL>',
              role: 'Developer',
              roleId: '2',
              isActive: true,
            },
            {
              id: '5',
              displayName: 'Karan',
              email: '<EMAIL>',
              role: 'Developer',
              roleId: '2',
              isActive: true,
            },
            {
              id: '6',
              displayName: 'Rohan',
              email: '<EMAIL>',
              role: 'Developer',
              roleId: '2',
              isActive: true,
            },
          ];
          this.totalEntries = this.users.length;
        },
      });
  }

  checkSuperUserExists() {
    this.isLoading = true;
    this.errorMessage = '';

    // Use the dedicated endpoint to check if a super user exists
    this.userManagementService.checkSuperUserExists().subscribe({
      next: (response) => {
        this.superUserExists = response.exists;
        this.isLoading = false;
        console.log('Super user exists check:', this.superUserExists);
      },
      error: (error) => {
        console.error('Error checking if super user exists:', error);
        // Default to false if there's an error
        this.superUserExists = false;
        this.isLoading = false;
      },
    });
  }

  loadOrganizations() {
    this.isLoading = true;
    this.errorMessage = '';

    this.userManagementService.listOrganizations().subscribe({
      next: (data) => {
        // Map API response to our Organization interface
        let orgs = data.map((org: any) => ({
          id: org.id,
          name: org.name,
          email: org.email || 'N/A',
          domain: org.domain,
          type: org.type || 'Enterprise',
          status: org.status || 'Active',
          description: org.description,
          website: org.website,
          address: org.address,
        }));

        // Filter organizations based on user permissions
        if (!this.isSuperUser) {
          // Regular users and organization admins can only see their own organization
          if (this.userOrgId) {
            orgs = orgs.filter((org) => org.id === this.userOrgId);
          }
        }

        this.organizations = orgs;
        this.totalEntries = this.organizations.length;
        this.isLoading = false;

        // Update pagination if we're on the organization tab
        if (this.activeTab === 'organization') {
          this.currentPage = 1;
        }

        // If we have organizations, select the first one and load its users
        if (this.organizations.length > 0 && !this.selectedOrgId) {
          this.selectedOrgId = this.organizations[0]?.id;
          this.selectedOrgName = this.organizations[0]?.name || '';

          // For non-super users, automatically go to the user tab
          if (!this.isSuperUser) {
            this.activeTab = 'user';
          }

          this.loadUsers();
          this.loadRoles();
        }
      },
      error: (error) => {
        console.error('Error loading organizations:', error);
        this.errorMessage = 'Failed to load organizations. Please try again.';
        this.isLoading = false;

        // Fallback to sample data if API fails
        let orgs = [
          {
            id: '1',
            name: 'RevUp AI',
            email: '<EMAIL>',
            type: 'Enterprise',
            status: 'Active',
          },
          {
            id: '2',
            name: 'Tech Solutions',
            email: '<EMAIL>',
            type: 'SMB',
            status: 'Active',
          },
          {
            id: '3',
            name: 'Global Innovations',
            email: '<EMAIL>',
            type: 'Enterprise',
            status: 'Inactive',
          },
          {
            id: '4',
            name: 'Digital Startups',
            email: '<EMAIL>',
            type: 'Startup',
            status: 'Active',
          },
          {
            id: '5',
            name: 'Future Systems',
            email: '<EMAIL>',
            type: 'SMB',
            status: 'Pending',
          },
        ];

        // Filter organizations based on user permissions
        if (!this.isSuperUser) {
          // For testing, assume the user belongs to the first organization
          this.userOrgId = this.userOrgId || '1';
          orgs = orgs.filter((org) => org.id === this.userOrgId);
        }

        this.organizations = orgs;
        this.totalEntries = this.organizations.length;

        // Select the first organization from the fallback data
        if (this.organizations.length > 0 && !this.selectedOrgId) {
          this.selectedOrgId = this.organizations[0]?.id;
          this.selectedOrgName = this.organizations[0]?.name || '';
        }
      },
    });
  }

  createOrganization() {
    // Check if user has permission to create organizations
    if (!this.canCreateOrganization()) {
      this.errorMessage = 'You do not have permission to create organizations.';
      return;
    }

    if (this.organizationForm.invalid) {
      this.markFormGroupTouched(this.organizationForm);
      return;
    }

    this.isLoading = true;
    const formData = this.organizationForm.value;

    // Prepare data for API
    const organizationData = {
      name: formData.name,
      domain: formData.domain || formData.email.split('@')[1], // Use email domain if not provided
      email: formData.email,
      type: formData.type,
      status: formData.status,
      website: formData.website,
      address: formData.address,
      description: formData.description,
    };

    this.userManagementService
      .createOrganization({
        name: organizationData.name,
        domain: organizationData.domain,
      })
      .subscribe({
        next: () => {
          this.successMessage = 'Organization created successfully!';
          this.loadOrganizations();
          this.resetOrgForm();
          this.isLoading = false;
          // Close modal
          const modalCheckbox = document.getElementById(
            'AddOrganizationModal'
          ) as HTMLInputElement;
          if (modalCheckbox) {
            modalCheckbox.checked = false;
          }
        },
        error: (error) => {
          console.error('Error creating organization:', error);
          this.errorMessage =
            'Failed to create organization. Please try again.';
          this.isLoading = false;
        },
      });
  }

  resetForm() {
    this.organizationForm.reset({
      type: 'Enterprise',
      status: 'Active',
      send_welcome_email: false,
    });
    this.errorMessage = '';
    this.successMessage = '';
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if ((control as any).controls) {
        this.markFormGroupTouched(control as FormGroup);
      }
    });
  }

  // Users data - will be populated from API
  users: any[] = [];
  // Roles data - will be populated from API
  roles: any[] = [];

  // Selected user for editing
  selectedUser: any = null;

  // Selected role for editing
  selectedRole: any = null;

  // Selected organization for editing
  selectedOrg: Organization | null = null;

  // Organizations data - will be populated from API
  organizations: Organization[] = [];

  // Pagination variables
  pageSize = 5;
  currentPage = 1;
  totalEntries = 0;
  pageSizeOptions = [3, 5, 10];

  get paginatedUsers() {
    const start = (this.currentPage - 1) * this.pageSize;
    return this.users.slice(start, start + this.pageSize);
  }

  // Get paginated roles
  get paginatedRoles() {
    const start = (this.currentPage - 1) * this.pageSize;
    return this.roles.slice(start, start + this.pageSize);
  }

  // Get paginated organizations
  get paginatedOrganizations() {
    const start = (this.currentPage - 1) * this.pageSize;
    return this.organizations.slice(start, start + this.pageSize);
  }

  get totalPages() {
    return Math.ceil(this.totalEntries / this.pageSize);
  }

  get currentPageStart() {
    return (this.currentPage - 1) * this.pageSize + 1;
  }

  get currentPageEnd() {
    return Math.min(this.currentPage * this.pageSize, this.totalEntries);
  }

  prevPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  changePageSize(event: Event) {
    const select = event.target as HTMLSelectElement;
    this.pageSize = parseInt(select.value, 10);
    this.currentPage = 1; // Reset to first page when changing page size
  }

  // User Type Selection and Modal Control Functions

  onOrganizationChange(event: Event) {
    const select = event.target as HTMLSelectElement;
    const orgId = select.value;

    if (orgId) {
      this.selectedOrgId = orgId;

      // Find the organization name
      const org = this.organizations.find((o) => o.id === orgId);
      if (org) {
        this.selectedOrgName = org.name;
      }

      // Load roles for the selected organization
      this.loadRoles();
    }
  }

  openSelectUserTypeModal() {
    // Check if user has permission to create users or admins
    if (!this.canCreateUsers() && !this.canCreateAdmins()) {
      this.errorMessage =
        'You do not have permission to create users or admins.';
      return;
    }

    this.errorMessage = '';
    this.successMessage = '';

    // Open the modal
    const modalCheckbox = document.getElementById(
      'SelectUserTypeModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  openAddAdminModal() {
    // Check if user has permission to create admins
    if (!this.canCreateAdmins()) {
      this.errorMessage =
        'You do not have permission to create organization admins.';
      return;
    }

    if (!this.selectedOrgId) {
      this.errorMessage = 'Please select an organization first.';
      return;
    }

    this.resetAdminForm();
    this.errorMessage = '';
    this.successMessage = '';

    // Close the selection modal
    const selectionModal = document.getElementById(
      'SelectUserTypeModal'
    ) as HTMLInputElement;
    if (selectionModal) {
      selectionModal.checked = false;
    }

    // Open the admin modal
    const modalCheckbox = document.getElementById(
      'AddAdminModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  openAddUserModal() {
    // Check if user has permission to create users
    if (!this.canCreateUsers()) {
      this.errorMessage = 'You do not have permission to create users.';
      return;
    }

    if (!this.selectedOrgId) {
      this.errorMessage = 'Please select an organization first.';
      return;
    }

    this.resetUserForm();
    this.errorMessage = '';
    this.successMessage = '';

    // Close the selection modal
    const selectionModal = document.getElementById(
      'SelectUserTypeModal'
    ) as HTMLInputElement;
    if (selectionModal) {
      selectionModal.checked = false;
    }

    // Open the user modal
    const modalCheckbox = document.getElementById(
      'AddUserModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  openEditUserModal(user: any) {
    // Check if user has permission to edit users
    if (!this.canCreateUsers()) {
      this.errorMessage = 'You do not have permission to edit users.';
      return;
    }

    if (!this.selectedOrgId) {
      this.errorMessage = 'Please select an organization first.';
      return;
    }

    this.selectedUser = user;
    this.errorMessage = '';
    this.successMessage = '';

    // Populate the form with user data
    this.userForm.patchValue({
      full_name: user.displayName,
      email: user.email,
      role_id: user.roleId || '',
      // Don't set password fields when editing
      password: '',
      confirm_password: '',
      send_welcome_email: false,
    });

    // Open the edit user modal
    const modalCheckbox = document.getElementById(
      'EditUserModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  toggleUserStatus(user: any) {
    // Check if user has permission to edit users
    if (!this.canCreateUsers()) {
      this.errorMessage = 'You do not have permission to modify users.';
      return;
    }

    if (!this.selectedOrgId) {
      this.errorMessage = 'Please select an organization first.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const newStatus = !user.isActive;
    const action = newStatus ? 'activate' : 'deactivate';

    this.userManagementService
      .updateUserStatus(this.selectedOrgId, user.id, newStatus)
      .subscribe({
        next: () => {
          this.successMessage = `User ${action}d successfully!`;
          // Update the user status in the local array
          user.isActive = newStatus;
          this.isLoading = false;
        },
        error: (error) => {
          console.error(`Error ${action}ing user:`, error);
          this.errorMessage = `Failed to ${action} user. Please try again.`;
          this.isLoading = false;
        },
      });
  }

  updateUser() {
    // Check if user has permission to edit users
    if (!this.canCreateUsers()) {
      this.errorMessage = 'You do not have permission to edit users.';
      return;
    }

    if (!this.selectedOrgId) {
      this.errorMessage = 'Please select an organization first.';
      return;
    }

    if (!this.selectedUser) {
      this.errorMessage = 'No user selected for editing.';
      return;
    }

    if (this.userForm.invalid) {
      this.markFormGroupTouched(this.userForm);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const formData = this.userForm.value;

    // Prepare data for API
    const userData = {
      email: formData.email,
      full_name: formData.full_name,
      role_id: formData.role_id,
    };

    console.log('Updating user with data:', userData);

    this.userManagementService
      .updateUser(this.selectedOrgId, this.selectedUser.id, userData)
      .subscribe({
        next: () => {
          this.successMessage = 'User updated successfully!';
          this.loadUsers(); // Reload the users list
          this.isLoading = false;

          // Close modal
          const modalCheckbox = document.getElementById(
            'EditUserModal'
          ) as HTMLInputElement;
          if (modalCheckbox) {
            modalCheckbox.checked = false;
          }
        },
        error: (error) => {
          console.error('Error updating user:', error);
          this.errorMessage = 'Failed to update user. Please try again.';
          this.isLoading = false;
        },
      });
  }

  createAdmin() {
    // Check if user has permission to create admins
    if (!this.canCreateAdmins()) {
      this.errorMessage =
        'You do not have permission to create organization admins.';
      return;
    }

    if (this.adminForm.invalid) {
      this.markFormGroupTouched(this.adminForm);
      return;
    }

    if (!this.selectedOrgId) {
      this.errorMessage =
        'No organization selected. Please select an organization first.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const formData = this.adminForm.value;

    // Prepare data for API
    const adminData = {
      email: formData.email,
      full_name: formData.full_name,
      password: formData.password,
      send_welcome_email: formData.send_welcome_email,
    };

    console.log('Creating admin with data:', {
      ...adminData,
      password: '********',
      organization_id: this.selectedOrgId,
    });

    this.userManagementService
      .createOrganizationAdmin(this.selectedOrgId, adminData)
      .subscribe({
        next: () => {
          this.successMessage = 'Admin created successfully!';
          this.resetAdminForm();
          this.loadUsers(); // Reload the users list
          this.isLoading = false;

          // Close modal
          const modalCheckbox = document.getElementById(
            'AddAdminModal'
          ) as HTMLInputElement;
          if (modalCheckbox) {
            modalCheckbox.checked = false;
          }
        },
        error: (error) => {
          console.error('Error creating admin:', error);

          let detailedError = '';
          if (error.error) {
            if (typeof error.error === 'string') {
              detailedError = error.error;
            } else if (error.error.detail) {
              detailedError = error.error.detail;
            } else if (error.error.message) {
              detailedError = error.error.message;
            }
          }

          if (detailedError) {
            this.errorMessage = `Failed to create admin: ${detailedError}`;
          } else {
            this.errorMessage = `Failed to create admin. Server returned status ${error.status}. Please try again.`;
          }

          this.isLoading = false;
        },
      });
  }

  // Edit functions

  openChangeRoleModal(user: any) {
    this.selectedUser = user;
    this.errorMessage = '';
    this.successMessage = '';

    // Open the modal
    const modalCheckbox = document.getElementById(
      'ChangeRoleModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  updateUserRole() {
    if (!this.selectedUser || !this.selectedUser.id) {
      this.errorMessage = 'No user selected';
      return;
    }

    const roleId = (document.getElementById('roleSelect') as HTMLSelectElement)
      ?.value;

    if (!roleId) {
      this.errorMessage = 'Please select a role';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.changeUserRole(this.selectedUser.id, roleId);

    // Close the modal
    const modalCheckbox = document.getElementById(
      'ChangeRoleModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = false;
    }
  }

  /**
   * Open the add role modal
   */
  openAddRoleModal() {
    // Check if user has permission to create roles
    if (!this.canCreateRoles()) {
      this.errorMessage = 'You do not have permission to create roles.';
      return;
    }

    if (!this.selectedOrgId) {
      this.errorMessage = 'Please select an organization first.';
      return;
    }

    // Reset the form and clear the selected role
    this.resetRoleForm();
    this.selectedRole = null; // Important: clear the selected role when creating a new one
    this.errorMessage = '';
    this.successMessage = '';

    // Make sure selectedPrivileges is initialized as an empty array
    this.selectedPrivileges = [];
    console.log(
      'Opening Add Role modal, selectedPrivileges reset to:',
      this.selectedPrivileges
    );

    // Open the modal
    const modalCheckbox = document.getElementById(
      'AddRoleModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  openEditRoleModal(role: any) {
    this.selectedRole = role;
    this.errorMessage = '';
    this.successMessage = '';

    // Reset the role form
    this.roleForm.reset();

    // Enable or disable form controls based on permissions
    if (this.canEditRole(role)) {
      this.roleForm.get('name')?.enable();
      this.roleForm.get('description')?.enable();
      this.roleForm.get('is_sharable')?.enable();
    } else {
      this.roleForm.get('name')?.disable();
      this.roleForm.get('description')?.disable();
      this.roleForm.get('is_sharable')?.disable();
    }

    // Set the form values based on the selected role
    this.roleForm.patchValue({
      name: role.role,
      description: role.description || '',
      is_sharable: role.is_sharable,
    });

    // Set the selected privileges
    if (role.permissions) {
      this.selectedPrivileges = [];
      console.log(
        'Setting selected privileges from role permissions:',
        role.permissions
      );

      // Get the privileges from the observable - use take(1) to avoid memory leaks
      this.privileges$.pipe(take(1)).subscribe((privileges) => {
        console.log('Available privileges:', privileges);

        // Handle both array and object formats for permissions
        if (Array.isArray(role.permissions)) {
          // For array format
          role.permissions.forEach((permissionName: string) => {
            const formattedName = permissionName.replace(/_/g, ' ');
            const privilege = privileges.find(
              (p) => p.name.toLowerCase() === formattedName.toLowerCase()
            );
            if (privilege) {
              this.selectedPrivileges.push(privilege.id);
            }
          });
        } else {
          // For object format
          Object.entries(role.permissions).forEach(([key, value]) => {
            if (value) {
              const permissionName = key.replace(/_/g, ' ');
              const privilege = privileges.find(
                (p) => p.name.toLowerCase() === permissionName.toLowerCase()
              );
              if (privilege) {
                this.selectedPrivileges.push(privilege.id);
              }
            }
          });
        }

        console.log(
          'Selected privileges after mapping:',
          this.selectedPrivileges
        );
      });
    }

    // Open the modal
    const modalCheckbox = document.getElementById(
      'EditRoleModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  togglePrivilege(privilegeId: number, event: Event) {
    // For the Add Role modal, we don't have a selectedRole yet, so we should allow toggling
    // For the Edit Role modal, only allow toggling if the user can edit the role
    const isAddRoleModal = !this.selectedRole;

    if (!isAddRoleModal && !this.canEditRole(this.selectedRole)) {
      event.preventDefault();
      return;
    }

    const checkbox = event.target as HTMLInputElement;
    console.log(`Toggle privilege ${privilegeId} to ${checkbox.checked}`);

    if (checkbox.checked) {
      // Only add if not already in the array
      if (!this.selectedPrivileges.includes(privilegeId)) {
        this.selectedPrivileges.push(privilegeId);
        console.log(
          `Added privilege ${privilegeId}, now selected: ${this.selectedPrivileges}`
        );
      }
    } else {
      const index = this.selectedPrivileges.indexOf(privilegeId);
      if (index !== -1) {
        this.selectedPrivileges.splice(index, 1);
        console.log(
          `Removed privilege ${privilegeId}, now selected: ${this.selectedPrivileges}`
        );
      }
    }
  }

  updateRole() {
    if (this.roleForm.invalid) {
      this.markFormGroupTouched(this.roleForm);
      return;
    }

    if (!this.selectedOrgId) {
      this.errorMessage =
        'No organization selected. Please select an organization first.';
      return;
    }

    if (!this.selectedRole || !this.selectedRole.id) {
      this.errorMessage = 'No role selected';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const formData = this.roleForm.value;

    // Create a promise to ensure we wait for the privileges to be processed
    const getPermissionsPromise = new Promise<Record<string, boolean>>(
      (resolve) => {
        // Convert selected privileges to permissions object
        const permissions: Record<string, boolean> = {};

        if (this.privileges$ && this.selectedPrivileges.length > 0) {
          // Get the actual privilege names from the privileges$ observable
          this.privileges$.pipe(take(1)).subscribe((privileges) => {
            this.selectedPrivileges.forEach((id) => {
              const privilege = privileges.find((p) => p.id === id);
              if (privilege) {
                // Convert spaces to underscores for API format
                const permissionKey = privilege.name.replace(/ /g, '_');
                permissions[permissionKey] = true;
              }
            });
            resolve(permissions);
          });
        } else {
          resolve(permissions);
        }
      }
    );

    // Wait for permissions to be processed, then send the API request
    getPermissionsPromise.then((permissions) => {
      // Prepare data for API
      const roleData = {
        name: formData.name,
        description: formData.description,
        is_sharable: formData.is_sharable,
        permissions: permissions,
      };

      console.log('Updating role with data:', roleData);

      if (!this.selectedOrgId || !this.selectedRole || !this.selectedRole.id) {
        this.errorMessage = 'Missing organization or role information.';
        this.isLoading = false;
        return;
      }

      this.userManagementService
        .updateRolePermissions(
          this.selectedOrgId,
          this.selectedRole.id,
          roleData
        )
        .subscribe({
          next: (updatedRole) => {
            console.log('Role update response:', updatedRole);
            this.successMessage = 'Role updated successfully!';

            // Update the role in our local array instead of reloading
            if (this.selectedRole && this.selectedRole.id) {
              const roleIndex = this.roles.findIndex(
                (r) => r.id === this.selectedRole.id
              );
              if (roleIndex !== -1) {
                // Update the role with the new data
                this.roles[roleIndex] = {
                  ...this.roles[roleIndex],
                  role: formData.name,
                  description: formData.description,
                  is_sharable: formData.is_sharable,
                  privileges: this.formatPermissions(permissions),
                  permissions: permissions,
                };

                // Update the role ID to name mapping if name changed
                if (this.roles[roleIndex].role !== formData.name) {
                  this.roleIdToNameMap.set(this.selectedRole.id, formData.name);
                }

                console.log(
                  'Updated role in local array:',
                  this.roles[roleIndex]
                );
              } else {
                // If we can't find the role, reload the list
                console.log('Could not find role in local array, reloading');
                this.loadRoles();
              }
            } else {
              // Fallback to reloading if something went wrong
              this.loadRoles();
            }

            this.isLoading = false;

            // Close modal
            const modalCheckbox = document.getElementById(
              'EditRoleModal'
            ) as HTMLInputElement;
            if (modalCheckbox) {
              modalCheckbox.checked = false;
            }
          },
          error: (error) => {
            console.error('Error updating role:', error);

            let detailedError = '';
            if (error.error) {
              if (typeof error.error === 'string') {
                detailedError = error.error;
              } else if (error.error.detail) {
                detailedError = error.error.detail;
              } else if (error.error.message) {
                detailedError = error.error.message;
              }
            }

            if (detailedError) {
              this.errorMessage = `Failed to update role: ${detailedError}`;
            } else {
              this.errorMessage = `Failed to update role. Server returned status ${error.status}. Please try again.`;
            }

            this.isLoading = false;
          },
        });
    });
  }

  openEditOrgModal(org: Organization) {
    this.selectedOrg = org;
    this.errorMessage = '';
    this.successMessage = '';

    // Reset the organization form
    this.organizationForm.reset();

    // Set the form values based on the selected organization
    this.organizationForm.patchValue({
      name: org.name,
      email: org.email,
      domain: org.domain || '',
      type: org.type,
      status: org.status,
      website: org.website || '',
      address: org.address || '',
      description: org.description || '',
      send_welcome_email: false,
    });

    // Open the modal
    const modalCheckbox = document.getElementById(
      'EditOrganizationModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  updateOrganization() {
    if (this.organizationForm.invalid) {
      this.markFormGroupTouched(this.organizationForm);
      return;
    }

    if (!this.selectedOrg || !this.selectedOrg.id) {
      this.errorMessage = 'No organization selected';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const formData = this.organizationForm.value;

    // Prepare data for API
    const organizationData = {
      name: formData.name,
      domain: formData.domain || formData.email.split('@')[1], // Use email domain if not provided
      email: formData.email,
      type: formData.type,
      status: formData.status,
      website: formData.website,
      address: formData.address,
      description: formData.description,
    };

    console.log('Updating organization with data:', organizationData);

    this.userManagementService
      .updateOrganization(this.selectedOrg.id, organizationData)
      .subscribe({
        next: () => {
          this.successMessage = 'Organization updated successfully!';
          this.loadOrganizations(); // Reload the organizations list
          this.isLoading = false;

          // Close modal
          const modalCheckbox = document.getElementById(
            'EditOrganizationModal'
          ) as HTMLInputElement;
          if (modalCheckbox) {
            modalCheckbox.checked = false;
          }
        },
        error: (error) => {
          console.error('Error updating organization:', error);

          let detailedError = '';
          if (error.error) {
            if (typeof error.error === 'string') {
              detailedError = error.error;
            } else if (error.error.detail) {
              detailedError = error.error.detail;
            } else if (error.error.message) {
              detailedError = error.error.message;
            }
          }

          if (detailedError) {
            this.errorMessage = `Failed to update organization: ${detailedError}`;
          } else {
            this.errorMessage = `Failed to update organization. Server returned status ${error.status}. Please try again.`;
          }

          this.isLoading = false;
        },
      });
  }
}

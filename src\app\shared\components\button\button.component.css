/* Button Component Styles */

button {
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
  transition: all 0.2s ease-in-out !important;
}

button:not([disabled]):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

button:not([disabled]):active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Ripple effect */
button::after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 10%,
    transparent 10.01%
  );
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.5s, opacity 1s;
}

button:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

/* Loading animation */
.btn-loading::before {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Subtle hover glow for primary buttons */
.btn-primary:not([disabled]):hover {
  box-shadow: 0 0 15px rgba(148, 0, 255, 0.3);
}

.btn-secondary:not([disabled]):hover {
  box-shadow: 0 0 15px rgba(39, 0, 93, 0.3);
}

.btn-success:not([disabled]):hover {
  box-shadow: 0 0 15px rgba(44, 206, 138, 0.3);
}

.btn-error:not([disabled]):hover {
  box-shadow: 0 0 15px rgba(245, 54, 91, 0.3);
}

.btn-warning:not([disabled]):hover {
  box-shadow: 0 0 15px rgba(251, 99, 64, 0.3);
}

/* Disabled state */
button[disabled] {
  cursor: not-allowed;
  opacity: 0.6;
}

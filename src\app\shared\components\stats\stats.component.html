<!-- Show header with content when onlyHeader is false -->
<div
  class="bg-backgroundPrimary border rounded-md w-full transition-all hover-shadow"
  [ngClass]="{ 'border-backgroundSecondary': getTheme() === 'dark' }"
  *ngIf="!onlyHeader"
  [@fadeIn]
  [attr.data-theme]="getTheme()"
>
  <!-- Header Section -->
  <div class="flex justify-between items-center p-3 px-3">
    <div class="flex items-center gap-3">
      <img
        *ngIf="isImage(icon)"
        [src]="icon"
        alt="Icon"
        class="transition-all h-8 w-8"
      />

      <div
        [ngStyle]="{ 'background-color': getLighterShade() }"
        class="py-2 px-3 rounded-md transition-all"
      >
        <i
          *ngIf="icon && !isImage(icon)"
          [ngClass]="[icon, iconSize]"
          [class]="getIconColorClass()"
          class="transition-all"
        ></i>
      </div>
      <h3 class="text-base font-medium text-content1">{{ title }}</h3>
    </div>
    <span
      *ngIf="badge !== null && badge !== undefined"
      class="rounded-md text-base font-bold badge badge-flat-secondary"
      [@badgeAnimation]="badge"
    >
      {{ badge }}
    </span>
  </div>

  <!-- Dynamic Content Section -->
  <div
    class="border-t mt-1 p-3 text-content2"
    [ngClass]="{ 'border-backgroundSecondary': getTheme() === 'dark' }"
    #contentContainer
  >
    <ng-content></ng-content>
  </div>
</div>

<!-- Show only header when onlyHeader is true -->
<div
  class="bg-backgroundPrimary border rounded-md w-full transition-all hover-shadow"
  [ngClass]="{ 'border-backgroundSecondary': getTheme() === 'dark' }"
  *ngIf="onlyHeader"
  [@fadeIn]
  [attr.data-theme]="getTheme()"
>
  <!-- Header Section -->
  <div class="flex justify-between items-center p-3 px-3">
    <div class="flex items-center gap-3">
      <img
        *ngIf="isImage(icon)"
        [src]="icon"
        alt="Icon"
        class="transition-all h-8 w-8"
      />
      <div
        [ngStyle]="{ 'background-color': getLighterShade() }"
        class="py-2 px-3 rounded-md transition-all"
      >
        <i
          *ngIf="icon && !isImage(icon)"
          [ngClass]="[icon, iconSize]"
          [class]="getIconColorClass()"
          class="transition-all"
        ></i>
      </div>
      <h3 class="text-base font-medium text-content1">{{ title }}</h3>
    </div>
    <span
      *ngIf="badge !== null && badge !== undefined"
      class="rounded-md text-base font-bold badge badge-flat-secondary"
      [@badgeAnimation]="badge"
    >
      {{ badge }}
    </span>
  </div>
</div>

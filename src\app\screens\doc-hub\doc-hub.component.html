<!-- No access message -->
<div
  *ngIf="permissionsLoaded && !hasDocumentAccess"
  class="flex flex-col items-center justify-center min-h-[70vh]"
>
  <div
    class="bg-backgroundPrimary p-8 rounded-lg shadow-md max-w-md text-center"
  >
    <div
      class="rounded-full bg-backgroundSecondary p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center"
    >
      <i class="ti ti-lock text-4xl text-warning"></i>
    </div>
    <h2 class="text-xl font-semibold text-content1 mb-2">Access Restricted</h2>
    <p class="text-content2 mb-6">
      You don't have permission to access the Document Hub. Please contact your
      administrator to update your role permissions.
    </p>
    <button class="btn btn-primary" (click)="goBack()">
      <i class="ti ti-arrow-left mr-2"></i> Go Back
    </button>
  </div>
</div>

<!-- Main content - only show if user has access -->
<div *ngIf="permissionsLoaded && hasDocumentAccess">
  <!-- Permission info for download - only show after permissions are loaded -->
  <div *ngIf="!canDownloadDocuments" class="alert alert-info mb-4 shadow-sm">
    <i class="ti ti-info-circle text-xl"></i>
    <span
      >You can view documents but don't have permission to download them.</span
    >
  </div>

  <div class="flex items-center justify-between mb-4">
    <div class="form-control w-1/4 max-w-md">
      <select
        class="select select-bordered w-full"
        [(ngModel)]="selectedType"
        style="border-radius: 0.35rem !important"
      >
        <option value="">All Types</option>
        <option *ngFor="let type of types" [value]="type">{{ type }}</option>
      </select>
    </div>
    <div class="flex gap-2">
      <div class="form-control relative max-w-md">
        <input
          type="text"
          placeholder="Search..."
          class="input input-bordered w-full pl-10"
          [(ngModel)]="searchQuery"
        />
        <span class="absolute inset-y-0 left-3 flex items-center text-gray-500">
          <i class="ti ti-search"></i>
        </span>
      </div>
    </div>
  </div>

  <!-- Loading state -->
  <div *ngIf="loading" class="flex justify-center my-8">
    <div
      class="absolute animate-spin rounded-full h-8 w-8 border-b-2 border-primary top-[50%]"
    ></div>
  </div>

  <!-- Documents table -->
  <div
    *ngIf="!loading && !error"
    class="enhanced-table overflow-hidden rounded-lg border shadow-sm"
  >
    <table
      class="table table-compact table-hover w-full border-collapse custom-table border border-[var(--border-color)]"
    >
      <thead>
        <tr>
          <th
            class="text-left font-medium text-content1 border-r border-b border-[var(--border-color)]"
            style="width: 40%"
          >
            Object Name
          </th>
          <th
            class="text-left font-medium text-content1 border-r border-b border-[var(--border-color)]"
            style="width: 20%"
          >
            Object Type
          </th>
          <th
            class="text-left font-medium text-content1 border-r border-b border-[var(--border-color)]"
            style="width: 20%"
          >
            Database
          </th>
          <th
            class="text-left font-medium text-content1 border-b border-[var(--border-color)]"
            style="width: 20%"
          >
            Action
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="let item of paginatedTableData; let i = index"
          class="cursor-pointer border-b transition-all duration-200"
          [ngStyle]="{ 'animation-delay': i * 0.05 + 's' }"
          style="animation: fadeInRow 0.5s ease-out forwards"
        >
          <td class="border-r border-[var(--border-color)]">
            <div class="font-semibold text-content1 mb-1">
              {{ item.procedure_name }}
            </div>
          </td>
          <td class="text-content1 border-r border-[var(--border-color)]">
            {{ item.object_type }}
          </td>
          <td class="text-content1 border-r border-[var(--border-color)]">
            {{ item.database_name }}
          </td>
          <td class="text-content1">
            <!-- Show download button if user has permission -->
            <button
              *ngIf="permissionsLoaded && canDownloadDocuments"
              class="btn btn-outline btn-sm h-9 min-h-0 transition-all hover:shadow-md"
              (click)="downloadDocument(item); $event.stopPropagation()"
            >
              <i class="ti ti-download mr-1"></i> Download
            </button>

            <!-- Show message if user doesn't have permission (only after permissions are loaded) -->
            <span
              *ngIf="permissionsLoaded && !canDownloadDocuments"
              class="text-sm text-content2 italic"
            >
              No download permission
            </span>

            <!-- Show default button while permissions are loading -->
            <button
              *ngIf="!permissionsLoaded"
              class="btn btn-outline btn-sm h-9 min-h-0 transition-all hover:shadow-md"
              disabled
            >
              <i class="ti ti-download mr-1"></i> Download
            </button>
          </td>
        </tr>

        <!-- Empty state -->
        <tr *ngIf="paginatedTableData.length === 0">
          <td colspan="4" class="py-16 text-center">
            <div class="flex flex-col items-center justify-center gap-4">
              <div
                class="rounded-full bg-backgroundSecondary p-4 mb-2 shadow-md"
              >
                <i
                  class="ti ti-file-search text-4xl text-primary empty-state-icon"
                ></i>
              </div>
              <h3 class="text-xl font-medium text-content1">
                No documents found
              </h3>
              <p class="text-content2">
                {{
                  searchQuery
                    ? "No documents match your search criteria. Try adjusting your filters or search term."
                    : "There are no documents available yet."
                }}
              </p>
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Pagination controls -->
    <div
      *ngIf="totalEntries > 0"
      class="flex justify-between items-center bg-backgroundPrimary p-4 border-t border-[var(--border-color)]"
    >
      <div class="flex flex-row items-center space-x-4">
        <div
          class="flex items-center px-3 py-1.5 bg-backgroundPrimary rounded-md border border-[var(--border-color)]"
        >
          <i class="ti ti-list text-primary mr-2"></i>
          <h2 class="text-sm font-medium text-content2 whitespace-nowrap">
            Showing
            <span class="text-content1 font-semibold">{{
              currentPageStart
            }}</span>
            to
            <span class="text-content1 font-semibold">{{
              currentPageEnd
            }}</span>
            of
            <span class="text-content1 font-semibold">{{ totalEntries }}</span>
            documents
          </h2>
        </div>
        <div class="relative dropdown" style="width: 120px">
          <div
            class="flex items-center bg-backgroundPrimary rounded-md border border-[var(--border-color)]"
          >
            <select
              class="select select-sm w-full rounded-md bg-backgroundPrimary text-content1 border-0 pl-3 pr-8 transition-all focus:ring-2 focus:ring-primary/20"
              [(ngModel)]="pageSize"
              (change)="changePageSize($event)"
              style="
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
              "
            >
              <option *ngFor="let size of pageSizeOptions" [value]="size">
                {{ size }} per page
              </option>
            </select>
            <div
              class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-content2"
            >
              <i
                class="ti ti-chevron-down transition-transform text-primary"
              ></i>
            </div>
          </div>
        </div>
      </div>

      <!-- Updated pagination section -->
      <div class="pagination flex items-center">
        <div
          class="flex items-center rounded-md bg-backgroundPrimary border border-[var(--border-color)] p-0.5"
        >
          <button
            class="btn btn-sm h-8 min-h-0 btn-ghost flex items-center text-content2 px-2 transition-all hover:bg-primary/10 hover:text-primary rounded-l-md rounded-r-none border-r border-[var(--border-color)]"
            (click)="prevPage()"
            [disabled]="currentPage === 1"
          >
            <span class="flex items-center"
              ><i class="ti ti-chevron-left mr-1"></i>Prev</span
            >
          </button>

          <div class="px-3 flex items-center">
            <span class="text-content2"
              >Page {{ currentPage }} of {{ totalPages }}</span
            >
          </div>

          <button
            class="btn btn-sm h-8 min-h-0 btn-ghost flex items-center text-content2 px-2 transition-all hover:bg-primary/10 hover:text-primary rounded-r-md rounded-l-none border-l border-[var(--border-color)]"
            (click)="nextPage()"
            [disabled]="currentPage === totalPages"
          >
            <span class="flex items-center"
              >Next <i class="ti ti-chevron-right ml-1"></i
            ></span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Close enhanced-table div -->

/* Dependency Chart Component Styles */

.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spin 0.75s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Table Styles */
th {
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  background-color: rgba(148, 0, 255, 0.03);
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.08),
    rgba(44, 206, 138, 0.03)
  );
  padding: 0.75rem 1rem;
}

/* Dark mode table header */
[data-theme="dark"] th {
  background-color: rgba(148, 0, 255, 0.05);
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.12),
    rgba(44, 206, 138, 0.05)
  );
}

.table-hover tr:hover {
  background-color: rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

/* Selected row styling */
.selected-row {
  background-color: rgba(37, 99, 235, 0.1) !important;
  border-left: 3px solid #2563eb;
  font-weight: 500;
}

.bg-primary-50 {
  background-color: rgba(37, 99, 235, 0.1);
}

/* Hover effect for clickable rows */
.cursor-pointer {
  cursor: pointer;
}

.hover\:bg-gray-100:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s ease;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.btn-outline:hover {
  transform: translateY(-1px);
}

/* Pagination Styles */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination button {
  min-width: 24px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-active {
  background-color: #2563eb;
  color: white;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .flex-col {
    flex-direction: column;
  }

  .md\:w-\[40\%\],
  .md\:w-\[60\%\] {
    width: 100%;
  }

  /* Stack the layout on mobile */
  .flex.gap-2.h-\[calc\(100vh-150px\)\] {
    flex-direction: column;
    height: auto;
  }

  /* Make chart not sticky on mobile */
  .sticky.top-0.h-\[calc\(100vh-150px\)\] {
    position: relative;
    height: 500px; /* Fixed height on mobile */
  }

  /* Adjust top controls for mobile */
  .flex.pb-3.gap-2.justify-between.items-center {
    flex-direction: column;
    align-items: flex-start;
  }

  .flex.pb-3.gap-2.justify-between.items-center > div {
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 8px;
  }

  /* Make search input smaller on mobile */
  .form-control.relative.w-64 {
    width: 100%;
  }
}

/* Cursor styles */
.cursor-pointer {
  cursor: pointer;
}

/* Additional styles for filter controls */
.select {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid #d1d5db;
  background-color: white;
}

.select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.input {
  padding: 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid #d1d5db;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* Improved container styles */
.rounded-lg {
  border-radius: 0.5rem;
}

.border {
  border: 1px solid #e5e7eb;
}

.overflow-y-auto {
  overflow-y: auto;
}

.max-h-\[500px\] {
  max-height: 500px;
}

.min-h-\[600px\] {
  min-height: 600px;
}

/* Enhanced Markdown Styling for Summary */
:host ::ng-deep markdown {
  display: block;
}

/* Specific styling for summary markdown */
:host ::ng-deep .summary-markdown {
  padding: 1rem;
  background-color: var(--backgroundPrimary);
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
}

/* Headings */
:host ::ng-deep markdown h1,
:host ::ng-deep markdown h2,
:host ::ng-deep markdown h3,
:host ::ng-deep markdown h4,
:host ::ng-deep markdown h5,
:host ::ng-deep markdown h6 {
  font-weight: 600;
  line-height: 1.25;
  color: var(--content1);
}

:host ::ng-deep markdown h3 {
  font-size: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.3rem;
  color: var(--primary);
}

/* Paragraphs and lists */
:host ::ng-deep markdown p,
:host ::ng-deep markdown ul,
:host ::ng-deep markdown ol {
  margin-bottom: 1rem;
}

:host ::ng-deep markdown ul,
:host ::ng-deep markdown ol {
  padding-left: 1.5rem;
}

:host ::ng-deep markdown li {
  margin-bottom: 0.25rem;
}

:host ::ng-deep markdown ul li {
  list-style-type: disc;
}

/* Code blocks and inline code */
:host ::ng-deep markdown pre {
  background-color: var(--backgroundSecondary);
  border-radius: 0.375rem;
  padding: 1rem;
  overflow-x: auto;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
}

:host ::ng-deep markdown code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
  font-size: 1rem;
  font-weight: 700;
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  background-color: var(--backgroundSecondary);
  color: var(--content1);
}

:host ::ng-deep markdown pre code {
  padding: 0;
  background-color: transparent;
  color: inherit;
  font-size: 0.9em;
  line-height: 1.5;
}

/* Enhanced code blocks for summary */
:host ::ng-deep .summary-markdown pre {
  background-color: var(--backgroundSecondary);
  border-radius: 0.5rem;
  padding: 1.25rem;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

:host ::ng-deep .summary-markdown pre::before {
  content: "JSON";
  position: absolute;
  top: 0;
  right: 1rem;
  background-color: var(--primary);
  color: white;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 0 0 0.375rem 0.375rem;
  font-weight: 600;
  letter-spacing: 0.05em;
}

:host ::ng-deep .summary-markdown pre code {
  display: block;
  line-height: 1.6;
  font-size: 0.95em;
  color: var(--content1);
}

/* Syntax highlighting for JSON in summary */
:host ::ng-deep .summary-markdown pre .hljs-attr {
  color: var(--primary);
  font-weight: 600;
}

:host ::ng-deep .summary-markdown pre .hljs-string {
  color: var(--success);
}

:host ::ng-deep .summary-markdown pre .hljs-number {
  color: var(--warning);
}

:host ::ng-deep .summary-markdown pre .hljs-literal {
  color: var(--error);
}

/* JSON formatting */
:host ::ng-deep markdown .json-key {
  color: var(--primary);
  font-weight: 600;
}

:host ::ng-deep markdown .json-string {
  color: var(--success);
}

:host ::ng-deep markdown .json-number {
  color: var(--warning);
}

:host ::ng-deep markdown .json-boolean {
  color: var(--error);
}

/* Enhanced JSON formatting for summary */
:host ::ng-deep .summary-markdown h3 {
  color: var(--primary);
  font-size: 1.5rem;
  margin-top: 0;
  border-bottom: 2px solid var(--primary);
  padding-bottom: 0.5rem;
}

:host ::ng-deep .summary-markdown h3:first-child {
  margin-top: 0;
}

:host ::ng-deep .summary-markdown ul {
  margin-bottom: 1.5rem;
}

:host ::ng-deep .summary-markdown ul li {
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 0.5rem;
}

:host ::ng-deep .summary-markdown ul li strong {
  color: var(--primary);
  font-weight: 600;
}

:host ::ng-deep .summary-markdown p {
  margin-bottom: 1rem;
  line-height: 1.7;
}

/* Blockquotes */
:host ::ng-deep markdown blockquote {
  border-left: 4px solid var(--primary);
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: var(--content2);
}

/* Tables */
:host ::ng-deep markdown table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  overflow: hidden;
}

:host ::ng-deep markdown th {
  background-color: var(--backgroundSecondary);
  font-weight: 600;
  text-align: left;
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

:host ::ng-deep markdown td {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  border-right: 1px solid var(--border-color);
}

:host ::ng-deep markdown tr:last-child td {
  border-bottom: none;
}

:host ::ng-deep markdown td:last-child {
  border-right: none;
}

/* Links */
:host ::ng-deep markdown a {
  color: var(--primary);
  text-decoration: none;
}

:host ::ng-deep markdown a:hover {
  text-decoration: underline;
}

/* Horizontal rule */
:host ::ng-deep markdown hr {
  border: 0;
  border-top: 1px solid var(--border-color);
  margin: 1.5rem 0;
}

/* Use RippleUI theme tokens */
:root {
  --profile-border: var(--border-color, rgba(226, 226, 226, 1));
  --profile-border-hover: var(--border-color-hover, rgba(200, 200, 200, 1));
}

[data-theme="dark"] {
  --profile-border: var(--border-color, rgba(52, 52, 52, 1));
  --profile-border-hover: var(--border-color-hover, rgba(75, 75, 75, 1));
}

input {
  border: 1px solid var(--profile-border);
  height: 43px;
  padding: 16px;
  border-radius: 9px;
}

.max-w-lg {
  max-width: 41.6rem !important;
}

.bg-secondary.text-white.rounded-t.px-6.py-4.flex.items-center.justify-between {
  border-radius: 18px 18px 0px 0px;
}

.bg-backgroundPrimary.rounded.shadow {
  border-radius: 12px;
}

/* Fix for multiple scrollbars */
.profile-container {
  margin-bottom: 0;
  width: 100%;
  height: 100%;
}

/* GitHub section styling */
.w-8.h-8.flex.items-center.justify-center.bg-backgroundSecondary.rounded-md {
  transition: all 0.3s ease;
}

.w-8.h-8.flex.items-center.justify-center.bg-backgroundSecondary.rounded-md:hover {
  transform: scale(1.05);
  box-shadow: 0 0 10px rgba(var(--primary-rgb), 0.2);
}

/* Connected Apps section styling */
.connected-apps-section .ti-brand-github {
  color: #333;
}

[data-theme="dark"] .connected-apps-section .ti-brand-github {
  color: #fff;
}

/* Modal animations */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-content {
  animation: modalFadeIn 0.3s ease-out forwards;
}

/* Modal overlay animation */
@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-overlay {
  animation: overlayFadeIn 0.2s ease-out forwards;
}

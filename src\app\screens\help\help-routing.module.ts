import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HelpComponent } from './help.component';
import { FaqComponent } from './faq/faq.component';
import { RequestFormComponent } from './request-form/request-form.component';

const routes: Routes = [
  { path: '', component: HelpComponent },
  {
    path: 'faq',
    component: FaqComponent,
  },
  {
    path: 'request-form',
    component: RequestFormComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HelpRoutingModule {}

/** @type {import('tailwindcss').Config} */
export default {
  content: ["./src/**/*.{html,js}"],
  safelist: [
    "btn-primary",
    "btn-secondary",
    "btn-success",
    "btn-error",
    "btn-warning",
    "btn-outline-primary",
    "btn-outline-secondary",
    "btn-outline-success",
    "btn-outline-error",
    "btn-outline-warning",
    "btn-solid-primary",
    "btn-solid-secondary",
    "btn-solid-success",
    "btn-solid-error",
    "btn-solid-warning",
    "btn-ghost",
    "btn-xs",
    "btn-sm",
    "btn-md",
    "btn-lg",
    "btn-xl",
    "btn-rounded",
    "btn-block",
    "btn-loading",
  ],
  theme: {
    extend: {},
  },
  plugins: [require("rippleui")],
  rippleui: {
    themes: [
      {
        themeName: "light",
        colorScheme: "light",
        colors: {
          primary: "#9400FF",
          secondary: "#27005D",
          success: "#2CCE8A",
          warning: "#FB6340",
          error: "#F5365B",

          background: "#FFFFFF",
          backgroundPrimary: "#FFFFFF",
          backgroundSecondary: "#EAEAEA",

          content1: "#080619",
          content2: "#110020",
        },
      },
      {
        themeName: "dark",
        colorScheme: "dark",
        colors: {
          primary: "#9400FF",
          secondary: "#27005D",
          success: "#2CCE8A",
          warning: "#FB6340",
          error: "#F5365B",

          background: "#1A1A1A",
          backgroundPrimary: "#242424",
          backgroundSecondary: "#333333",

          content1: "#FFFFFF",
          content2: "#E0E0E0",
        },
      },
    ],
  },
};

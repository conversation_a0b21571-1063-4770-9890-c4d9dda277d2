.spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #fff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Enhanced loading animations */
.animate-spin-slow {
  animation: spin 8s linear infinite;
}

.animate-bounce-slow {
  animation: bounce 3s infinite;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-ping-slow {
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes ping {
  75%,
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

@keyframes progress {
  0% {
    width: 15%;
  }
  50% {
    width: 85%;
  }
  100% {
    width: 15%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

/* Fix for text colors in dark mode */
[data-theme="dark"] .text-content1 {
  color: var(--content1, #ffffff);
}

[data-theme="dark"] .text-content2 {
  color: var(--content2, #e0e0e0);
}

[data-theme="dark"] .text-content3 {
  color: var(--content2, #bbbbbb);
}

/* Component-specific styles */
/* Note: Global form control styling is now in styles.css */

/* Fix for dropdown in transform modal */
[data-theme="dark"] #Step2 .input,
[data-theme="dark"] .relative.w-full .input {
  background-color: var(--backgroundPrimary) !important;
}

/* Fix for text colors in cards and other elements */
[data-theme="dark"] .text-adaptive-primary {
  color: var(--content1) !important;
}

[data-theme="dark"] .text-adaptive-secondary {
  color: var(--content2) !important;
}

/* Fix for badge colors in dark mode */
[data-theme="dark"] .badge {
  background-color: var(--backgroundSecondary) !important;
  color: var(--content1) !important;
}

[data-theme="dark"] .badge-outline-primary {
  color: var(--primary) !important;
  border-color: var(--primary) !important;
}

/* Fix for checkbox styling in dark mode */
[data-theme="dark"] input[type="checkbox"] + i {
  color: var(--content2) !important;
}

[data-theme="dark"] input[type="checkbox"]:checked + i.ti-check {
  color: var(--primary) !important;
}

/* Pagination styling */
.pagination button:not([disabled]):hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Ripple effect for pagination buttons */
.pagination button::after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 10%,
    transparent 10.01%
  );
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.3s, opacity 0.5s;
}

/* Sticky columns for horizontal scrolling */
.table th.sticky-left,
.table td.sticky-left {
  position: sticky !important;
  left: 0 !important;
  z-index: 10 !important;
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  border-right: 1px solid var(--border-color) !important;
  box-shadow: 2px 0 5px -2px rgba(0, 0, 0, 0.1) !important;
}

.table th.sticky-right,
.table td.sticky-right {
  position: sticky !important;
  right: 0 !important;
  z-index: 10 !important;
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  border-left: 1px solid var(--border-color) !important;
  box-shadow: -2px 0 5px -2px rgba(0, 0, 0, 0.1) !important;
}

/* Increase z-index for header cells to appear above body cells */
.table thead th.sticky-left {
  z-index: 20 !important;
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.08),
    rgba(44, 206, 138, 0.03)
  ) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), 2px 0 5px -2px rgba(0, 0, 0, 0.1) !important;
}

.table thead th.sticky-right {
  z-index: 20 !important;
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.08),
    rgba(44, 206, 138, 0.03)
  ) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), -2px 0 5px -2px rgba(0, 0, 0, 0.1) !important;
}

/* Dark mode header styles for sticky columns */
[data-theme="dark"] .table thead th.sticky-left,
[data-theme="dark"] .table thead th.sticky-right {
  background-color: rgba(148, 0, 255, 0.05) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.12),
    rgba(44, 206, 138, 0.05)
  ) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2), 2px 0 5px -2px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .table thead th.sticky-right {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2), -2px 0 5px -2px rgba(0, 0, 0, 0.3) !important;
}

/* Ensure proper background colors in dark mode */
[data-theme="dark"] .table th.sticky-left,
[data-theme="dark"] .table td.sticky-left {
  background-color: var(--backgroundPrimary) !important;
  box-shadow: 2px 0 5px -2px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .table th.sticky-right,
[data-theme="dark"] .table td.sticky-right {
  background-color: var(--backgroundPrimary) !important;
  box-shadow: -2px 0 5px -2px rgba(0, 0, 0, 0.3) !important;
}

/* Add shadow effect to indicate scrollable content */
.table td.sticky-left::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  pointer-events: none;
  box-shadow: inset -2px 0 4px -2px rgba(0, 0, 0, 0.1);
}

.table td.sticky-right::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
  pointer-events: none;
  box-shadow: inset 2px 0 4px -2px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .table td.sticky-left::after {
  box-shadow: inset -2px 0 4px -2px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .table td.sticky-right::after {
  box-shadow: inset 2px 0 4px -2px rgba(0, 0, 0, 0.3);
}

/* Fix for hover state with sticky columns */
.table tr:hover td.sticky-left,
.table tr:hover td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.03),
    rgba(148, 0, 255, 0.01)
  ) !important;
  opacity: 1 !important;
}

/* Ensure hover doesn't make sticky columns transparent */
.table-hover tbody tr:hover td.sticky-left,
.table-hover tbody tr:hover td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  opacity: 1 !important;
}

/* Direct override for RippleUI table hover styles */
.table.table-hover > tbody > tr:hover > td.sticky-left,
.table.table-hover > tbody > tr:hover > td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.03),
    rgba(148, 0, 255, 0.01)
  ) !important;
  opacity: 1 !important;
}

/* Dark mode hover styles */
[data-theme="dark"] .table tr:hover td.sticky-left,
[data-theme="dark"] .table tr:hover td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.05),
    rgba(148, 0, 255, 0.02)
  ) !important;
  opacity: 1 !important;
}

/* Dark mode direct override for RippleUI table hover styles */
[data-theme="dark"] .table.table-hover > tbody > tr:hover > td.sticky-left,
[data-theme="dark"] .table.table-hover > tbody > tr:hover > td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.05),
    rgba(148, 0, 255, 0.02)
  ) !important;
  opacity: 1 !important;
}

/* Override any hover styles that might make sticky columns transparent */
.enhanced-table .table-hover tbody tr:hover td.sticky-left,
.enhanced-table .table-hover tbody tr:hover td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  opacity: 1 !important;
}

/* Fix for the enhanced-table overflow */
.enhanced-table {
  overflow-x: hidden !important;
}

.enhanced-table .relative.overflow-x-auto {
  max-width: 100% !important;
  overflow-x: auto !important;
}

/* Ensure the scroll bar is inside the table container */
.enhanced-table .flex.flex-col {
  max-width: 100% !important;
}

/* Fix for RippleUI table-hover class that uses gray background */
.enhanced-table .table-hover tbody tr:hover td {
  background-color: transparent !important;
  border-color: var(--border-color) !important;
}

/* Additional fix for sticky columns in enhanced-table */
.enhanced-table .table-hover tbody tr:hover td.sticky-left,
.enhanced-table .table-hover tbody tr:hover td.sticky-right {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity)
  ) !important;
  opacity: 1 !important;
}

[data-theme="dark"] .enhanced-table .table-hover tbody tr:hover td.sticky-left,
[data-theme="dark"]
  .enhanced-table
  .table-hover
  tbody
  tr:hover
  td.sticky-right {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity)
  ) !important;
  opacity: 1 !important;
}

[data-theme="dark"] .pagination button.btn-primary {
  color: white !important;
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}

/* Fix for custom-table hover styles */
.custom-table.table-hover > tbody > tr:hover > td.sticky-left,
.custom-table.table-hover > tbody > tr:hover > td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  opacity: 1 !important;
}

[data-theme="dark"]
  .custom-table.table-hover
  > tbody
  > tr:hover
  > td.sticky-left,
[data-theme="dark"]
  .custom-table.table-hover
  > tbody
  > tr:hover
  > td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  opacity: 1 !important;
}

/* Ensure sticky columns maintain background on hover */
.custom-table tr:hover td.sticky-left,
.custom-table tr:hover td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  z-index: 10 !important;
}

/* Fix for table-compact class with sticky columns */
.table-compact tbody tr:hover td.sticky-left,
.table-compact tbody tr:hover td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  opacity: 1 !important;
}

/* Most specific selector to ensure sticky columns maintain background on hover */
.table.table-compact.table-hover.custom-table tbody tr:hover td.sticky-left,
.table.table-compact.table-hover.custom-table tbody tr:hover td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  opacity: 1 !important;
  z-index: 10 !important;
}

[data-theme="dark"]
  .table.table-compact.table-hover.custom-table
  tbody
  tr:hover
  td.sticky-left,
[data-theme="dark"]
  .table.table-compact.table-hover.custom-table
  tbody
  tr:hover
  td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  opacity: 1 !important;
  z-index: 10 !important;
}

/* Additional fix for database-details component table */
.enhanced-table
  .table.table-compact.table-hover.w-full.border-collapse.custom-table.border
  tbody
  tr:hover
  td.sticky-left,
.enhanced-table
  .table.table-compact.table-hover.w-full.border-collapse.custom-table.border
  tbody
  tr:hover
  td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  opacity: 1 !important;
  z-index: 10 !important;
}

[data-theme="dark"]
  .enhanced-table
  .table.table-compact.table-hover.w-full.border-collapse.custom-table.border
  tbody
  tr:hover
  td.sticky-left,
[data-theme="dark"]
  .enhanced-table
  .table.table-compact.table-hover.w-full.border-collapse.custom-table.border
  tbody
  tr:hover
  td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  opacity: 1 !important;
  z-index: 10 !important;
}

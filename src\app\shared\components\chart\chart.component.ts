import {
  Component,
  ElementRef,
  AfterViewInit,
  ViewChild,
  Input,
  OnChanges,
  SimpleChanges,
  OnDestroy,
} from '@angular/core';
import cytoscape, { Core } from 'cytoscape';
import dagre from 'cytoscape-dagre';
import coseBilkent from 'cytoscape-cose-bilkent';

// Register layouts
cytoscape.use(coseBilkent);
cytoscape.use(dagre);

@Component({
  selector: 'app-chart',
  templateUrl: './chart.component.html',
  styleUrls: ['./chart.component.css'],
})
export class ChartComponent implements AfterViewInit, OnChanges, OnDestroy {
  @ViewChild('cyContainer', { static: true }) cyContainer!: ElementRef;

  @Input() nodesData: any[] = [];
  @Input() edgesData: any[] = [];

  private cy!: Core;
  private fullGraphElements: any[] = [];
  public isSubgraphView = false;
  public isLoading = true;
  public hasError = false;
  public errorMessage = '';
  public longLoadingMessage = false;
  private loadingTimeout: any;

  toggledStates: { [key: string]: boolean } = {
    view: false,
    table: false,
    procedure: false,
    function: false,
    trigger: false,
  };

  ngAfterViewInit(): void {
    console.log(
      'Chart AfterViewInit - Nodes:',
      this.nodesData?.length,
      'Edges:',
      this.edgesData?.length
    );

    // Start loading state
    this.isLoading = true;
    this.hasError = false;

    // Set a timeout to show the long loading message after 5 seconds
    this.loadingTimeout = setTimeout(() => {
      this.longLoadingMessage = true;
    }, 5000);

    if (this.nodesData?.length || this.edgesData?.length) {
      this.deferGraphRender();
    } else {
      // No data to render, but don't show error immediately
      // Just keep the loading state until data arrives
      console.log('No chart data available yet, waiting for data...');
      // Don't set hasError to true here to prevent error box from showing
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log(
      'Chart OnChanges - Nodes:',
      this.nodesData?.length,
      'Edges:',
      this.edgesData?.length
    );

    // Reset states
    this.isLoading = true;
    this.hasError = false;
    this.longLoadingMessage = false;

    // Clear any existing timeout
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }

    // Set a timeout to show the long loading message after 5 seconds
    this.loadingTimeout = setTimeout(() => {
      this.longLoadingMessage = true;
    }, 5000);

    if (
      (changes['nodesData'] || changes['edgesData']) &&
      (this.nodesData?.length || this.edgesData?.length)
    ) {
      if (this.cy) {
        this.cy.destroy();
      }
      this.deferGraphRender();
    } else if (changes['nodesData'] || changes['edgesData']) {
      // Data is being loaded but not available yet
      // Keep the loading state and don't show error
      console.log('Data is being loaded but not available yet');
    }
  }

  ngOnDestroy(): void {
    if (this.cy) {
      this.cy.destroy();
    }

    // Clear any existing timeout
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }
  }

  public deferGraphRender(): void {
    console.log('Deferring graph render...');

    // Reset states
    this.isLoading = true;
    this.hasError = false;
    this.longLoadingMessage = false;

    // Clear any existing timeout
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }

    // Set a timeout to show the long loading message after 5 seconds
    this.loadingTimeout = setTimeout(() => {
      this.longLoadingMessage = true;
    }, 5000);

    requestAnimationFrame(() => {
      setTimeout(() => {
        this.initializeGraph();
      }, 100); // Increased timeout for DOM to be ready
    });
  }

  private initializeGraph(): void {
    console.log('Initializing graph...');
    if (!this.cyContainer || !this.cyContainer.nativeElement) {
      console.error('Cytoscape container not found!');
      this.setErrorState(
        'Chart container not found. Please try refreshing the page.'
      );
      return;
    }

    try {
      // Map nodes and edges to cytoscape format
      this.fullGraphElements = [
        ...this.nodesData.map((node) => ({
          group: 'nodes',
          data: {
            id: node.data.id,
            label: node.data.label,
            type: node.data.type,
          },
          classes: node.data.type, // Add class based on node type
        })),
        ...this.edgesData.map((edge) => ({
          group: 'edges',
          data: {
            id: edge.data.id,
            source: edge.data.source,
            target: edge.data.target,
            label: edge.data.label || '',
          },
        })),
      ];

      console.log(
        'Creating cytoscape instance with elements:',
        this.fullGraphElements.length
      );

      this.cy = cytoscape({
        container: this.cyContainer.nativeElement,
        elements: this.fullGraphElements,
        layout: { name: 'preset' }, // Start with preset, will apply layout after
        style: this.getCytoscapeStyles(),
        zoomingEnabled: true,
        userZoomingEnabled: true,
        panningEnabled: true,
        userPanningEnabled: true,
        textureOnViewport: true,
        pixelRatio: 1,
        wheelSensitivity: 0.3,
        // Ensure initial viewport is centered
        zoom: 1,
        pan: { x: 0, y: 0 },
        minZoom: 0.1,
        maxZoom: 3,
      });

      // Make all elements visible
      this.cy.elements().style('display', 'element');

      // Set node colors based on type
      this.applyNodeColors();

      // Make nodes grabbable
      this.cy.nodes().grabify();

      // Apply layout after a delay to ensure DOM is ready
      setTimeout(() => {
        console.log('Running layout...');
        this.runLayout();

        // Clear loading state after layout is complete
        setTimeout(() => {
          this.isLoading = false;
          clearTimeout(this.loadingTimeout);
        }, 500);
      }, 300);

      // Add node click event
      this.cy.on('tap', 'node', (event) => {
        const nodeId = event.target.id();
        this.highlightSubGraph(nodeId);
      });

      console.log('Cytoscape instance created successfully');
    } catch (error) {
      console.error('Error creating cytoscape instance:', error);
      this.setErrorState(
        'Failed to create chart. Please try again later.',
        error
      );
    }
  }

  private setErrorState(message: string, error?: any): void {
    this.isLoading = false;
    this.hasError = true;

    // Include error details if available
    if (error) {
      const errorDetails =
        error.message ||
        (typeof error === 'string' ? error : JSON.stringify(error));
      this.errorMessage = `${message}\n\nTechnical details: ${errorDetails}`;
    } else {
      this.errorMessage = message;
    }

    clearTimeout(this.loadingTimeout);
  }

  private runLayout() {
    try {
      console.log('Running layout with proper centering...');
      const layout = this.cy.layout(this.getCoseBilkentLayout());

      // Add event listener for when layout completes
      layout.one('layoutstop', () => {
        console.log('Layout completed, centering graph...');
        // Ensure proper centering and fitting after layout completes
        setTimeout(() => {
          // First reset the zoom level
          this.cy.zoom(1);
          // Then fit and center
          this.cy.fit(undefined, 50);
          this.cy.center();
          console.log('Graph centered and fitted');
        }, 100);
      });

      layout.run();

      // Additional safety timeout to ensure centering happens
      setTimeout(() => {
        this.cy.fit(undefined, 50);
        this.cy.center();
      }, 1000);
    } catch (error) {
      console.error('Error running layout:', error);
      this.setErrorState(
        'Failed to layout chart. Please try again later.',
        error
      );
    }
  }

  private applyNodeColors() {
    this.cy.nodes().forEach((node) => {
      const nodeType = node.data('type');
      let color = '#4CAF50'; // Default color

      switch (nodeType) {
        case 'view':
          color = '#3498DB'; // Blue
          break;
        case 'table':
          color = '#E74C3C'; // Red
          break;
        case 'procedure':
          color = '#9B59B6'; // Purple
          break;
        case 'function':
          color = '#F39C12'; // Orange
          break;
        case 'trigger':
          color = '#2ECC71'; // Green
          break;
      }

      node.style('background-color', color);
    });
  }

  private getCoseBilkentLayout(): any {
    return {
      name: 'cose-bilkent',
      animate: false,
      nodeDimensionsIncludeLabels: true,
      refresh: 20,
      fit: true,
      padding: 50,
      randomize: false, // Don't randomize to get more consistent layouts
      nodeRepulsion: 10000, // Increased to spread nodes more
      idealEdgeLength: 120, // Increased for better spacing
      edgeElasticity: 0.15,
      nestingFactor: 1.5,
      gravity: 0.3, // Increased to pull nodes toward center
      // Ensure the graph is centered in the viewport
      boundingBox: undefined, // Will use the container size
      // Position the graph in the center of the viewport
      position: function (_node: any) {
        return { x: 0, y: 0 }; // Default position at center
      },
      // Ensure the layout runs to completion
      quality: 'default',
      // Improve initial positioning
      initialEnergyOnIncremental: 0.8,
    };
  }

  private getCytoscapeStyles(): any[] {
    return [
      {
        selector: 'node',
        style: {
          label: 'data(label)',
          'text-valign': 'center',
          'text-halign': 'center',
          'background-color': '#4CAF50', // Default color
          shape: 'ellipse',
          width: 'label',
          height: 'label',
          padding: '10px',
          'text-wrap': 'wrap',
          'text-max-width': '100px',
          'font-size': '10px',
          color: '#fff', // Text color
          'text-outline-width': 1,
          'text-outline-color': 'rgba(0, 0, 0, 0.5)',
          'min-width': '30px',
          'min-height': '30px',
        },
      },
      {
        selector: 'node.view',
        style: { 'background-color': '#3498DB' },
      },
      {
        selector: 'node.table',
        style: { 'background-color': '#E74C3C' },
      },
      {
        selector: 'node.procedure',
        style: { 'background-color': '#9B59B6' },
      },
      {
        selector: 'node.function',
        style: { 'background-color': '#F39C12' },
      },
      {
        selector: 'node.trigger',
        style: { 'background-color': '#2ECC71' },
      },
      {
        selector: 'edge',
        style: {
          width: 1.5,
          'curve-style': 'bezier',
          'line-color': 'rgba(150, 150, 150, 0.8)',
          'target-arrow-color': 'rgba(150, 150, 150, 0.8)',
          'target-arrow-shape': 'triangle',
          'arrow-scale': 0.6,
          label: 'data(label)',
          'text-rotation': 'autorotate',
          'text-margin-y': '-10px',
          'font-size': '8px',
          color: 'rgba(255, 255, 255, 0.9)',
          'text-outline-width': 2,
          'text-outline-color': 'rgba(0, 0, 0, 0.5)',
        },
      },
      {
        selector: 'node:selected',
        style: {
          'border-width': 3,
          'border-color': '#FFC107',
        },
      },
      {
        selector: 'edge:selected',
        style: {
          width: 3,
          'line-color': '#FFC107',
          'target-arrow-color': '#FFC107',
        },
      },
    ];
  }

  zoomIn(): void {
    if (this.cy) {
      this.cy.zoom(this.cy.zoom() * 1.2);
      this.cy.center();
    }
  }

  zoomOut(): void {
    if (this.cy) {
      this.cy.zoom(this.cy.zoom() * 0.8);
      this.cy.center();
    }
  }

  toggleVisibility(type: string): void {
    if (!this.cy) return;

    const nodes = this.cy.nodes(`.${type}`);
    const isHidden = this.toggledStates[type];
    const display = isHidden ? 'element' : 'none';

    this.cy.batch(() => {
      nodes.style('display', display);
      nodes.connectedEdges().style('display', display);
    });

    this.toggledStates[type] = !isHidden;

    // Rerun layout after visibility change
    setTimeout(() => {
      if (this.cy.elements(':visible').length > 0) {
        this.runLayout();
      }
    }, 300);
  }

  isToggled(nodeType: string): boolean {
    return this.toggledStates[nodeType];
  }

  showEntireGraph(): void {
    if (!this.cy) return;

    console.log('Showing entire graph and resetting view');
    this.isSubgraphView = false;

    // Reset toggle states
    for (const type in this.toggledStates) {
      this.toggledStates[type] = false;
    }

    // Make all elements visible
    this.cy.elements().style('display', 'element');

    // Reset node colors
    this.applyNodeColors();

    // First reset zoom and position
    this.cy.zoom(1);
    this.cy.center();

    // Rerun the layout with a slight delay
    setTimeout(() => {
      this.runLayout();

      // Additional centering after layout
      setTimeout(() => {
        this.cy.fit(undefined, 50);
        this.cy.center();
        console.log('Graph reset and centered');
      }, 500);
    }, 300);
  }

  highlightSubGraph(nodeId: string): void {
    if (!this.cy) return;

    console.log('Highlighting subgraph for node:', nodeId);
    this.isSubgraphView = true;

    const node = this.cy.$id(nodeId);
    if (node.empty()) {
      console.error('Node not found:', nodeId);
      return;
    }

    // Get connected elements
    const connectedEdges = node.connectedEdges();
    const connectedNodes = connectedEdges.connectedNodes();

    // Hide all elements first
    this.cy.elements().style('display', 'none');

    // Show only the selected node and its connections
    node.style('display', 'element');
    connectedEdges.style('display', 'element');
    connectedNodes.style('display', 'element');

    // Highlight the selected node
    node.style('border-width', 3);
    node.style('border-color', '#FFC107');

    // Apply layout to visible elements
    setTimeout(() => {
      const visibleElements = this.cy.elements(':visible');
      if (visibleElements.length > 0) {
        visibleElements.layout(this.getCoseBilkentLayout()).run();
      }
    }, 300);
  }

  goBack(): void {
    this.showEntireGraph();
  }

  focusOnNode(nodeId: string): void {
    if (!this.cy) return;

    const node = this.cy.$id(nodeId);
    if (node.empty()) {
      console.error('Node not found:', nodeId);
      return;
    }

    this.highlightSubGraph(nodeId);
    this.cy.fit(node, 100);
  }
}

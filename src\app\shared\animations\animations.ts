import { 
  trigger, 
  state, 
  style, 
  animate, 
  transition,
  query,
  stagger,
  animateChild,
  group
} from '@angular/animations';

// Fade in animation
export const fadeIn = trigger('fadeIn', [
  transition(':enter', [
    style({ opacity: 0 }),
    animate('300ms ease-in', style({ opacity: 1 })),
  ]),
]);

// Fade in up animation
export const fadeInUp = trigger('fadeInUp', [
  transition(':enter', [
    style({ opacity: 0, transform: 'translateY(10px)' }),
    animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
  ]),
]);

// Slide in from right
export const slideInRight = trigger('slideInRight', [
  transition(':enter', [
    style({ opacity: 0, transform: 'translateX(20px)' }),
    animate('300ms ease-out', style({ opacity: 1, transform: 'translateX(0)' })),
  ]),
  transition(':leave', [
    animate('300ms ease-in', style({ opacity: 0, transform: 'translateX(20px)' })),
  ]),
]);

// Slide in from left
export const slideInLeft = trigger('slideInLeft', [
  transition(':enter', [
    style({ opacity: 0, transform: 'translateX(-20px)' }),
    animate('300ms ease-out', style({ opacity: 1, transform: 'translateX(0)' })),
  ]),
  transition(':leave', [
    animate('300ms ease-in', style({ opacity: 0, transform: 'translateX(-20px)' })),
  ]),
]);

// Scale animation
export const scaleIn = trigger('scaleIn', [
  transition(':enter', [
    style({ opacity: 0, transform: 'scale(0.95)' }),
    animate('200ms ease-out', style({ opacity: 1, transform: 'scale(1)' })),
  ]),
]);

// List animations with stagger effect
export const listAnimation = trigger('listAnimation', [
  transition('* => *', [
    query(':enter', [
      style({ opacity: 0, transform: 'translateY(10px)' }),
      stagger('60ms', [
        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
      ]),
    ], { optional: true }),
  ]),
]);

// Modal animations
export const modalAnimation = trigger('modalAnimation', [
  transition(':enter', [
    group([
      query('.modal-overlay', [
        style({ opacity: 0 }),
        animate('250ms ease-out', style({ opacity: 1 })),
      ]),
      query('.modal-content', [
        style({ opacity: 0, transform: 'scale(0.95) translateY(10px)' }),
        animate('300ms ease-out', style({ opacity: 1, transform: 'scale(1) translateY(0)' })),
      ]),
    ]),
  ]),
  transition(':leave', [
    group([
      query('.modal-overlay', [
        animate('200ms ease-in', style({ opacity: 0 })),
      ]),
      query('.modal-content', [
        animate('250ms ease-in', style({ opacity: 0, transform: 'scale(0.95) translateY(10px)' })),
      ]),
    ]),
  ]),
]);

// Expandable panel animation
export const expandCollapse = trigger('expandCollapse', [
  state('collapsed', style({ height: '0', overflow: 'hidden', opacity: 0 })),
  state('expanded', style({ height: '*', opacity: 1 })),
  transition('collapsed <=> expanded', [
    animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)'),
  ]),
]);

// Rotate animation
export const rotateAnimation = trigger('rotate', [
  state('default', style({ transform: 'rotate(0)' })),
  state('rotated', style({ transform: 'rotate(180deg)' })),
  transition('default <=> rotated', [
    animate('300ms ease-out'),
  ]),
]);

// Pulse animation
export const pulseAnimation = trigger('pulse', [
  transition('* => *', [
    style({ transform: 'scale(1)' }),
    animate('300ms ease-in-out', style({ transform: 'scale(1.05)' })),
    animate('300ms ease-in-out', style({ transform: 'scale(1)' })),
  ]),
]);

// Shake animation for error states
export const shakeAnimation = trigger('shake', [
  transition('* => *', [
    style({ transform: 'translateX(0)' }),
    animate('100ms ease-in-out', style({ transform: 'translateX(-10px)' })),
    animate('100ms ease-in-out', style({ transform: 'translateX(10px)' })),
    animate('100ms ease-in-out', style({ transform: 'translateX(-10px)' })),
    animate('100ms ease-in-out', style({ transform: 'translateX(0)' })),
  ]),
]);

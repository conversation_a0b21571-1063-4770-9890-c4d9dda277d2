import {
  Component,
  Input,
  AfterViewInit,
  ChangeDetectorRef,
  ElementRef,
  ViewChild,
  OnDestroy,
} from '@angular/core';
import {
  trigger,
  transition,
  style,
  animate,
  state,
} from '@angular/animations';
import { ThemeService } from 'src/app/core/theme/theme.service';

@Component({
  selector: 'app-stats',
  templateUrl: './stats.component.html',
  styleUrls: ['./stats.component.css'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(10px)' }),
        animate(
          '400ms ease-out',
          style({ opacity: 1, transform: 'translateY(0)' })
        ),
      ]),
    ]),
    trigger('badgeAnimation', [
      transition(':increment, :decrement', [
        style({ transform: 'scale(1.3)', color: '#9400FF' }),
        animate('300ms ease-out', style({ transform: 'scale(1)', color: '*' })),
      ]),
    ]),
    trigger('progressAnimation', [
      state('void', style({ width: '0%' })),
      transition('void => *', [animate('800ms ease-out')]),
    ]),
  ],
})
export class StatsComponent implements AfterViewInit, OnDestroy {
  @Input() title!: string;
  @Input() icon?: string;
  @Input() badge?: string | number;
  @Input() iconColor: string = 'text-gray-500';
  @Input() iconSize: string = 'text-2xl';
  @Input() onlyHeader: boolean = false;

  @ViewChild('contentContainer', { static: false })
  contentContainer?: ElementRef;
  hasContent: boolean = false;
  private observer?: MutationObserver;

  constructor(private cdr: ChangeDetectorRef) {}

  isImage(icon: string | undefined): boolean {
    return !!icon && (icon.startsWith('http') || icon.startsWith('assets/'));
  }
  getLighterShade(): string {
    const hex = this.extractHexColor(this.iconColor);
    // Use different lightening factors for light and dark modes
    const factor = this.getTheme() === 'light' ? 0.85 : 0.25;
    return this.lightenColor(hex, factor);
  }

  extractHexColor(colorClass: string): string {
    // Extract hex color from the class name (e.g., text-[#FB6340])
    const match = colorClass.match(/#([0-9A-Fa-f]{6})/);

    // If we're in dark mode, use the adjusted colors for the background
    if (this.getTheme() === 'dark') {
      if (colorClass.includes('text-gray-500')) {
        return '#555555'; // Darker background for gray in dark mode
      } else if (colorClass.includes('text-[#FB6340]')) {
        return '#7A3120'; // Darker background for orange in dark mode
      } else if (colorClass.includes('text-[#2CCE8A]')) {
        return '#156745'; // Darker background for green in dark mode
      } else if (colorClass.includes('text-[#9400FF]')) {
        return '#4A007F'; // Darker background for purple in dark mode
      }
    }

    return match ? `#${match[1]}` : '#CCCCCC'; // Default to gray if not found
  }

  lightenColor(hex: string, factor: number): string {
    let r = parseInt(hex.substring(1, 3), 16);
    let g = parseInt(hex.substring(3, 5), 16);
    let b = parseInt(hex.substring(5, 7), 16);

    if (this.getTheme() === 'light') {
      // For light mode, lighten the color
      r = Math.min(255, Math.floor(r + (255 - r) * factor));
      g = Math.min(255, Math.floor(g + (255 - g) * factor));
      b = Math.min(255, Math.floor(b + (255 - b) * factor));
    } else {
      // For dark mode, darken the color slightly but keep some saturation
      r = Math.max(0, Math.floor(r * (1 - factor * 0.5)));
      g = Math.max(0, Math.floor(g * (1 - factor * 0.5)));
      b = Math.max(0, Math.floor(b * (1 - factor * 0.5)));
    }

    return `rgb(${r}, ${g}, ${b})`;
  }
  ngAfterViewInit() {
    this.checkContent();

    // Setup observer for detecting content changes
    if (this.contentContainer?.nativeElement) {
      this.observer = new MutationObserver(() => {
        this.checkContent();
      });

      this.observer.observe(this.contentContainer.nativeElement, {
        childList: true,
        subtree: true,
      });
    }
  }

  private checkContent() {
    setTimeout(() => {
      const hasProjectedElements =
        this.contentContainer?.nativeElement.children.length > 0;

      // Update only if the value actually changes
      if (this.hasContent !== hasProjectedElements) {
        this.hasContent = hasProjectedElements;
        this.cdr.detectChanges();
        console.log('Has projected content:', this.hasContent);
      }
    }, 0);
  }

  ngOnDestroy() {
    if (this.observer) this.observer.disconnect();
  }

  // Get current theme from ThemeService
  getTheme(): string {
    return localStorage.getItem('theme') || 'light';
  }

  // Get the appropriate icon color class based on theme
  getIconColorClass(): string {
    // For predefined colors, adjust for dark mode
    if (this.getTheme() === 'dark') {
      // Brighten specific colors in dark mode
      if (this.iconColor.includes('text-gray-500')) {
        return 'text-[#BBBBBB]';
      } else if (this.iconColor.includes('text-[#FB6340]')) {
        return 'text-[#FF8A6A]';
      } else if (this.iconColor.includes('text-[#2CCE8A]')) {
        return 'text-[#4AEAA8]';
      } else if (this.iconColor.includes('text-[#9400FF]')) {
        return 'text-[#B54AFF]';
      }
    }

    // Default: return the original color for light mode
    return this.iconColor;
  }
}

<!-- <div class="dashboard-container">

  <nav class="sidebar">
    <ul>
      <li><a routerLink="/dashboard">Dashboard</a></li>
      <li><a routerLink="/settings">Settings</a></li>
      <li><a (click)="logout()">Logout</a></li>
    </ul>
  </nav>

  <div class="main-content">
    <header class="topbar">
      <h2>My App</h2>
    </header>
    <div class="content">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
 -->

<div class="flex h-screen flex-row overflow-hidden" [attr.data-theme]="theme">
  <!-- Sidebar -->
  <aside
    [class.w-64]="!collapsed"
    [class.w-16]="collapsed"
    class="flex flex-col transition-all duration-300 items-center justify-between"
    [ngClass]="{
      'bg-secondary text-white border-r': theme === 'light',
      'bg-backgroundPrimary text-content1 border-r border-r-[var(--border-color)]':
        theme === 'dark'
    }"
  >
    <div class="w-full">
      <div
        class="flex justify-start items-center px-4 pt-4 w-full"
        [ngClass]="{
          'text-white': theme === 'light',
          'text-content1': theme === 'dark'
        }"
      >
        <img src="assets/logo-icon.png" />
        <div *ngIf="!collapsed" class="text-xl text-nowrap ml-2">
          RevMigrate
        </div>
      </div>

      <nav class="mt-2 space-y-1 w-full">
        <ul>
          <li>
            <a
              routerLink="/dashboard"
              routerLinkActive="active"
              class="px-4 py-3"
            >
              <span><i class="ti ti-home-2 text-2xl"></i></span>
              <span *ngIf="!collapsed" class="text-nowrap ml-3">Dashboard</span>
            </a>
          </li>
          <li>
            <a
              routerLink="/database"
              routerLinkActive="active"
              class="px-4 py-3"
            >
              <span><i class="ti ti-database text-2xl"></i></span>
              <span *ngIf="!collapsed" class="text-nowrap ml-3"
                >My Databases</span
              >
            </a>
          </li>
          <li>
            <a
              routerLink="/prompt-vault"
              routerLinkActive="active"
              class="px-4 py-3"
            >
              <span><i class="ti ti-wallet text-2xl"></i></span>
              <span *ngIf="!collapsed" class="text-nowrap ml-3"
                >Prompt Vault</span
              >
            </a>
          </li>
          <li>
            <a
              routerLink="/doc-hub"
              routerLinkActive="active"
              class="px-4 py-3"
            >
              <span><i class="ti ti-file-stack text-2xl"></i></span>
              <span *ngIf="!collapsed" class="text-nowrap ml-3">Doc Hub</span>
            </a>
          </li>
          <!-- Admin link using cached property -->
          <li *ngIf="hasAdminAccess">
            <a routerLink="/admin" routerLinkActive="active" class="px-4 py-3">
              <span><i class="ti ti-users text-2xl"></i></span>
              <span *ngIf="!collapsed" class="text-nowrap ml-3">Admin</span>
            </a>
          </li>
          <!-- Debug info for admin access -->
          <!-- <li *ngIf="isDebugMode" class="debug-info">
            <div class="px-4 py-3 text-xs">
              <p>isSuperUser: {{ isSuperUser }}</p>
              <p>isAdmin: {{ isAdmin }}</p>
              <p>Role: {{ userDetails?.role?.name }}</p>
            </div>
          </li> -->
        </ul>
      </nav>
    </div>

    <nav class="mt-2 space-y-1 gap-3 bottom-0 mb-3 items-center w-full">
      <ul>
        <li>
          <a routerLink="/help" routerLinkActive="active" class="px-4 py-3">
            <div
              class="p-4 rounded-lg flex flex-col justify-start w-full shadow-md"
              [ngClass]="{
                'bg-[#3C0093]': theme === 'light',
                'bg-background border border-primary': theme === 'dark'
              }"
              *ngIf="!collapsed"
            >
              <p
                class="text-xl mb-2"
                [ngClass]="{
                  'text-white': theme === 'light',
                  'text-content1': theme === 'dark'
                }"
              >
                Need Help?
              </p>
              <button
                class="btn rounded-sm hover:bg-primary-focus"
                [ngClass]="{
                  'border bg-primary text-white': theme === 'light',
                  'bg-primary text-white border-none': theme === 'dark'
                }"
              >
                Request Help
              </button>
            </div>
            <span *ngIf="collapsed"><i class="ti ti-help text-2xl"></i></span>
          </a>
        </li>
        <li (click)="toggleSidebar()">
          <a class="hover:text-primary px-4 py-3">
            <span><i class="ti ti-pinned text-2xl"></i></span>
            <!-- generated Business Documennt /Migration Plan -->
            <div
              *ngIf="!collapsed"
              class="text-nowrap ml-3"
              [ngClass]="{ 'text-content1': theme === 'dark' }"
            >
              {{ collapsed ? "Pin Sidebar" : "UnPin Sidebar" }}
            </div>
          </a>
        </li>
      </ul>
    </nav>
  </aside>

  <!-- Main Content -->
  <div class="flex-1 flex flex-col overflow-hidden">
    <!-- Topbar -->
    <div class="sticky top-0 bg-backgroundPrimary z-50">
      <header class="flex justify-between items-center px-4 py-2">
        <div class="flex items-baseline">
          <h2 class="text-xl font-semibold text-content1">{{ pageTitle }}</h2>
          <div
            *ngIf="breadcrumb2 && (hasAdminAccess || breadcrumb1 !== 'Admin')"
            class="text-sm flex items-center ml-4"
            [ngClass]="{
              'text-gray-500': theme === 'light',
              'text-gray-400': theme === 'dark'
            }"
          >
            <a
              href="javascript:void(0)"
              (click)="navigateToBreadcrumb1()"
              class="hover:underline hover:text-primary cursor-pointer"
              [ngClass]="{
                'text-gray-600': theme === 'light',
                'text-gray-300': theme === 'dark'
              }"
            >
              {{ breadcrumb1 }}
            </a>
            <span class="mx-1">/</span>
            <p
              class="font-semibold"
              [ngClass]="{
                'text-gray-700': theme === 'light',
                'text-gray-200': theme === 'dark'
              }"
            >
              {{ breadcrumb2 }}
            </p>
          </div>
        </div>
        <div class="flex items-center gap-4">
          <div
            class="theme-switch-wrapper"
            [attr.aria-label]="
              theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'
            "
            title="{{
              theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'
            }}"
          >
            <label class="theme-switch" for="theme-checkbox">
              <input
                type="checkbox"
                id="theme-checkbox"
                [checked]="theme === 'dark'"
                (change)="toggleTheme()"
              />
              <div class="slider round">
                <div class="icons-container">
                  <i class="ti ti-sun sun-icon"></i>
                  <i class="ti ti-moon moon-icon"></i>
                </div>
              </div>
            </label>
          </div>
          <div>
            <div class="dropdown">
              <label class="cursor-pointer" tabindex="0">
                <div
                  class="flex items-center gap-2 px-4 py-2 hover:text-primary"
                >
                  <app-avatar-placeholder
                    size="sm"
                    [name]="userDetails.full_name"
                    className="avatar-ring-primary"
                  ></app-avatar-placeholder>
                  <span class="text-content1"
                    >{{ userDetails.full_name }}
                  </span>
                  <i class="ti ti-chevron-down"></i></div
              ></label>
              <div
                class="dropdown-menu border"
                [ngClass]="{
                  'border-gray-200': theme === 'light',
                  'border-gray-700': theme === 'dark'
                }"
              >
                <a class="dropdown-item text-base" routerLink="/profile"
                  >Profile</a
                >
                <a
                  tabindex="-1"
                  class="dropdown-item text-base"
                  (click)="logout()"
                  >Log Out</a
                >
              </div>
            </div>
          </div>
        </div>
      </header>
      <div class="divider my-0 opacity-50"></div>
    </div>
    <!-- Dashboard Content -->
    <div class="py-3 px-4 overflow-y-auto flex-1">
      <app-chat-bot></app-chat-bot>
      <router-outlet></router-outlet>
    </div>
  </div>
</div>

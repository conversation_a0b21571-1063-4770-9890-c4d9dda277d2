/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;
@import "~@ng-select/ng-select/themes/default.theme.css";
/* Import a Highlight.js dark theme */
@import "highlight.js/styles/monokai-sublime.css"; /* Or any dark theme of your choice */
/* Import animation utilities */
@import "./app/shared/animations/animation-utilities.css";
/* Import global table styles */
@import "./app/shared/styles/table-styles.css";
/* Import global modal styles */
@import "./app/shared/styles/modal-styles.css";

/* Fix for body and html to prevent extra scrollbars */
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* Theme-specific variables */
:root {
  /* Use RippleUI theme tokens */
  --text-primary: var(--content1, #080619);
  --text-secondary: var(--content2, #110020);
  --text-muted: rgba(0, 0, 0, 0.6);
  --bg-muted: rgba(0, 0, 0, 0.05);
  --card-shadow: rgba(0, 0, 0, 0.1);
  --hover-elevation: 0 8px 15px rgba(0, 0, 0, 0.1);
  --scrollbar-track: #f1f1f1;
  --scrollbar-thumb: #c1c1c1;
  --scrollbar-thumb-hover: #a8a8a8;
  --border-color: rgba(226, 226, 226, 1);
  --border-color-hover: rgba(200, 200, 200, 1);
  /* Progress bar colors */
  --progress-bar-from: var(--warning, #fb6340); /* Use RippleUI warning color */
  --progress-bar-to: var(--success, #2cce8a); /* Use RippleUI success color */
  --progress-bg: #e5e7eb; /* Tailwind gray-200 */
  --progress-text: #374151; /* Tailwind gray-700 */
}

[data-theme="dark"] {
  --text-primary: var(--content1, #ffffff);
  --text-secondary: var(--content2, #e0e0e0);
  --text-muted: rgba(255, 255, 255, 0.7);
  --bg-muted: rgba(255, 255, 255, 0.05);
  --card-shadow: rgba(0, 0, 0, 0.3);
  --hover-elevation: 0 8px 15px rgba(0, 0, 0, 0.3);
  --scrollbar-track: #2d2d2d;
  --scrollbar-thumb: #555555;
  --scrollbar-thumb-hover: #777777;
  --border-color: rgba(52, 52, 52, 1);
  --border-color-hover: rgba(75, 75, 75, 1);
  --progress-bar-from: var(--warning, #fb6340); /* Use RippleUI warning color */
  --progress-bar-to: var(--success, #2cce8a); /* Use RippleUI success color */
  --progress-bg: var(--backgroundSecondary, #333333);
  --progress-text: var(--content1, #ffffff);
}

/* Adaptive text classes */
.text-adaptive-primary {
  color: var(--text-primary);
}

.text-adaptive-secondary {
  color: var(--text-secondary);
}

.text-adaptive-muted {
  color: var(--text-muted);
}

/* Fix for primary text color in dark mode */
[data-theme="dark"] .text-primary {
  color: var(--primary, #9400ff) !important;
}

/* Fix for text color in dark mode */
[data-theme="dark"] .text-gray-500 {
  color: #bbbbbb !important;
}

/* Fix for pagination text in dark mode */
[data-theme="dark"] .pagination .text-gray-500 {
  color: #bbbbbb !important;
}

/* Fix for table text in dark mode */
[data-theme="dark"] .table td {
  color: var(--content1, #ffffff);
}

/* Dark mode styles for code blocks */
[data-theme="dark"] pre {
  background-color: #2d2d2d;
  color: white;
}

[data-theme="dark"] code {
  color: #f8f8f2;
}

.badge-flat-success {
  background-color: rgba(44, 206, 138, 0.15);
  transition: all 0.3s ease;
}

.badge-flat-success .text-success {
  color: #2cce8a;
}

[data-theme="dark"] .badge-flat-success {
  background-color: rgba(44, 206, 138, 0.25);
}

[data-theme="dark"] .badge-flat-success .text-success {
  color: #4aeaa8;
}

.badge-flat-success:hover {
  background-color: rgba(44, 206, 138, 0.25);
  box-shadow: 0 0 8px rgba(44, 206, 138, 0.3);
}

[data-theme="dark"] .badge-flat-success:hover {
  background-color: rgba(44, 206, 138, 0.35);
  box-shadow: 0 0 8px rgba(44, 206, 138, 0.5);
}

.badge-flat-error {
  background-color: rgba(245, 54, 91, 0.15);
  transition: all 0.3s ease;
}

.badge-flat-error .text-error {
  color: #f5365b;
}

[data-theme="dark"] .badge-flat-error {
  background-color: rgba(245, 54, 91, 0.25);
}

[data-theme="dark"] .badge-flat-error .text-error {
  color: #ff5b7a;
}

.badge-flat-error:hover {
  background-color: rgba(245, 54, 91, 0.25);
  box-shadow: 0 0 8px rgba(245, 54, 91, 0.3);
}

[data-theme="dark"] .badge-flat-error:hover {
  background-color: rgba(245, 54, 91, 0.35);
  box-shadow: 0 0 8px rgba(245, 54, 91, 0.5);
}

.badge-flat-secondary {
  background-color: rgba(148, 0, 255, 0.15);
  color: var(--primary, #9400ff);
  transition: all 0.3s ease;
}

[data-theme="dark"] .badge-flat-secondary {
  background-color: rgba(148, 0, 255, 0.25);
  color: var(--primary, #9400ff);
}

.badge-flat-secondary:hover {
  background-color: rgba(148, 0, 255, 0.25);
  box-shadow: 0 0 8px rgba(148, 0, 255, 0.3);
}

[data-theme="dark"] .badge-flat-secondary:hover {
  background-color: rgba(148, 0, 255, 0.35);
  box-shadow: 0 0 8px rgba(148, 0, 255, 0.5);
}

/*Scroll bar*/
/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* Cursor styles */
[data-theme="dark"] * {
  cursor: default;
}

[data-theme="dark"] a,
[data-theme="dark"] button,
[data-theme="dark"] .cursor-pointer,
[data-theme="dark"] [role="button"],
[data-theme="dark"] select,
[data-theme="dark"] input[type="checkbox"],
[data-theme="dark"] input[type="radio"] {
  cursor: pointer;
}

[data-theme="dark"] input[type="text"],
[data-theme="dark"] input[type="password"],
[data-theme="dark"] input[type="email"],
[data-theme="dark"] input[type="number"],
[data-theme="dark"] input[type="search"],
[data-theme="dark"] input[type="tel"],
[data-theme="dark"] input[type="url"],
[data-theme="dark"] textarea {
  cursor: text;
}

[data-theme="dark"] input[disabled],
[data-theme="dark"] button[disabled] {
  cursor: not-allowed;
}

.btn-outline-default {
  --tw-border-opacity: 1;
  --tw-text-opacity: 1;
  background-color: transparent;
  border-color: rgb(var(--gray-6) / var(--tw-bg-opacity));
  border-width: 2px;
  color: rgb(var(--gray-9) / var(--tw-text-opacity));
  transition-duration: 0.15s;
  transition-property: transform, color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.btn-outline-default:hover {
  --tw-bg-opacity: 1;
  --tw-text-opacity: 1;
  border-color: rgb(var(--primary) / var(--tw-bg-opacity));
  background-color: rgb(var(--primary) / var(--tw-bg-opacity));
  color: rgb(255 255 255 / var(--tw-text-opacity)) /* #ffffff */;
}
.btn-outline-default:focus-visible {
  outline-color: rgb(var(--primary));
}
.pagination .btn {
  min-width: 1.5rem;
  padding: 0.25rem 0;
}
/* Global form control styling - matching prompt vault */
.select,
.input,
.textarea {
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
  border-radius: 6px;
}

.select:focus,
.input:focus,
.textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(148, 0, 255, 0.1);
  outline: none;
}

.select:hover,
.input:hover,
.textarea:hover {
  border-color: var(--primary);
}

/* Input specific styling */
.input {
  border-width: 1px;
  border-radius: 6px;
}

.input::placeholder {
  color: #71717a;
  opacity: 1;
}

/* For disabled input fields */
.input:disabled::placeholder {
  color: #718096;
}

.input:disabled {
  opacity: 0.5;
  pointer-events: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Select dropdown styling */
select {
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%239400ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  padding-right: 2rem;
}

select:hover {
  border-color: var(--primary);
}

/* Custom dropdown icon animation */
.pointer-events-none i.ti-chevron-down {
  transition: transform 0.3s ease;
  color: var(--primary);
}

select:focus + .pointer-events-none i.ti-chevron-down {
  transform: rotate(180deg);
}

.dropdown:hover .pointer-events-none i.ti-chevron-down {
  transform: translateY(2px);
}

/* Enhanced select styling */
.select {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
  position: relative;
  z-index: 1;
}

.select:hover {
  border-color: var(--primary);
}

.select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(148, 0, 255, 0.15);
  outline: none;
}

/* Form control containers */
.form-control .input,
.form-control .select {
  color: var(--content1);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.form-control .input:focus,
.form-control .select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(148, 0, 255, 0.1);
  outline: none;
}

.form-control .input:hover,
.form-control .select:hover {
  border-color: var(--primary);
}

/* Search icon styling */
.form-control .ti-search {
  color: var(--content2);
}

.btn {
  border-radius: 6px;
}
.btn-outline-default {
  --tw-border-opacity: 1;
  --tw-text-opacity: 1;
  background-color: transparent;
  border-color: rgb(var(--content1) / var(--tw-border-opacity));
  border-width: 2px;
  color: rgb(var(--content1) / var(--tw-text-opacity));
  transition-duration: 0.15s;
  transition-property: transform, color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.badge {
  font-weight: 500;
}

.divider {
  height: 1px;
}

/* Styling for ng-select dropdown panel */
.ng-dropdown-panel {
  @apply bg-backgroundPrimary;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  margin-top: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Styling for ng-select dropdown items */
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
  padding: 8px 12px;
  transition: all 0.2s ease;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option:hover {
  background-color: rgba(148, 0, 255, 0.05);
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected {
  background-color: rgba(148, 0, 255, 0.1);
  color: var(--primary);
  font-weight: 500;
}

/* Dark mode specific styles for ng-select */
[data-theme="dark"] .ng-dropdown-panel {
  background-color: var(--backgroundPrimary) !important;
  border-color: var(--border-color) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
  color: var(--content1) !important;
}

[data-theme="dark"]
  .ng-dropdown-panel
  .ng-dropdown-panel-items
  .ng-option:hover {
  background-color: rgba(148, 0, 255, 0.15) !important;
}

[data-theme="dark"]
  .ng-dropdown-panel
  .ng-dropdown-panel-items
  .ng-option.ng-option-selected {
  background-color: rgba(148, 0, 255, 0.25) !important;
  color: var(--primary) !important;
}

/* Fix border colors for dark mode */
[data-theme="dark"] .border {
  border-color: var(--border-color) !important;
}

[data-theme="dark"] .border-b {
  border-bottom-color: var(--border-color) !important;
}

[data-theme="dark"] .border-t {
  border-top-color: var(--border-color) !important;
}

[data-theme="dark"] .border-l {
  border-left-color: var(--border-color) !important;
}

[data-theme="dark"] .border-r {
  border-right-color: var(--border-color) !important;
}

/* Dark mode form control styling - matching prompt vault */
[data-theme="dark"] input,
[data-theme="dark"] select,
[data-theme="dark"] textarea,
[data-theme="dark"] .input,
[data-theme="dark"] .select,
[data-theme="dark"] .textarea {
  background-color: var(--backgroundPrimary) !important;
  color: var(--content1) !important;
  border: 1px solid var(--border-color) !important;
  transition: all 0.3s ease !important;
}

/* Focus states for all inputs */
[data-theme="dark"] input:focus,
[data-theme="dark"] select:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] .input:focus,
[data-theme="dark"] .select:focus,
[data-theme="dark"] .textarea:focus {
  border-color: var(--primary) !important;
  box-shadow: 0 0 0 2px rgba(148, 0, 255, 0.1) !important;
  outline: none !important;
}

/* Hover states for all inputs */
[data-theme="dark"] input:hover:not(:disabled),
[data-theme="dark"] select:hover:not(:disabled),
[data-theme="dark"] textarea:hover:not(:disabled),
[data-theme="dark"] .input:hover:not(:disabled),
[data-theme="dark"] .select:hover:not(:disabled),
[data-theme="dark"] .textarea:hover:not(:disabled) {
  border-color: rgba(148, 0, 255, 0.7) !important;
}

/* Placeholder text for all inputs */
[data-theme="dark"] input::placeholder,
[data-theme="dark"] textarea::placeholder,
[data-theme="dark"] .input::placeholder,
[data-theme="dark"] .textarea::placeholder {
  color: var(--content2) !important;
  opacity: 0.7 !important;
}

/* Dropdown options in dark mode */
[data-theme="dark"] select option {
  background-color: var(--backgroundPrimary) !important;
  color: var(--content1) !important;
}

/* Fix for dropdown menu/panel in dark mode */
[data-theme="dark"] .select__menu,
[data-theme="dark"] .ng-dropdown-panel,
[data-theme="dark"] .dropdown-menu,
[data-theme="dark"] .select-dropdown,
[data-theme="dark"] .select-options,
[data-theme="dark"] .ng-select .ng-dropdown-panel {
  background-color: var(--backgroundPrimary) !important;
  color: var(--content1) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Fix for dropdown items/options in dark mode */
[data-theme="dark"] .select__option,
[data-theme="dark"] .ng-option,
[data-theme="dark"] .dropdown-item,
[data-theme="dark"] .select-option,
[data-theme="dark"] .ng-select .ng-option {
  background-color: var(--backgroundPrimary) !important;
  color: var(--content1) !important;
}

/* Fix for dropdown hover states in dark mode */
[data-theme="dark"] .select__option:hover,
[data-theme="dark"] .ng-option:hover,
[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .select-option:hover,
[data-theme="dark"] .ng-select .ng-option:hover,
[data-theme="dark"] .ng-select .ng-option.ng-option-marked {
  background-color: rgba(148, 0, 255, 0.15) !important;
  color: var(--content1) !important;
}

/* Fix for selected dropdown item in dark mode */
[data-theme="dark"] .select__option--is-selected,
[data-theme="dark"] .ng-option.ng-option-selected,
[data-theme="dark"] .dropdown-item.active,
[data-theme="dark"] .select-option.selected,
[data-theme="dark"] .ng-select .ng-option.ng-option-selected {
  background-color: rgba(148, 0, 255, 0.25) !important;
  color: var(--content1) !important;
}

/* Form control containers */
[data-theme="dark"] .form-control,
[data-theme="dark"] .dropdown {
  background-color: transparent !important;
}

/* Icons within form controls */
[data-theme="dark"] .form-control i,
[data-theme="dark"] .dropdown i,
[data-theme="dark"] .pointer-events-none i {
  color: var(--content2) !important;
}

/* Dark mode select styling */
[data-theme="dark"] .select:focus {
  box-shadow: 0 0 0 3px rgba(148, 0, 255, 0.25) !important;
}

[data-theme="dark"] .select:hover {
  border-color: rgba(148, 0, 255, 0.7) !important;
}

/* Dark mode dropdown arrow */
[data-theme="dark"] select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%239400ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E") !important;
}

/* Fix for native HTML select dropdown in dark mode */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  /* Webkit browsers (Chrome, Safari) */
  [data-theme="dark"] select {
    color-scheme: dark;
  }

  [data-theme="dark"] select option {
    background-color: var(--backgroundPrimary);
    color: var(--content1);
  }
}

@-moz-document url-prefix() {
  /* Firefox */
  [data-theme="dark"] select {
    background-color: var(--backgroundPrimary);
    color: var(--content1);
  }

  [data-theme="dark"] select option {
    background-color: var(--backgroundPrimary);
    color: var(--content1);
  }
}

/* Microsoft Edge and IE */
@supports (-ms-ime-align: auto) {
  [data-theme="dark"] select,
  [data-theme="dark"] select option {
    background-color: var(--backgroundPrimary);
    color: var(--content1);
  }
}

/* Modal form controls in dark mode */
[data-theme="dark"] .modal-content input,
[data-theme="dark"] .modal-content select,
[data-theme="dark"] .modal-content textarea {
  background-color: var(--backgroundPrimary) !important;
  color: var(--content1) !important;
  border-color: var(--border-color) !important;
}

[data-theme="dark"] .modal-content .input:focus,
[data-theme="dark"] .modal-content .select:focus,
[data-theme="dark"] .modal-content .textarea:focus {
  border-color: var(--primary) !important;
  box-shadow: 0 0 0 2px rgba(148, 0, 255, 0.1) !important;
}

/* Fix for card selection in dark mode */
[data-theme="dark"] .card {
  background-color: var(--backgroundPrimary) !important;
  border-color: var(--border-color) !important;
}

[data-theme="dark"] .card:hover {
  border-color: rgba(148, 0, 255, 0.7) !important;
}

[data-theme="dark"] .card.border-primary {
  border-color: var(--primary) !important;
}

/* Fix sidebar in dark mode */
[data-theme="dark"] aside {
  border-right: 1px solid var(--border-color);
}

/* Fix carousel border in dark mode */
[data-theme="dark"] .max-w-\[700px\].border {
  border-color: var(--border-color) !important;
}

/* Fix section header border in dark mode */
[data-theme="dark"]
  .flex.justify-between.items-center.bg-backgroundPrimary.border-b {
  border-bottom-color: var(--border-color) !important;
}

/* Global progress bar styles for consistency across all pages */
.progress-container {
  background-color: var(--progress-bg);
  border-radius: 0.5rem; /* Increased roundness */
  height: 0.5rem; /* Match the h-2 (0.5rem) from the database page */
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  margin: 0.5rem 0;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .progress-container {
  border-color: var(--border-color);
  background-color: var(--progress-bg);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5);
}

.progress-bar {
  height: 100%;
  border-radius: 0.5rem; /* Increased roundness */
  background-image: linear-gradient(to right, var(--warning), var(--success));
  transition: width 1s cubic-bezier(0.34, 1.56, 0.64, 1);
  background-size: 200% 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  min-width: 5px;
}

[data-theme="dark"] .progress-bar {
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.2);
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.3));
}

/* Hover effects for progress bars */
.progress-container:hover .progress-bar {
  background-size: 200% 100%;
  animation: gradientShift 3s ease infinite;
  filter: brightness(1.1);
}

[data-theme="dark"] .progress-container:hover .progress-bar {
  filter: brightness(1.1) drop-shadow(0 0 3px rgba(255, 255, 255, 0.4));
}

/* Gradient animation for progress bars */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
.table > thead > tr th:first-child {
  border-bottom-left-radius: 0rem;
  border-left: 0;
  border-top-left-radius: 0rem;
}
.table > thead > tr th:last-child {
  border-bottom-right-radius: 0rem;
  border-right: 0;
  border-top-right-radius: 0rem;
}

.table.table-compact > thead > tr > th {
  @apply text-base font-medium border-r;
}
.table.table-compact > tbody > tr > td {
  @apply text-base border-r;
}
/* RippleUI select styling */
.select {
  border-width: 1px;
  border-radius: 6px;
  position: relative;
}

/* RippleUI select dropdown styling */
.select-dropdown {
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--backgroundPrimary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 4px;
  overflow: hidden;
  z-index: 50;
}

.select-dropdown .select-option {
  padding: 8px 12px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.select-dropdown .select-option:hover {
  background-color: rgba(148, 0, 255, 0.05);
}

.select-dropdown .select-option.selected {
  background-color: rgba(148, 0, 255, 0.1);
  color: var(--primary);
  font-weight: 500;
}

/* Dark mode specific styles for RippleUI select dropdown */
[data-theme="dark"] .select-dropdown {
  background-color: var(--backgroundPrimary) !important;
  border-color: var(--border-color) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .select-dropdown .select-option {
  color: var(--content1) !important;
}

[data-theme="dark"] .select-dropdown .select-option:hover {
  background-color: rgba(148, 0, 255, 0.15) !important;
}

[data-theme="dark"] .select-dropdown .select-option.selected {
  background-color: rgba(148, 0, 255, 0.25) !important;
  color: var(--primary) !important;
}

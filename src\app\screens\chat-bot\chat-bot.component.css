/* Chat bot component styles */

/* Peeking icon styles */
.peeking-icon {
  transition: all 0.3s ease;
  border: 2px solid #e5e7eb;
  border-right: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 9999;
  /* Ensure it's visible on all backgrounds */
  background-color: rgb(
    var(--secondary)
  ); /* Changed from white to secondary color */
  color: white; /* Added white text color for better visibility */
  /* Make it peek from the right */
  transform: translateX(calc(100% - 30px));
  animation: peek 3s ease-in-out infinite;
}

.peeking-icon:hover {
  transform: translateX(0);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  animation: none;
}

@keyframes peek {
  0%,
  100% {
    transform: translateX(calc(100% - 30px));
  }
  50% {
    transform: translateX(calc(100% - 40px));
  }
}

/* Animation for the chatbot window */
.chatbot-window {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1); /* Bouncy effect */
  transform-origin: bottom right;
  animation: scale-in 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Improve the shadow effect for better visibility */
.shadow-lg {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Ensure the chatbot is above other elements */
.z-50 {
  z-index: 50;
}

/* Chat message animations */
.bg-gray-100 {
  transition: all 0.2s ease;
}

.bg-gray-100:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  background-color: #f3f4f6;
}

/* Input field animation */
input[type="text"] {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

input[type="text"]:focus {
  border-color: #9400ff;
  box-shadow: 0 0 0 2px rgba(148, 0, 255, 0.2);
  outline: none;
}

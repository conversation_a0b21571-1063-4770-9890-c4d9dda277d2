import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Observable, of } from 'rxjs';
import { take } from 'rxjs/operators';
import { UserManagementService } from '../../../core/services/user-management/user-management.service';
import { AuthService } from '../../../core/auth/auth.service';

@Component({
  selector: 'app-organization-details',
  templateUrl: './organization-details.component.html',
  styleUrls: ['./organization-details.component.css'],
})
export class OrganizationDetailsComponent implements OnInit {
  // Organization details
  organizationId: string = '';
  organizationName: string = '';

  // Tab management
  activeTab: string = 'user';

  // User and role data
  paginatedUsers: any[] = [];
  paginatedRoles: any[] = [];

  // Pagination
  currentPage: number = 1;
  pageSize: number = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  totalEntries: number = 0;
  totalPages: number = 0;
  currentPageStart: number = 0;
  currentPageEnd: number = 0;

  // Forms
  userForm!: FormGroup;
  roleForm!: FormGroup;

  // State management
  isLoading: boolean = false;
  errorMessage: string = '';
  successMessage: string = '';

  // User permissions
  isSuperUser: boolean = false;
  isOrgAdmin: boolean = false;
  userOrgId: string = '';
  currentUser: any = null;

  // Selected items
  selectedUser: any = null;
  selectedRole: any = null;

  // Role ID to name mapping
  roleIdToNameMap: Map<string, string> = new Map();

  // Privileges
  privileges$!: Observable<any[]>;
  selectedPrivileges: number[] = [];

  // Data arrays
  users: any[] = [];
  roles: any[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userManagementService: UserManagementService,
    private authService: AuthService,
    private fb: FormBuilder
  ) {
    this.initUserForm();
    this.initRoleForm();
  }

  ngOnInit(): void {
    // Get the organization ID from the route
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.organizationId = id;
        this.loadOrganizationDetails();
        this.loadUsers();
        this.loadRoles();
        this.loadPermissions();
      } else {
        this.router.navigate(['/user-management']);
      }
    });

    // Load current user information
    this.loadCurrentUser();
  }

  loadCurrentUser() {
    this.authService.getUserRoleDetails().subscribe({
      next: (roleDetails) => {
        // Store the full user details
        this.currentUser = roleDetails;

        // Set basic user information
        this.isSuperUser = roleDetails.is_superuser || false;

        // Case-insensitive check for Admin role
        const roleName = roleDetails.role?.name || roleDetails.role?.role || '';
        const hasRoleId =
          roleDetails.role_id !== undefined && roleDetails.role_id !== null;

        // Consider user an admin if they have Admin role name OR they have a role_id
        this.isOrgAdmin = roleName.toLowerCase() === 'admin' || hasRoleId;

        this.userOrgId = roleDetails.organization?.id || '';

        console.log('User role details loaded:', {
          isSuperUser: this.isSuperUser,
          isOrgAdmin: this.isOrgAdmin,
          userOrgId: this.userOrgId,
          permissions: roleDetails.role?.permissions,
        });
      },
      error: (error) => {
        console.error('Error loading current user role details:', error);

        // Fallback to getUserDetails if getUserRoleDetails fails
        this.authService.getUserDetails().subscribe({
          next: (user) => {
            // Store the full user details
            this.currentUser = user;

            // Set basic user information
            this.isSuperUser = user.is_superuser || false;

            // Case-insensitive check for Admin role
            const roleName = user.role?.name || '';
            const hasRoleId =
              user.role_id !== undefined && user.role_id !== null;

            // Consider user an admin if they have Admin role name OR they have a role_id
            this.isOrgAdmin = roleName.toLowerCase() === 'admin' || hasRoleId;

            this.userOrgId = user.organization_id || '';

            console.log('User details loaded (fallback):', {
              isSuperUser: this.isSuperUser,
              isOrgAdmin: this.isOrgAdmin,
              userOrgId: this.userOrgId,
              permissions: user.role?.permissions,
            });
          },
          error: (err) => {
            console.error('Error in fallback user details:', err);
            // Default to no special permissions
            this.isSuperUser = false;
            this.isOrgAdmin = false;
            this.userOrgId = '';
            this.currentUser = null;
          },
        });
      },
    });
  }

  loadOrganizationDetails() {
    this.isLoading = true;
    this.errorMessage = '';

    this.userManagementService
      .getOrganizationById(this.organizationId)
      .subscribe({
        next: (org) => {
          this.organizationName = org.name;
          this.isLoading = false;

          // Update the document title with the organization name
          document.title = this.organizationName;

          // Store the organization name in localStorage for breadcrumb use
          const pageTitles = localStorage.getItem('pageTitles')
            ? JSON.parse(localStorage.getItem('pageTitles') || '{}')
            : {};

          // Set the title for this specific route
          const currentPath = `/admin/organization/${this.organizationId}`;
          pageTitles[currentPath] = this.organizationName;
          localStorage.setItem('pageTitles', JSON.stringify(pageTitles));

          console.log('Stored organization name in localStorage:', {
            path: currentPath,
            name: this.organizationName,
            allTitles: pageTitles,
          });
        },
        error: (error) => {
          console.error('Error loading organization details:', error);
          this.errorMessage =
            'Failed to load organization details. Please try again.';
          this.isLoading = false;
        },
      });
  }

  loadUsers() {
    this.isLoading = true;
    this.errorMessage = '';

    this.userManagementService
      .listOrganizationUsers(this.organizationId)
      .subscribe({
        next: (data) => {
          // Map API response to our users array
          this.users = data.map((user: any) => {
            // Check if we have role_id but no role name
            let roleName = user.role?.name || user.role?.role;
            const roleId = user.role_id || user.role?.id;

            // If we have a role ID but no role name, try to get it from our mapping
            if (roleId && !roleName && this.roleIdToNameMap.size > 0) {
              roleName = this.getRoleNameById(roleId);
            }

            return {
              id: user.id,
              displayName: user.full_name,
              email: user.email,
              role: roleName || 'No Role',
              roleId: roleId,
              isActive: user.is_active,
            };
          });

          // If we have roles loaded but some users still don't have role names, update them
          if (this.roleIdToNameMap.size > 0) {
            this.updateUserRoleNames();
          }

          this.totalEntries = this.users.length;
          this.updatePagination();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading users:', error);
          this.errorMessage = 'Failed to load users. Please try again.';
          this.isLoading = false;
        },
      });
  }

  loadRoles() {
    this.isLoading = true;
    this.errorMessage = '';

    this.userManagementService
      .getOrganizationRoles(this.organizationId)
      .subscribe({
        next: (data) => {
          // Map API response to our roles array
          this.roles = data.map((role: any) => {
            // Ensure permissions is not null or undefined
            let permissions = role.permissions || {};

            return {
              id: role.id,
              role: role.name,
              description: role.description,
              is_sharable: role.is_sharable,
              privileges: this.formatPermissions(permissions),
              permissions: permissions,
            };
          });

          // Build role ID to name mapping
          this.roleIdToNameMap.clear();
          this.roles.forEach((role) => {
            this.roleIdToNameMap.set(role.id, role.role);
          });

          // Update user roles if users are already loaded
          if (this.users.length > 0) {
            this.updateUserRoleNames();
          }

          this.totalEntries = this.roles.length;
          this.updatePagination();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading roles:', error);
          this.errorMessage = 'Failed to load roles. Please try again.';
          this.isLoading = false;
        },
      });
  }

  loadPermissions() {
    this.userManagementService.getAllPermissions().subscribe({
      next: (permissions) => {
        // Convert permissions to the format expected by the UI
        this.privileges$ = of(
          permissions.map((permission, index) => ({
            id: index + 1,
            name: permission,
          }))
        );
      },
      error: (error) => {
        console.error('Error loading permissions:', error);
        // Fallback to sample data
        this.privileges$ = of([
          { id: 1, name: 'Read Access' },
          { id: 2, name: 'Write Access' },
          { id: 3, name: 'Execute Access' },
        ]);
      },
    });
  }

  // Initialize forms
  initUserForm() {
    this.userForm = this.fb.group(
      {
        full_name: ['', [Validators.required]],
        email: ['', [Validators.required, Validators.email]],
        password: ['', [Validators.required, Validators.minLength(8)]],
        confirm_password: ['', [Validators.required]],
        role_id: ['', [Validators.required]],
        send_welcome_email: [true],
      },
      {
        validators: this.passwordMatchValidator,
      }
    );
  }

  initRoleForm() {
    this.roleForm = this.fb.group({
      name: [{ value: '', disabled: false }, [Validators.required]],
      description: [{ value: '', disabled: false }, [Validators.required]],
      is_sharable: [{ value: true, disabled: false }],
      permissions: [{}],
    });
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password')?.value;
    const confirmPassword = form.get('confirm_password')?.value;

    if (password && confirmPassword && password !== confirmPassword) {
      form.get('confirm_password')?.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    return null;
  }

  // Pagination methods
  updatePagination() {
    const data = this.activeTab === 'user' ? this.users : this.roles;
    this.totalEntries = data.length;
    this.totalPages = Math.ceil(this.totalEntries / this.pageSize);

    // Ensure current page is valid
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages || 1;
    }

    // Calculate start and end indices
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.totalEntries);

    // Update current page info
    this.currentPageStart = this.totalEntries > 0 ? startIndex + 1 : 0;
    this.currentPageEnd = endIndex;

    // Update paginated data
    if (this.activeTab === 'user') {
      this.paginatedUsers = this.users.slice(startIndex, endIndex);
    } else {
      this.paginatedRoles = this.roles.slice(startIndex, endIndex);
    }
  }

  prevPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePagination();
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePagination();
    }
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePagination();
    }
  }

  changePageSize(event: any) {
    this.pageSize = parseInt(event.target.value, 10);
    this.currentPage = 1;
    this.updatePagination();
  }

  // Tab switching
  switchTab(tab: string) {
    this.activeTab = tab;
    this.currentPage = 1;
    this.updatePagination();
  }

  // Helper methods
  updateUserRoleNames() {
    this.users.forEach((user) => {
      if (
        user.roleId &&
        (!user.role || user.role === 'No Role' || user.role === 'Unknown Role')
      ) {
        user.role = this.getRoleNameById(user.roleId);
      }
    });
  }

  getRoleNameById(roleId?: string): string {
    if (!roleId) return 'No Role';
    return this.roleIdToNameMap.get(roleId) || 'Unknown Role';
  }

  formatPermissions(permissions: Record<string, boolean> | string[]): string {
    if (!permissions) return 'No permissions';

    let activePermissions: string[] = [];

    // Handle array format
    if (Array.isArray(permissions)) {
      activePermissions = permissions.map((permission) =>
        this.formatPermissionName(permission)
      );
    } else {
      // Handle object format
      activePermissions = Object.entries(permissions)
        .filter(([_, value]) => value)
        .map(([key, _]) => this.formatPermissionName(key));
    }

    return activePermissions.length > 0
      ? activePermissions.join(', ')
      : 'No permissions';
  }

  formatPermissionName(permission: string): string {
    if (!permission) return '';

    // Replace underscores with spaces and capitalize each word
    return permission
      .replace(/_/g, ' ')
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  isPredefinedRole(roleName?: string): boolean {
    if (!roleName) return false;

    const predefinedRoles = [
      'Admin',
      'Developer',
      'Tester',
      'Analyst',
      'Deployment',
    ];
    return predefinedRoles.includes(roleName);
  }

  getPermissionsList(
    permissions?: Record<string, boolean> | string[]
  ): string[] {
    if (!permissions) return [];

    // Handle array format
    if (Array.isArray(permissions)) {
      return permissions;
    }

    // Handle object format
    return Object.entries(permissions)
      .filter(([_, value]) => value)
      .map(([key, _]) => key);
  }

  getTopPermissions(
    permissions?: Record<string, boolean> | string[],
    count: number = 3
  ): string[] {
    if (!permissions) return [];

    const enabledPermissions = this.getPermissionsList(permissions);
    return enabledPermissions.slice(0, count);
  }

  countPermissions(permissions?: Record<string, boolean> | string[]): number {
    if (!permissions) return 0;

    return this.getPermissionsList(permissions).length;
  }

  // User and role management functions
  openAddUserModal() {
    // Check if user has permission to create users
    if (!this.canCreateUsers()) {
      this.errorMessage = 'You do not have permission to create users.';
      return;
    }

    this.resetUserForm();
    this.errorMessage = '';
    this.successMessage = '';

    // Open the user modal
    const modalCheckbox = document.getElementById(
      'AddUserModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  openEditUserModal(user: any) {
    // Check if user has permission to edit users
    if (!this.canEditUsers()) {
      this.errorMessage = 'You do not have permission to edit users.';
      return;
    }

    this.selectedUser = user;
    this.errorMessage = '';
    this.successMessage = '';

    // Populate the form with user data
    this.userForm.patchValue({
      full_name: user.displayName,
      email: user.email,
      role_id: user.roleId || '',
      // Don't set password fields when editing
      password: '',
      confirm_password: '',
      send_welcome_email: false,
    });

    // Open the modal
    const modalCheckbox = document.getElementById(
      'EditUserModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  createUser() {
    // Check if user has permission to create users
    if (!this.canCreateUsers()) {
      this.errorMessage = 'You do not have permission to create users.';
      return;
    }

    // Log permission check for debugging
    console.log('Create user permission check:', {
      canCreate: this.canCreateUsers(),
      isSuperUser: this.isSuperUser,
      isOrgAdmin: this.isOrgAdmin,
      permissions: this.currentUser?.role?.permissions,
      // Superusers and organization admins can create users
      // Users with create_users permission can also create users
    });

    if (this.userForm.invalid) {
      this.markFormGroupTouched(this.userForm);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const formData = this.userForm.value;

    // Prepare data for API
    const userData = {
      email: formData.email,
      full_name: formData.full_name,
      password: formData.password,
      role_id: formData.role_id,
      send_welcome_email: formData.send_welcome_email,
    };

    console.log('Creating user with data:', {
      ...userData,
      password: '********',
      organization_id: this.organizationId,
    });

    this.userManagementService
      .createOrganizationUser(this.organizationId, userData)
      .subscribe({
        next: (_) => {
          this.successMessage = 'User created successfully!';
          this.resetUserForm();
          this.loadUsers(); // Reload the users list
          this.isLoading = false;

          // Close modal
          const modalCheckbox = document.getElementById(
            'AddUserModal'
          ) as HTMLInputElement;
          if (modalCheckbox) {
            modalCheckbox.checked = false;
          }
        },
        error: (error: any) => {
          console.error('Error creating user:', error);
          this.errorMessage = 'Failed to create user. Please try again.';
          this.isLoading = false;
        },
      });
  }

  updateUser() {
    if (!this.selectedUser) {
      this.errorMessage = 'No user selected for update.';
      return;
    }

    // Check if user has permission to edit users
    if (!this.canEditUsers()) {
      this.errorMessage = 'You do not have permission to update users.';
      return;
    }

    // Log permission check for debugging
    console.log('Update user permission check:', {
      canEdit: this.canEditUsers(),
      isSuperUser: this.isSuperUser,
      isOrgAdmin: this.isOrgAdmin,
      permissions: this.currentUser?.role?.permissions,
      // Superusers and organization admins can edit users
      // Users with edit_users permission can also edit users
    });

    if (this.userForm.invalid) {
      this.markFormGroupTouched(this.userForm);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const formData = this.userForm.value;

    // Prepare data for API
    const userData = {
      email: formData.email,
      full_name: formData.full_name,
      role_id: formData.role_id,
    };

    console.log('Updating user with data:', userData);

    this.userManagementService
      .updateUser(this.organizationId, this.selectedUser.id, userData)
      .subscribe({
        next: () => {
          this.successMessage = 'User updated successfully!';
          this.loadUsers(); // Reload the users list
          this.isLoading = false;

          // Close modal
          const modalCheckbox = document.getElementById(
            'EditUserModal'
          ) as HTMLInputElement;
          if (modalCheckbox) {
            modalCheckbox.checked = false;
          }
        },
        error: (error: any) => {
          console.error('Error updating user:', error);
          this.errorMessage = 'Failed to update user. Please try again.';
          this.isLoading = false;
        },
      });
  }

  /**
   * Change user role
   * @param userId User ID
   * @param roleId New role ID
   */
  changeUserRole(userId: string, roleId: string) {
    if (!userId || !roleId) {
      this.errorMessage = 'User ID and role ID are required';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    this.userManagementService
      .changeUserRole(this.organizationId, userId, roleId)
      .subscribe({
        next: (response) => {
          console.log('User role update response:', response);
          this.successMessage = 'User role updated successfully';
          this.loadUsers(); // Reload the users list
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error changing user role:', error);
          this.errorMessage = 'Failed to change user role. Please try again.';
          this.isLoading = false;
        },
      });
  }

  /**
   * Open the change role modal
   * @param user User to change role for
   */
  openChangeRoleModal(user: any) {
    this.selectedUser = user;
    this.errorMessage = '';
    this.successMessage = '';

    // Open the modal
    const modalCheckbox = document.getElementById(
      'ChangeRoleModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  /**
   * Update the selected user's role
   */
  updateUserRole() {
    if (!this.selectedUser || !this.selectedUser.id) {
      this.errorMessage = 'No user selected';
      return;
    }

    const roleId = (document.getElementById('roleSelect') as HTMLSelectElement)
      ?.value;

    if (!roleId) {
      this.errorMessage = 'Please select a role';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.changeUserRole(this.selectedUser.id, roleId);

    // Close the modal
    const modalCheckbox = document.getElementById(
      'ChangeRoleModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = false;
    }
  }

  toggleUserStatus(user: any) {
    if (!user || !user.id) {
      this.errorMessage = 'Invalid user data.';
      return;
    }

    // Check if user has permission to edit users
    if (!this.canEditUsers()) {
      this.errorMessage = 'You do not have permission to change user status.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const newStatus = !user.isActive;
    const action = newStatus ? 'activate' : 'deactivate';

    this.userManagementService
      .updateUserStatus(this.organizationId, user.id, newStatus)
      .subscribe({
        next: () => {
          this.successMessage = `User ${action}d successfully!`;
          // Update the user status in the local array
          user.isActive = newStatus;
          this.isLoading = false;
        },
        error: (error: any) => {
          console.error(`Error ${action}ing user:`, error);
          this.errorMessage = `Failed to ${action} user. Please try again.`;
          this.isLoading = false;
        },
      });
  }

  openViewRoleModal(role: any) {
    this.selectedRole = role;
    this.errorMessage = '';
    this.successMessage = '';

    // Open the modal
    const modalCheckbox = document.getElementById(
      'ViewRoleModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  openEditRoleModal(role: any) {
    this.selectedRole = role;
    this.errorMessage = '';
    this.successMessage = '';

    // Reset selected privileges
    this.selectedPrivileges = [];

    // Convert permissions to selected privileges
    if (role.permissions) {
      // Handle array format
      if (Array.isArray(role.permissions)) {
        this.privileges$.subscribe((privileges) => {
          privileges.forEach((privilege) => {
            if (role.permissions.includes(privilege.name)) {
              this.selectedPrivileges.push(privilege.id);
            }
          });
        });
      } else {
        // Handle object format
        this.privileges$.subscribe((privileges) => {
          privileges.forEach((privilege) => {
            if (role.permissions[privilege.name]) {
              this.selectedPrivileges.push(privilege.id);
            }
          });
        });
      }
    }

    // Populate the form with role data
    this.roleForm.patchValue({
      name: role.role,
      description: role.description,
      is_sharable: role.is_sharable,
    });

    // Open the modal
    const modalCheckbox = document.getElementById(
      'EditRoleModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  openDeleteRoleModal(role: any) {
    this.selectedRole = role;
    this.errorMessage = '';
    this.successMessage = '';

    // Open the modal
    const modalCheckbox = document.getElementById(
      'DeleteRoleModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  createRole() {
    // Check if user has permission to create roles
    if (!this.canCreateRoles()) {
      this.errorMessage = 'You do not have permission to create roles.';
      return;
    }

    // Log permission check for debugging
    console.log('Create role permission check:', {
      canCreate: this.canCreateRoles(),
      isSuperUser: this.isSuperUser,
      isOrgAdmin: this.isOrgAdmin,
      // Super users can create any roles, admin users can create custom roles
    });

    if (this.roleForm.invalid) {
      this.markFormGroupTouched(this.roleForm);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const formData = this.roleForm.value;

    // Convert selected privileges to permissions object
    const getPermissionsPromise = new Promise<Record<string, boolean>>(
      (resolve) => {
        const permissions: Record<string, boolean> = {};

        this.privileges$.subscribe((privileges) => {
          privileges.forEach((privilege) => {
            permissions[privilege.name] = this.selectedPrivileges.includes(
              privilege.id
            );
          });
          resolve(permissions);
        });
      }
    );

    // Wait for permissions to be processed, then send the API request
    getPermissionsPromise.then((permissions) => {
      // Prepare data for API
      const roleData = {
        name: formData.name,
        description: formData.description,
        is_sharable: formData.is_sharable,
        permissions: permissions,
      };

      console.log('Creating role with data:', roleData);

      this.userManagementService
        .createOrganizationRole(this.organizationId, roleData)
        .subscribe({
          next: () => {
            this.successMessage = 'Role created successfully!';
            this.resetRoleForm();
            this.loadRoles(); // Reload the roles list
            this.isLoading = false;

            // Close modal
            const modalCheckbox = document.getElementById(
              'AddRoleModal'
            ) as HTMLInputElement;
            if (modalCheckbox) {
              modalCheckbox.checked = false;
            }
          },
          error: (error: any) => {
            console.error('Error creating role:', error);
            this.errorMessage = 'Failed to create role. Please try again.';
            this.isLoading = false;
          },
        });
    });
  }

  updateRole() {
    if (!this.selectedRole) {
      this.errorMessage = 'No role selected for update.';
      return;
    }

    if (!this.canEditRole(this.selectedRole)) {
      this.errorMessage = 'You do not have permission to edit this role.';

      // Log permission check for debugging
      console.log('Update role permission check:', {
        canEdit: this.canEditRole(this.selectedRole),
        isSuperUser: this.isSuperUser,
        isOrgAdmin: this.isOrgAdmin,
        isPredefined: this.isPredefinedRole(this.selectedRole?.role),
        // Super users can edit any role, admin users can only edit custom roles
      });

      return;
    }

    if (this.roleForm.invalid) {
      this.markFormGroupTouched(this.roleForm);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const formData = this.roleForm.value;

    // Convert selected privileges to permissions object
    const getPermissionsPromise = new Promise<Record<string, boolean>>(
      (resolve) => {
        const permissions: Record<string, boolean> = {};

        this.privileges$.subscribe((privileges) => {
          privileges.forEach((privilege) => {
            permissions[privilege.name] = this.selectedPrivileges.includes(
              privilege.id
            );
          });
          resolve(permissions);
        });
      }
    );

    // Wait for permissions to be processed, then send the API request
    getPermissionsPromise.then((permissions) => {
      // Prepare data for API
      const roleData = {
        name: formData.name,
        description: formData.description,
        is_sharable: formData.is_sharable,
        permissions: permissions,
      };

      console.log('Updating role with data:', roleData);

      if (!this.organizationId || !this.selectedRole || !this.selectedRole.id) {
        this.errorMessage = 'Missing organization or role information.';
        this.isLoading = false;
        return;
      }

      this.userManagementService
        .updateRolePermissions(
          this.organizationId,
          this.selectedRole.id,
          roleData
        )
        .subscribe({
          next: (updatedRole) => {
            console.log('Role update response:', updatedRole);
            this.successMessage = 'Role updated successfully!';
            this.loadRoles(); // Reload the roles list
            this.isLoading = false;

            // Close modal
            const modalCheckbox = document.getElementById(
              'EditRoleModal'
            ) as HTMLInputElement;
            if (modalCheckbox) {
              modalCheckbox.checked = false;
            }
          },
          error: (error: any) => {
            console.error('Error updating role:', error);
            this.errorMessage = 'Failed to update role. Please try again.';
            this.isLoading = false;
          },
        });
    });
  }

  deleteRole() {
    if (!this.selectedRole) {
      this.errorMessage = 'No role selected for deletion.';
      return;
    }

    if (!this.canDeleteRole(this.selectedRole)) {
      this.errorMessage = 'You do not have permission to delete this role.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.userManagementService
      .deleteRole(this.organizationId, this.selectedRole.id)
      .subscribe({
        next: () => {
          this.successMessage = 'Role deleted successfully!';
          this.loadRoles(); // Reload the roles list
          this.isLoading = false;

          // Close modal
          const modalCheckbox = document.getElementById(
            'DeleteRoleModal'
          ) as HTMLInputElement;
          if (modalCheckbox) {
            modalCheckbox.checked = false;
          }
        },
        error: (error: any) => {
          console.error('Error deleting role:', error);
          this.errorMessage = 'Failed to delete role. Please try again.';
          this.isLoading = false;
        },
      });
  }

  togglePrivilege(privilegeId: number, event: any) {
    // For the Add Role modal, we don't have a selectedRole yet, so we should allow toggling
    // For the Edit Role modal, only allow toggling if the user can edit the role
    const isAddRoleModal = !this.selectedRole;

    if (!isAddRoleModal && !this.canEditRole(this.selectedRole)) {
      event.preventDefault();
      return;
    }

    const checked = event.target.checked;

    if (checked) {
      // Add to selected privileges if not already present
      if (!this.selectedPrivileges.includes(privilegeId)) {
        this.selectedPrivileges.push(privilegeId);
      }
    } else {
      // Remove from selected privileges
      this.selectedPrivileges = this.selectedPrivileges.filter(
        (id) => id !== privilegeId
      );
    }
  }

  resetUserForm() {
    this.userForm.reset({
      full_name: '',
      email: '',
      password: '',
      confirm_password: '',
      role_id: '',
      send_welcome_email: true,
    });
    this.selectedUser = null;
  }

  resetRoleForm() {
    this.roleForm.reset({
      name: '',
      description: '',
      is_sharable: true,
      permissions: {},
    });
    this.selectedPrivileges = [];
    this.selectedRole = null;
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  // Permission checks
  canCreateUsers(): boolean {
    // Superusers can always create users
    if (this.isSuperUser) {
      return true;
    }

    // Organization admins can create users
    if (this.isOrgAdmin) {
      return true;
    }

    // Check if user has create_users permission from getUserRoleDetails
    if (this.currentUser?.role?.permissions) {
      // From user-role endpoint, permissions are always in array format
      if (Array.isArray(this.currentUser.role.permissions)) {
        return this.currentUser.role.permissions.includes('create_users');
      }
      // For backward compatibility with getUserDetails
      else if (typeof this.currentUser.role.permissions === 'object') {
        return !!this.currentUser.role.permissions['create_users'];
      }
    }

    return false;
  }

  canCreateRoles(): boolean {
    // Super users can always create roles
    if (this.isSuperUser) {
      return true;
    }

    // Admin users can also create roles
    if (this.isOrgAdmin) {
      return true;
    }

    return false;
  }

  canEditUsers(): boolean {
    // Superusers can always edit users
    if (this.isSuperUser) {
      return true;
    }

    // Organization admins can edit users
    if (this.isOrgAdmin) {
      return true;
    }

    // Check if user has edit_users permission from getUserRoleDetails
    if (this.currentUser?.role?.permissions) {
      // From user-role endpoint, permissions are always in array format
      if (Array.isArray(this.currentUser.role.permissions)) {
        return this.currentUser.role.permissions.includes('edit_users');
      }
      // For backward compatibility with getUserDetails
      else if (typeof this.currentUser.role.permissions === 'object') {
        return !!this.currentUser.role.permissions['edit_users'];
      }
    }

    return false;
  }

  canEditRole(role?: any): boolean {
    if (!role) return false;

    // Predefined roles can only be edited by superusers
    if (this.isPredefinedRole(role.role) && !this.isSuperUser) return false;

    // Superusers can edit any role
    if (this.isSuperUser) {
      return true;
    }

    // Admin users can edit custom roles (non-predefined roles)
    if (this.isOrgAdmin && !this.isPredefinedRole(role.role)) {
      return true;
    }

    return false;
  }

  canDeleteRole(role?: any): boolean {
    if (!role) return false;

    // Predefined roles cannot be deleted by anyone
    if (this.isPredefinedRole(role.role)) return false;

    // Only superusers can delete roles
    return this.isSuperUser;
  }

  // Navigation
  goBack() {
    this.router.navigate(['/admin']);
  }
}

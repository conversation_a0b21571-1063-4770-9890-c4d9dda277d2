<div class="flex pb-3 gap-2 justify-between items-center">
  <div class="flex gap-2 items-center">
    <!-- Search with icon inside -->
    <div class="flex items-center min-w-[250px]" *ngIf="showTable">
      <div class="relative w-full">
        <input
          type="text"
          placeholder="Search..."
          class="input input-sm h-9 min-h-0 w-full pl-14 bg-backgroundPrimary text-content1 border-[var(--border-color)] rounded-md"
          (input)="onSearchChange($event)"
          style="text-indent: 24px"
        />
        <div
          class="absolute inset-y-0 left-2 flex items-center pointer-events-none"
        >
          <i class="ti ti-search text-content2"></i>
        </div>
      </div>
    </div>

    <!-- Filter -->
    <select
      class="select select-sm min-w-[120px]"
      (change)="onTypeChange($event)"
      *ngIf="showTable"
    >
      <option value="procedure" [selected]="selectedType === 'procedure'">
        Procedures
      </option>
      <option value="function" [selected]="selectedType === 'function'">
        Functions
      </option>
      <option value="view" [selected]="selectedType === 'view'">Views</option>
      <option value="table" [selected]="selectedType === 'table'">
        Tables
      </option>
    </select>
  </div>

  <div class="flex gap-2">
    <button
      class="btn btn-sm"
      (click)="toggleTable()"
      [ngClass]="{
        'btn-outline-primary': !showTable,
        'btn-outline': showTable
      }"
    >
      {{ showTable ? "Hide Table" : "Show Table" }}
    </button>
    <button
      class="btn btn-sm"
      (click)="toggleChart()"
      [ngClass]="{
        'btn-outline-primary': !showChart,
        'btn-outline': showChart
      }"
    >
      {{ showChart ? "Hide Chart" : "Show Chart" }}
    </button>
  </div>
</div>

<div class="flex gap-2 h-[calc(100vh-140px)]">
  <div
    class="rounded-lg border overflow-hidden"
    [ngClass]="{
      'md:w-[40%]': showChart,
      'w-full': !showChart
    }"
    *ngIf="showTable"
  >
    <div class="flex flex-col h-full justify-between">
      <!-- Header -->
      <!-- <div
        class="flex justify-between items-center bg-backgroundPrimary p-2 px-4 rounded-lg"
      >
        <h3 class="text-sm font-medium">Database Objects</h3>
      </div> -->

      <!-- Table -->
      <div class="overflow-y-auto h-[calc(100%-90px)]">
        <table class="table-compact table table-hover min-w-full">
          <thead>
            <tr>
              <th
                *ngFor="let col of columns; let isLast = last"
                class="text-left font-medium text-content1 border-b border-[var(--border-color)] whitespace-nowrap sticky top-0"
                [ngClass]="{ 'border-r': !isLast }"
              >
                <span class="border-none">{{ col.label }}</span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="let item of filteredData"
              [class.selected-row]="item.selected"
              [class.bg-primary-50]="item.selected"
              class="cursor-pointer hover:bg-gray-100"
              (click)="highlightSubGraphFromParent(item.id)"
              title="Click to highlight in chart"
            >
              <td *ngFor="let col of columns; let i = index" class="p-2">
                <span *ngIf="i === 0" class="text-primary">
                  {{ item[col.key] }}
                </span>
                <span *ngIf="i !== 0">{{ item[col.key] }}</span>
              </td>
            </tr>
            <!-- No data placeholder -->
            <tr *ngIf="filteredData.length === 0">
              <td
                [attr.colspan]="columns.length"
                class="text-center p-4 text-gray-500"
              >
                No data available
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div
        *ngIf="filteredData && filteredData.length > 0"
        class="flex flex-col gap-3 justify-between items-center bg-backgroundPrimary p-4 px-2 border-t border-[var(--border-color)]"
      >
        <div
          class="flex flex-row items-center justify-between space-x-4 w-full"
        >
          <div
            class="flex items-center px-3 py-1.5 bg-backgroundPrimary rounded-md border border-[var(--border-color)]"
          >
            <i class="ti ti-list text-primary mr-2"></i>
            <h2 class="text-sm font-medium text-content2 whitespace-nowrap">
              Showing
              <span class="text-content1 font-semibold">{{
                currentPageStart
              }}</span>
              to
              <span class="text-content1 font-semibold">{{
                currentPageEnd
              }}</span>
              of
              <span class="text-content1 font-semibold">{{ totalItems }}</span>
              objects
            </h2>
          </div>
          <div class="relative dropdown" style="width: 120px">
            <div
              class="flex items-center bg-backgroundPrimary rounded-md border border-[var(--border-color)]"
            >
              <select
                class="select select-sm w-full rounded-md bg-backgroundPrimary text-content1 border-0 pl-3 pr-8 transition-all focus:ring-2 focus:ring-primary/20"
                [ngModel]="pageSize"
                (change)="onPageSizeChange($event)"
                style="
                  appearance: none;
                  -webkit-appearance: none;
                  -moz-appearance: none;
                "
              >
                <option *ngFor="let size of pageSizeOptions" [value]="size">
                  {{ size }} per page
                </option>
              </select>
              <div
                class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-content2"
              >
                <i
                  class="ti ti-chevron-down transition-transform text-primary"
                ></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Updated pagination section -->
        <div class="pagination flex items-center">
          <div
            class="flex items-center rounded-md bg-backgroundPrimary border border-[var(--border-color)] p-0.5"
          >
            <button
              class="btn btn-sm h-8 min-h-0 btn-ghost flex items-center text-content2 px-2 transition-all hover:bg-primary/10 hover:text-primary rounded-l-md rounded-r-none border-r border-[var(--border-color)]"
              (click)="prevPage()"
              [disabled]="currentPage === 1"
            >
              <span class="flex items-center"
                ><i class="ti ti-chevron-left mr-1"></i>Prev</span
              >
            </button>

            <div class="flex items-center px-1">
              <ng-container *ngFor="let page of visiblePageNumbers">
                <button
                  *ngIf="page !== '...'; else ellipsis"
                  class="btn btn-sm h-7 min-h-0 w-7 p-0 mx-0.5 transition-all"
                  [ngClass]="{
                    'btn-primary text-white shadow-sm': currentPage === +page,
                    'btn-ghost text-content2 hover:bg-primary/10 hover:text-primary':
                      currentPage !== +page
                  }"
                  (click)="goToPage(+page)"
                >
                  {{ page }}
                </button>

                <ng-template #ellipsis>
                  <span class="px-1 text-content2">...</span>
                </ng-template>
              </ng-container>
            </div>

            <button
              class="btn btn-sm h-8 min-h-0 btn-ghost flex items-center text-content2 px-2 transition-all hover:bg-primary/10 hover:text-primary rounded-r-md rounded-l-none border-l border-[var(--border-color)]"
              (click)="nextPage()"
              [disabled]="currentPage === totalPages"
            >
              <span class="flex items-center"
                >Next <i class="ti ti-chevron-right ml-1"></i
              ></span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    [ngClass]="{
      'md:w-[60%]': showTable,
      'w-full': !showTable
    }"
    *ngIf="showChart"
    class="sticky top-0 h-[calc(100vh-140px)]"
  >
    <div class="w-full h-full" *ngIf="showChart">
      <app-chart #chartComp [nodesData]="nodes" [edgesData]="edges"></app-chart>

      <!-- Summary button - positioned inside the chart area -->
      <div class="absolute top-20 right-4 z-10" *ngIf="selectedNodeId">
        <button class="btn btn-sm btn-primary" (click)="showSummary()">
          <i class="ti ti-file-text mr-1"></i> View Summary
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Summary Modal -->
<input class="modal-state" id="summaryModal" type="checkbox" />
<div class="modal w-screen">
  <label class="modal-overlay"></label>
  <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
    <div class="modal-header bg-backgroundPrimary">
      <h2 class="text-xl">Object Summary</h2>
      <label for="summaryModal" class="modal-close-btn"
        ><i class="ti ti-x"></i
      ></label>
    </div>

    <div class="p-3 py-2 max-h-[60vh] overflow-auto prose prose-sm">
      <div
        *ngIf="isLoadingSummary"
        class="flex flex-col justify-center items-center p-8 gap-4"
      >
        <!-- Colorful loader with gradient ring -->
        <div class="relative">
          <!-- Outer gradient ring with animation -->
          <div
            class="w-20 h-20 rounded-full bg-gradient-to-r from-primary via-secondary to-primary p-1 animate-spin-slow"
          >
            <!-- Inner background -->
            <div
              class="w-full h-full rounded-full bg-backgroundPrimary flex items-center justify-center"
            >
              <!-- Spinner -->
              <div
                class="loading loading-spinner loading-md text-primary"
              ></div>
            </div>
          </div>

          <!-- Decorative elements -->
          <div class="absolute -top-2 -right-2 animate-bounce-slow">
            <div
              class="w-6 h-6 rounded-full bg-secondary flex items-center justify-center text-content1"
            >
              <i class="ti ti-sparkles text-xs"></i>
            </div>
          </div>

          <div class="absolute -bottom-1 -left-1 animate-pulse">
            <div
              class="w-5 h-5 rounded-full bg-primary flex items-center justify-center text-content1"
            >
              <i class="ti ti-brain text-xs"></i>
            </div>
          </div>

          <div class="absolute top-1 -left-2 animate-ping-slow">
            <div
              class="w-4 h-4 rounded-full bg-info flex items-center justify-center text-content1"
            >
              <i class="ti ti-bulb text-xs"></i>
            </div>
          </div>
        </div>

        <!-- Colorful progress indicator -->
        <div
          class="w-48 bg-backgroundSecondary rounded-full h-2.5 mt-2 overflow-hidden"
        >
          <div class="h-full w-full relative">
            <!-- Gradient progress bar -->
            <div
              class="absolute inset-0 bg-gradient-to-r from-primary via-secondary to-info animate-[progress_2s_ease-in-out_infinite]"
            ></div>
            <!-- Shimmer effect -->
            <div
              class="absolute inset-0 bg-gradient-to-r from-transparent via-content1 to-transparent opacity-20 animate-[shimmer_1.5s_ease-in-out_infinite]"
            ></div>
          </div>
        </div>

        <!-- Enhanced status text with gradient -->
        <div class="text-center">
          <div
            class="inline-block bg-gradient-to-r from-primary to-secondary text-transparent bg-clip-text"
          >
            <h3 class="text-xl font-bold">Generating Summary</h3>
          </div>
          <div class="flex items-center justify-center gap-1 mt-1">
            <p class="text-content2 text-sm">Analyzing database object</p>
            <div class="flex">
              <span class="text-primary animate-bounce-slow delay-100">.</span>
              <span class="text-secondary animate-bounce-slow delay-200"
                >.</span
              >
              <span class="text-info animate-bounce-slow delay-300">.</span>
            </div>
          </div>
        </div>

        <!-- Show additional message if loading takes too long -->
        <div
          *ngIf="longLoadingMessage"
          class="mt-2 text-center max-w-md p-3 bg-backgroundSecondary rounded-lg border border-borderColor"
        >
          <div class="flex items-center gap-2 mb-2">
            <i class="ti ti-info-circle text-primary"></i>
            <p class="text-content1 text-sm font-medium">
              AI Processing in Progress
            </p>
          </div>
          <p class="text-content2 text-sm">
            Complex database objects require deeper analysis to generate
            accurate summaries.
          </p>
          <p class="text-content2 text-xs mt-2 opacity-75">
            This may take a few moments to complete.
          </p>
        </div>
      </div>

      <div *ngIf="!isLoadingSummary">
        <div
          *ngIf="summaryError"
          class="flex flex-col items-center p-6 gap-3 text-center"
        >
          <div
            class="w-16 h-16 rounded-full bg-error/10 flex items-center justify-center mb-2"
          >
            <i class="ti ti-alert-triangle text-error text-3xl"></i>
          </div>
          <h3 class="text-lg font-medium text-content1">
            Unable to Generate Summary
          </h3>
          <p class="text-error font-medium">{{ summaryError }}</p>
          <p class="text-content2 text-sm max-w-md">
            We encountered an issue while generating the summary for this
            database object. Please try again or select a different object.
          </p>
          <button
            class="btn btn-sm btn-outline-error mt-2"
            (click)="showSummary()"
          >
            <i class="ti ti-refresh mr-1"></i> Try Again
          </button>
        </div>

        <div *ngIf="!summaryError">
          <h3 class="mb-2 text-content2">{{ selectedObjectName }}</h3>
          <p class="text-sm text-content2 mb-4">
            Type: {{ selectedObjectType }}
          </p>

          <markdown
            *ngIf="objectSummary"
            [data]="objectSummary"
            class="summary-markdown"
          ></markdown>
          <div
            *ngIf="!objectSummary"
            class="flex flex-col items-center p-6 gap-3 text-center"
          >
            <div
              class="w-16 h-16 rounded-full bg-backgroundSecondary flex items-center justify-center mb-2"
            >
              <i class="ti ti-file-question text-content2 text-3xl"></i>
            </div>
            <h3 class="text-lg font-medium text-content1">
              No Summary Available
            </h3>
            <p class="text-content2 text-sm max-w-md">
              We couldn't generate a summary for this database object. This may
              happen with complex or non-standard objects.
            </p>
            <div class="flex gap-2 mt-2">
              <button class="btn btn-sm btn-outline" (click)="showSummary()">
                <i class="ti ti-refresh mr-1"></i> Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="flex gap-3 p-3 border-t justify-end bg-backgroundPrimary">
      <div class="flex gap-3">
        <!-- Copy button with integrated success state -->
        <button
          class="btn btn-sm rounded-md"
          [ngClass]="{
            'btn-secondary': !copySuccess,
            'btn-success': copySuccess,
            'opacity-75': isCopying
          }"
          (click)="copySummary()"
          [disabled]="isCopying || !objectSummary"
        >
          <i
            class="ti mr-1"
            [ngClass]="copySuccess ? 'ti-check' : 'ti-copy'"
          ></i>
          {{ copySuccess ? "Copied!" : "Copy" }}
        </button>
        <label class="btn btn-primary btn-sm rounded-md" for="summaryModal">
          Close
        </label>
      </div>
    </div>
  </div>
</div>

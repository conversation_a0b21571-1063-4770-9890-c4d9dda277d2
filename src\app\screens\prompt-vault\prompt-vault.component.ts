import {
  Component,
  OnInit,
  AfterViewInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { PromptsService } from 'src/app/core/services/prompts/prompts.service';
import { AuthService } from 'src/app/core/auth/auth.service';
import { Location } from '@angular/common';

interface DatabaseOption {
  id: number;
  key: string;
  name: string;
  icon: string;
}

@Component({
  selector: 'app-prompt-vault',
  templateUrl: './prompt-vault.component.html',
  styleUrls: ['./prompt-vault.component.css'],
})
export class PromptVaultComponent implements OnInit, AfterViewInit {
  @ViewChild('statusTemplate') statusTemplate!: TemplateRef<any>;
  @ViewChild('actionTemplate') actionTemplate!: TemplateRef<any>;

  searchQuery: string = '';
  selectedType: string = '';
  types: string[] = ['Procedure', 'Function', 'Trigger', 'View'];

  // User permissions
  isSuperUser: boolean = false;
  isAdmin: boolean = false;
  userPermissions: string[] = [];
  permissionsLoaded: boolean = false;

  // Permission flags
  canCreatePrompt: boolean = false;
  canEditPrompt: boolean = false;
  canDeletePrompt: boolean = false;

  // View details modal
  promptToView: any = null;

  constructor(
    private promptsService: PromptsService,
    private authService: AuthService,
    private location: Location
  ) {}

  currentPage: number = 1;
  pageSize: number = 5;
  pageSizeOptions: number[] = [5, 10, 15];

  tableData: any[] = [];
  loadingData: boolean = false;

  columns = [
    {
      key: 'promptName',
      label: 'Prompt',
      style: { width: '80%', textAlign: 'left' },
    },
    { key: 'type', label: 'Type', style: { width: '10%', textAlign: 'left' } },
    {
      key: 'action',
      label: '',
      custom: true,
      style: { width: '10%', textAlign: 'right' },
    },
  ];

  customTemplates = {
    status: null as TemplateRef<any> | null,
    action: null as TemplateRef<any> | null,
  };

  // Add isEditMode flag to track whether we're editing or creating
  isEditMode: boolean = false;
  // Store the ID of the prompt being edited
  editPromptId: string = '';

  ngOnInit(): void {
    this.updateFilteredDBLists();
    this.loadUserPermissions();
  }

  /**
   * Load user permissions and role information
   */
  loadUserPermissions(): void {
    this.authService.getUserRoleDetails().subscribe({
      next: (roleDetails) => {
        if (roleDetails) {
          // Set superuser status
          this.isSuperUser = roleDetails.is_superuser || false;

          // Check for Admin role
          const roleName =
            roleDetails.role?.name || roleDetails.role?.role || '';
          this.isAdmin = roleName.toLowerCase() === 'admin';

          // Get permissions
          if (roleDetails.role && roleDetails.role.permissions) {
            // Handle permissions in array format (from user-role endpoint)
            if (Array.isArray(roleDetails.role.permissions)) {
              this.userPermissions = roleDetails.role.permissions;
            }
            // Handle permissions in object format (for backward compatibility)
            else {
              this.userPermissions = Object.entries(
                roleDetails.role.permissions
              )
                .filter(([_, value]) => value === true)
                .map(([key, _]) => key);
            }
          }

          // Check specific permissions
          this.canCreatePrompt =
            this.isSuperUser ||
            this.isAdmin ||
            this.userPermissions.includes('prompt_vault_create');

          this.canEditPrompt =
            this.isSuperUser ||
            this.isAdmin ||
            this.userPermissions.includes('prompt_vault_edit');

          this.canDeletePrompt =
            this.isSuperUser ||
            this.isAdmin ||
            this.userPermissions.includes('prompt_vault_delete');

          // Mark permissions as loaded
          this.permissionsLoaded = true;

          console.log('User permissions loaded:', {
            isSuperUser: this.isSuperUser,
            isAdmin: this.isAdmin,
            permissions: this.userPermissions,
            canCreatePrompt: this.canCreatePrompt,
            canEditPrompt: this.canEditPrompt,
            canDeletePrompt: this.canDeletePrompt,
            permissionsLoaded: this.permissionsLoaded,
          });

          // Now that permissions are loaded, load prompts
          this.loadPrompts();
        }
      },
      error: (err) => {
        console.error('Error loading user permissions:', err);
        // Fallback to getUserDetails if getUserRoleDetails fails
        this.fallbackToUserDetails();
      },
    });
  }

  /**
   * Fallback to getUserDetails if getUserRoleDetails fails
   */
  fallbackToUserDetails(): void {
    this.authService.getUserDetails().subscribe({
      next: (user) => {
        if (user) {
          // Set superuser status
          this.isSuperUser = user.is_superuser || false;

          // Check for Admin role
          const roleName = user.role?.name || '';
          this.isAdmin = roleName.toLowerCase() === 'admin';

          // Get permissions
          if (user.role && user.role.permissions) {
            // Handle permissions in array format
            if (Array.isArray(user.role.permissions)) {
              this.userPermissions = user.role.permissions;
            }
            // Handle permissions in object format
            else {
              this.userPermissions = Object.entries(user.role.permissions)
                .filter(([_, value]) => value === true)
                .map(([key, _]) => key);
            }
          }

          // Check specific permissions
          this.canCreatePrompt =
            this.isSuperUser ||
            this.isAdmin ||
            this.userPermissions.includes('prompt_vault_create');

          this.canEditPrompt =
            this.isSuperUser ||
            this.isAdmin ||
            this.userPermissions.includes('prompt_vault_edit');

          this.canDeletePrompt =
            this.isSuperUser ||
            this.isAdmin ||
            this.userPermissions.includes('prompt_vault_delete');

          // Mark permissions as loaded
          this.permissionsLoaded = true;

          console.log('User permissions loaded (fallback):', {
            isSuperUser: this.isSuperUser,
            isAdmin: this.isAdmin,
            permissions: this.userPermissions,
            canCreatePrompt: this.canCreatePrompt,
            canEditPrompt: this.canEditPrompt,
            canDeletePrompt: this.canDeletePrompt,
            permissionsLoaded: this.permissionsLoaded,
          });

          // Now that permissions are loaded, load prompts
          this.loadPrompts();
        }
      },
      error: (err) => {
        console.error('Error loading user details:', err);
        // Even if permissions fail to load, still load prompts for viewing
        this.permissionsLoaded = true;
        this.loadPrompts();
      },
    });
  }

  /**
   * Check if the user has a specific permission
   * @param permission The permission to check
   * @returns True if the user has the permission, false otherwise
   */
  hasPermission(permission: string): boolean {
    return (
      this.isSuperUser ||
      this.isAdmin ||
      this.userPermissions.includes(permission)
    );
  }

  /**
   * Navigate back to the previous page
   */
  goBack(): void {
    this.location.back();
  }

  ngAfterViewInit(): void {
    this.customTemplates = {
      status: this.statusTemplate,
      action: this.actionTemplate,
    };
  }

  /**
   * Handle filter changes (type selection or search)
   * Reset to first page and reload data
   */
  onFilterChange(): void {
    this.currentPage = 1; // Reset to first page
    this.loadPrompts(); // Reload with new filters
  }

  get filteredTableData() {
    // We're now filtering on the server side, so just return the data
    return this.tableData;
  }

  get paginatedTableData() {
    // Use the filtered data directly without additional pagination
    // since we're already paginating on the server side
    return this.filteredTableData;
  }

  get currentPageStart(): number {
    return this.totalEntries === 0
      ? 0
      : (this.currentPage - 1) * this.pageSize + 1;
  }

  get currentPageEnd(): number {
    return Math.min(this.currentPage * this.pageSize, this.totalEntries);
  }

  get totalPages() {
    return Math.ceil(this.totalEntries / this.pageSize);
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.loadPrompts();
    }
  }

  prevPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.loadPrompts();
    }
  }

  changePageSize(event: Event) {
    const target = event.target as HTMLSelectElement;
    this.pageSize = parseInt(target.value, 10);
    this.currentPage = 1;
    this.loadPrompts();
  }

  /**
   * Gets an array of all page numbers for small paginations
   */
  getPagesArray(): number[] {
    return Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }

  /**
   * Gets the visible page numbers for pagination
   * Shows pages around the current page, first and last pages
   */
  getVisiblePages(): number[] {
    const pages: number[] = [];
    const startPage = Math.max(1, this.currentPage - 1);
    const endPage = Math.min(this.totalPages, this.currentPage + 1);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  /**
   * Navigate to a specific page
   */
  goToPage(page: number) {
    if (page !== this.currentPage) {
      this.currentPage = page;
      this.loadPrompts(); // Reload data when page changes
    }
  }

  onRowClicked(rowData: any) {
    console.log('Row clicked:', rowData);
    // Open the view details modal when a row is clicked
    this.viewPromptDetails(rowData);
  }

  // Modified to populate the form and open the modal in edit mode
  editPrompt(item: any) {
    // Check if user has permission to edit prompts
    if (!this.canEditPrompt) {
      console.warn('User does not have permission to edit prompts');
      return;
    }

    console.log('Edit prompt:', item);
    // Set edit mode flag
    this.isEditMode = true;
    // Store the ID for update operation
    this.editPromptId = item.id;

    // Populate the form with existing data
    this.newPrompt = {
      title: item.promptName,
      prompt_type: item.type,
      source_db_type: item.source,
      target_db_type: item.target,
      description: item.description,
    };

    // Update filter lists for the dropdown
    this.updateFilteredDBLists();

    // Open the modal
    (document.getElementById('newPrompt') as HTMLInputElement).checked = true;
  }

  deletePrompt(item: any) {
    console.log('Delete prompt:', item);
  }

  loading: boolean = false;
  formSubmitted = false;

  newPrompt: any = {
    title: '',
    prompt_type: '',
    source_db_type: '',
    target_db_type: '',
    description: '',
  };

  DBList: DatabaseOption[] = [
    { id: 1, key: 'sybase', name: 'Sybase', icon: 'assets/sybase.png' },
    {
      id: 2,
      key: 'postgresql',
      name: 'PostgreSQL',
      icon: 'assets/postgrace.png',
    },
    {
      id: 3,
      key: 'sqlserver',
      name: 'SQL Server',
      icon: 'assets/sql server.png',
    },
    { id: 4, key: 'mysql', name: 'MySQL', icon: 'assets/mysql.png' },
    {
      id: 5,
      key: 'amazon_rds_pg',
      name: 'Amazon RDS PostgreSQL',
      icon: 'assets/rds.png',
    },
    {
      id: 6,
      key: 'amazon_athena',
      name: 'Amazon Athena',
      icon: 'assets/amzathena.png',
    },
    { id: 7, key: 'oracle', name: 'Oracle', icon: 'assets/oracle.png' },
  ];

  filteredSourceDBList: DatabaseOption[] = [];
  filteredTargetDBList: DatabaseOption[] = [];

  updateFilteredDBLists() {
    this.filteredSourceDBList = this.DBList.filter(
      (db) => db.key !== this.newPrompt.target_db_type
    );
    this.filteredTargetDBList = this.DBList.filter(
      (db) => db.key !== this.newPrompt.source_db_type
    );
  }

  isFormValid(): boolean {
    return (
      this.newPrompt.title?.trim() &&
      this.newPrompt.description?.trim() &&
      this.newPrompt.prompt_type &&
      this.newPrompt.source_db_type &&
      this.newPrompt.target_db_type
    );
  }

  resetForm() {
    this.newPrompt = {
      title: '',
      prompt_type: '',
      source_db_type: '',
      target_db_type: '',
      description: '',
    };
    this.isEditMode = false;
    this.editPromptId = '';
  }

  // Modified to handle both create and update operations
  savePrompt() {
    this.formSubmitted = true;

    if (!this.isFormValid()) return;

    this.loading = true;

    // Get organization ID from localStorage
    const organization_id = localStorage.getItem('organization_id') || '';

    const payload = {
      title: this.newPrompt.title.trim(),
      prompt_type: this.newPrompt.prompt_type,
      source_db_type: this.newPrompt.source_db_type,
      target_db_type: this.newPrompt.target_db_type,
      description: this.newPrompt.description.trim(),
      organization_id: organization_id,
    };

    if (this.isEditMode) {
      // Update existing prompt
      this.promptsService.updatePrompt(this.editPromptId, payload).subscribe({
        next: (res) => {
          console.log('Prompt updated:', res);
          this.resetForm();
          this.loadPrompts();
          (document.getElementById('newPrompt') as HTMLInputElement).checked =
            false;
          this.loading = false;
          this.formSubmitted = false;
        },
        error: (err) => {
          console.error('Prompt update failed:', err);
          this.loading = false;
        },
      });
    } else {
      // Create new prompt
      this.promptsService.createPrompt(payload).subscribe({
        next: (res) => {
          console.log('Prompt created:', res);
          this.resetForm();
          this.loadPrompts();
          (document.getElementById('newPrompt') as HTMLInputElement).checked =
            false;
          this.loading = false;
          this.formSubmitted = false;
        },
        error: (err) => {
          console.error('Prompt create failed:', err);
          this.loading = false;
        },
      });
    }
  }

  // Add a method to open the modal in create mode
  openCreateModal() {
    // Check if user has permission to create prompts
    if (!this.canCreatePrompt) {
      console.warn('User does not have permission to create prompts');
      return;
    }

    this.resetForm();
    (document.getElementById('newPrompt') as HTMLInputElement).checked = true;
  }

  totalEntries: number = 0;
  loadPrompts() {
    // Only load prompts if permissions are loaded
    if (!this.permissionsLoaded) {
      // If permissions aren't loaded yet, wait for them
      setTimeout(() => this.loadPrompts(), 500);
      return;
    }

    this.loadingData = true;

    // Calculate pagination parameters
    const skip = (this.currentPage - 1) * this.pageSize;
    const limit = this.pageSize;

    // Optional filter parameters
    const type = this.selectedType || undefined;
    const search = this.searchQuery || undefined;

    // Get organization ID from localStorage
    const organization_id =
      localStorage.getItem('organization_id') || undefined;

    // 1. Load paginated data with filters
    this.promptsService
      .getPrompts(type, search, skip, limit, organization_id)
      .subscribe({
        next: (res) => {
          const promptList = res ?? [];

          this.tableData = promptList.map((item: any) => ({
            ...item,
            promptName: item.title,
            type: item.prompt_type,
            description: item.description,
            source: item.source_db_type,
            target: item.target_db_type,
            status: 'Active',
          }));
          this.loadingData = false;
        },
        error: (err) => {
          console.error('Paginated data error:', err);
          this.loadingData = false;
        },
      });

    // 2. Load total count with the same filters
    this.promptsService
      .getPrompts(type, search, 0, 10000, organization_id)
      .subscribe({
        next: (res) => {
          this.totalEntries = res?.length ?? 0;

          // If current page is now beyond the total pages, go to the last page
          if (this.currentPage > this.totalPages && this.totalPages > 0) {
            this.currentPage = this.totalPages;
            this.loadPrompts();
          }
        },
        error: (err) => console.error('Total count error:', err),
      });
  }

  showDeleteModal: boolean = false;
  promptToDelete: any = null;

  openDeleteModal(item: any) {
    // Check if user has permission to delete prompts
    if (!this.canDeletePrompt) {
      console.warn('User does not have permission to delete prompts');
      return;
    }

    this.promptToDelete = item;
    this.showDeleteModal = true;
    console.log(this.promptToDelete);
  }

  confirmDeletePrompt() {
    if (!this.promptToDelete) return;

    this.promptsService.deletePrompt(this.promptToDelete.id).subscribe({
      next: () => {
        // Optional: toast or alert
        this.loadPrompts(); // Refresh the list
      },
      error: (err) => {
        console.error('Error deleting prompt:', err);
      },
      complete: () => {
        this.showDeleteModal = false;
        this.promptToDelete = null;
      },
    });
  }

  // Add this method to your component class
  limitCharacters(): void {
    if (this.newPrompt.description && this.newPrompt.description.length > 600) {
      this.newPrompt.description = this.newPrompt.description.substring(0, 600);
    }
  }

  // Get the appropriate CSS class for character count
  getCharacterCountClass(): string {
    const length = this.newPrompt.description?.length || 0;
    if (length > 275) return 'exceeded text-error';
    if (length > 250) return 'warning text-warning';
    return 'text-content2';
  }

  /**
   * Open the view details modal for a prompt
   * @param item The prompt to view
   */
  viewPromptDetails(item: any): void {
    this.promptToView = item;
    // Use the checkbox-based modal pattern
    (document.getElementById('viewPrompt') as HTMLInputElement).checked = true;
  }
}

import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/core/auth/auth.service';

@Component({
  selector: 'app-not-found',
  templateUrl: './not-found.component.html',
  styleUrls: ['./not-found.component.css'],
})
export class NotFoundComponent {
  constructor(private router: Router, private authService: AuthService) {}

  goHome() {
    if (this.authService.isLoggedIn()) {
      this.router.navigate(['/dashboard']); // Redirect logged-in users to dashboard
    } else {
      this.router.navigate(['/']); // Redirect guests to login
    }
  }
}

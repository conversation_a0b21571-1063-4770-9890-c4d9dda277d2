import { Component, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from './core/auth/auth.service';
import { fadeIn } from './shared/animations/animations';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  animations: [fadeIn],
})
export class AppComponent {
  constructor(private router: Router, private authService: AuthService) {}
  title = 'revmigrate';
  tologin() {
    this.router.navigate(['/auth/login']);
  }

  @HostListener('document:mousemove')
  @HostListener('document:keydown')
  resetUserActivity() {
    this.authService.resetTimeout();
  }
}

import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, retry, tap } from 'rxjs/operators';
import { EnvironmentService } from '../environment.service';

@Injectable({
  providedIn: 'root',
})
export class DatabaseService {
  private apiBaseUrl: string;
  private wsBaseUrl: string;

  constructor(
    private http: HttpClient,
    private envService: EnvironmentService
  ) {
    // Get the API URLs from the environment service
    this.apiBaseUrl = this.envService.apiBaseUrl;
    this.wsBaseUrl = this.envService.apiBaseUrl
      .replace('http:', 'wss:')
      .replace('https:', 'wss:');

    // Log the URL in production for debugging
    if (this.envService.isProduction) {
      console.log('Database Service API URL:', this.apiBaseUrl);
    }
  }

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders({ Authorization: `Bearer ${token}` });
  }

  // API to create a new database connection
  createDbConfig(connectionDetails: any): Observable<any> {
    const url = `${this.apiBaseUrl}/multiDBConfig/dbconfig/`;
    return this.http
      .post(url, connectionDetails, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        catchError((error) => {
          console.error('Error creating DB config:', error);
          return throwError(() => error);
        })
      );
  }

  // API to fetch all database connections
  getDatabases(orgId: string): Observable<any> {
    const url = `${this.apiBaseUrl}/multiDBConfig/dbconfig/`;
    return this.http
      .get(url, {
        headers: this.getAuthHeaders(),
        params: { organization_id: orgId },
      })
      .pipe(
        catchError((error) => {
          console.error('Error fetching databases:', error);
          return throwError(() => error);
        })
      );
  }

  // API to fetch database objects analytics
  getDatabaseAnalytics(
    database_id: string,
    DB_MainConnection: string
  ): Observable<any> {
    const user_id: any = localStorage.getItem('user_id');
    const url = `${this.apiBaseUrl}/database-objects/${database_id}/analytics`;
    return this.http
      .get(url, {
        headers: this.getAuthHeaders(),
        params: { database_id, DB_MainConnection, user_id }, // Fixed params
      })
      .pipe(
        catchError((error) => {
          console.error('Error fetching database analytics:', error);
          return throwError(() => error);
        })
      );
  }

  getDbIdAnalytics(): Observable<any> {
    const user_id = localStorage.getItem('user_id');
    const activeDbInfo = JSON.parse(
      localStorage.getItem('activeDatabaseInfo') || '{}'
    );
    const database_id = activeDbInfo?.id;

    if (!database_id) {
      console.error('No active database ID found in localStorage');
      return of({ error: 'No active database ID found' });
    }

    const url = `${this.apiBaseUrl}/analytics/summary/${database_id}`;

    return this.http
      .get(url, {
        headers: this.getAuthHeaders(),
        params: { user_id: user_id || '' },
      })
      .pipe(
        catchError((error) => {
          console.error('Error fetching DB ID analytics:', error);
          return throwError(() => error);
        })
      );
  }

  // UPDATED: API method to fetch database elements with pagination and filtering
  // Update the getDatabaseElements method with proper typing
  getDatabaseElements(
    database_id: string,
    page: number = 1,
    pageSize: number = 50,
    nodeType: string = ''
  ): Observable<any> {
    const url = `${this.apiBaseUrl}/diagrams/elements/${database_id}`;

    // Create params object with the updated parameters
    const params: any = {
      page: page.toString(),
      page_size: pageSize.toString(),
    };

    // Only add node_type if it's not empty
    if (nodeType) {
      params.node_type = nodeType;
    }

    return this.http
      .get(url, {
        headers: this.getAuthHeaders(),
        params,
      })
      .pipe(
        map((response: any) => {
          // Use 'any' type here to bypass TypeScript checking
          // If the response has elements.nodes and elements.edges, restructure it
          if (response?.elements) {
            return {
              nodes: response.elements.nodes || [],
              edges: response.elements.edges || [],
              pagination: response.pagination,
            };
          }
          // If it's already in the right format, return as is
          else if (response?.nodes && response?.edges) {
            return response;
          }
          // Otherwise return empty arrays
          else {
            console.warn('Unexpected API response format:', response);
            return {
              nodes: [],
              edges: [],
              pagination: response?.pagination || {
                total_nodes: 0,
                total_pages: 1,
                current_page: page,
                page_size: pageSize,
                has_next: false,
                has_previous: false,
              },
            };
          }
        }),
        catchError((error) => {
          console.error('Error in getDatabaseElements API call:', error);
          return of({
            nodes: [],
            edges: [],
            pagination: {
              total_nodes: 0,
              total_pages: 1,
              current_page: page,
              page_size: pageSize,
              has_next: false,
              has_previous: false,
            },
          });
        })
      );
  }

  // UPDATED: API to fetch documents by organization and database
  getDocumentsByUserAndDatabase(database_id?: string): Observable<any> {
    const organization_id = localStorage.getItem('organization_id');

    if (!organization_id) {
      console.error('No organization_id found in localStorage');
      return of({ documents: [] });
    }

    // If no database_id is provided, try to get it from localStorage

    const url = `${this.apiBaseUrl}/documents/get-documents`;

    return this.http
      .get(url, {
        headers: this.getAuthHeaders(),
        params: { organization_id },
      })
      .pipe(
        tap((response) => console.log('Documents API response:', response)),
        catchError((error) => {
          console.error('Error fetching documents:', error);
          return of({ documents: [] });
        })
      );
  }

  // ✅ API: Generate Documentation (Accepts `database_id` & `procedure_name`)
  generateDocumentation(
    database_id: string,
    procedure_name: string
  ): Observable<any> {
    const url = `${this.apiBaseUrl}/documentation/generate/`;
    const payload = { database_id, procedure_name };

    return this.http
      .post(url, payload, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        catchError((error) => {
          console.error('Error generating documentation:', error);
          return throwError(() => error);
        })
      );
  }

  getDocumentation(databaseId: string, procedureName: string): void {
    const url = `${this.apiBaseUrl}/documentation/document/${databaseId}/${procedureName}`;

    this.http
      .get<any>(url, {
        headers: this.getAuthHeaders(), // Ensure auth headers are included
      })
      .subscribe({
        next: (response) => {
          if (response && response.document_base64) {
            this.downloadPdf(response.document_base64, response.procedure_name);
          } else {
            console.error('No document found');
          }
        },
        error: (error) => {
          console.error('Error fetching documentation:', error);
        },
      });
  }

  downloadPdf(base64String: string, fileName: string): void {
    try {
      const binaryData = atob(base64String);
      const arrayBuffer = new Uint8Array(binaryData.length);

      for (let i = 0; i < binaryData.length; i++) {
        arrayBuffer[i] = binaryData.charCodeAt(i);
      }

      const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
      const link = document.createElement('a');

      link.href = URL.createObjectURL(blob);
      link.download = `${fileName}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading PDF:', error);
    }
  }

  // WebSocket connection to get live analytics
  connectToWebSocket(databaseId: string): WebSocket {
    const wsUrl = `${this.wsBaseUrl}/multiDBConfig/ws/analyze_dbconfig`;
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      const token = localStorage.getItem('token');
      if (token) {
        ws.send(token);
        console.log('Sent token to WebSocket');

        setTimeout(() => {
          ws.send(JSON.stringify({ database_id: databaseId }));
          console.log('Sent database_id to WebSocket');
        }, 1000);
      } else {
        console.error('No token found in localStorage.');
      }
    };

    ws.onmessage = (event) => {
      try {
        const jsonData = JSON.parse(event.data);
        console.log('Received WebSocket JSON:', jsonData);
      } catch {
        console.log('Received WebSocket Text:', event.data);
      }
    };

    ws.onerror = (error) => console.error('WebSocket Error:', error);

    ws.onclose = (event) => {
      console.warn('WebSocket Closed:', event.code, event.reason);
      setTimeout(() => this.connectToWebSocket(databaseId), 5000); // Auto-reconnect after 5s
    };

    window.onbeforeunload = () => ws.close();

    return ws;
  }

  transformCode(
    databaseId: string,
    objects: Array<{ name: string; type: string }>,
    transformToApi: boolean,
    transformToRdbms: boolean,
    targetDbType: string,
    prompts: string[]
  ): Observable<any> {
    const requestBody = {
      database_id: databaseId,
      objects: objects,
      transform_to_api: transformToApi,
      transform_to_rdbms: transformToRdbms,
      target_db_type: targetDbType,
      prompts: prompts,
    };

    const url = `${this.apiBaseUrl}/code-transformation/transform/`;

    return this.http
      .post<any>(url, requestBody, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        catchError((error) => {
          console.error('Error transforming code:', error);
          return throwError(() => error);
        })
      );
  }

  // API to fetch database statistics for dashboard
  getDatabaseStats(): Observable<any> {
    const url = `${this.apiBaseUrl}/dashboard/database-stats`;
    return this.http
      .get(url, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        catchError((error) => {
          console.error('Error fetching database stats:', error);
          return throwError(() => error);
        })
      );
  }

  getTransformationResult(
    databaseId: string,
    objectType: string,
    objectName: string
  ): Observable<any> {
    const url = `${this.apiBaseUrl}/code-transformation/results/${databaseId}/${objectType}/${objectName}`;
    return this.http.get<any>(url, { headers: this.getAuthHeaders() }).pipe(
      catchError((error) => {
        console.error('Error fetching transformation result:', error);
        return throwError(() => error);
      })
    );
  }

  // UPDATED: API method to get diagram node types with pagination and filtering
  getDiagramNodeTypes(
    nodeType: string = '',
    page: number = 1,
    pageSize: number = 50
  ): Observable<any> {
    const organization_id = localStorage.getItem('organization_id');
    const activeDbInfo = localStorage.getItem('activeDatabaseInfo');
    const database_id = activeDbInfo ? JSON.parse(activeDbInfo).id : null;

    if (!database_id) {
      console.error('Missing database_id in localStorage');
      return of({
        data: {
          procedure: [],
          function: [],
          view: [],
          table: [],
        },
        pagination: {
          total_nodes: 0,
          total_pages: 1,
          current_page: 1,
          page_size: pageSize,
          has_next: false,
          has_previous: false,
          type_counts: {
            procedure: 0,
            function: 0,
            view: 0,
            table: 0,
          },
        },
      });
    }

    const url = `${this.apiBaseUrl}/diagrams/types/${database_id}`;

    // Create params object with the updated parameters
    const params: any = {
      page: page.toString(),
      page_size: pageSize.toString(),
    };

    // Add organization_id if available
    if (organization_id) {
      params.organization_id = organization_id;
    }

    // Only add node_type if it's not empty
    if (nodeType) {
      params.node_type = nodeType;
    }

    console.log(`Making request to ${url} with params:`, params);

    return this.http
      .get<{
        data: {
          procedure: Array<{ id: string; label: string }>;
          function: Array<{ id: string; label: string }>;
          view: Array<{ id: string; label: string }>;
          table: Array<{ id: string; label: string }>;
        };
        pagination: {
          total_nodes: number;
          total_pages: number;
          current_page: number;
          page_size: number;
          has_next: boolean;
          has_previous: boolean;
          type_counts: {
            procedure: number;
            function: number;
            view: number;
            table: number;
          };
        };
      }>(url, {
        headers: this.getAuthHeaders(),
        params,
      })
      .pipe(
        // Retry once if there's a network error
        retry(1),
        tap((response) => {
          console.log('Diagram node types API response:', response);
        }),
        catchError((error) => {
          console.error('Error in getDiagramNodeTypes API call:', error);
          // Return a default empty response instead of propagating the error
          return of({
            data: {
              procedure: [],
              function: [],
              view: [],
              table: [],
            },
            pagination: {
              total_nodes: 0,
              total_pages: 1,
              current_page: 1,
              page_size: pageSize,
              has_next: false,
              has_previous: false,
              type_counts: {
                procedure: 0,
                function: 0,
                view: 0,
                table: 0,
              },
            },
          });
        })
      );
  }

  generateDependencyChart(databaseId: string): Observable<any> {
    const url = `${this.apiBaseUrl}/database-visualization/dependency-chart/${databaseId}`;

    return this.http
      .post(
        url,
        {},
        {
          headers: this.getAuthHeaders(),
        }
      )
      .pipe(
        catchError((error) => {
          console.error('Error generating dependency chart:', error);
          return throwError(() => error);
        })
      );
  }

  // NEW: API to update database connection status
  updateConnectionStatus(dbId: string, status: string): Observable<any> {
    const url = `${this.apiBaseUrl}/multiDBConfig/dbconfig/${dbId}/status`;

    return this.http
      .patch(
        url,
        { connection_status: status },
        {
          headers: this.getAuthHeaders(),
        }
      )
      .pipe(
        catchError((error) => {
          console.error('Error updating connection status:', error);
          return throwError(() => error);
        })
      );
  }

  // NEW: API to toggle database connection status
  toggleConnectionStatus(dbId: string): Observable<any> {
    const url = `${this.apiBaseUrl}/multiDBConfig/dbconfig/${dbId}/toggle-connection-status`;

    return this.http
      .put(
        url,
        {},
        {
          headers: this.getAuthHeaders(),
        }
      )
      .pipe(
        catchError((error) => {
          console.error('Error toggling connection status:', error);
          return throwError(() => error);
        })
      );
  }

  // API to get a single database connection by ID
  getDatabaseById(dbId: string): Observable<any> {
    const url = `${this.apiBaseUrl}/multiDBConfig/dbconfig/${dbId}`;

    return this.http
      .get(url, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        catchError((error) => {
          console.error('Error fetching database connection:', error);
          return throwError(() => error);
        })
      );
  }

  // API to update a database connection
  updateDatabase(dbId: string, connectionDetails: any): Observable<any> {
    const url = `${this.apiBaseUrl}/multiDBConfig/dbconfig/${dbId}`;

    return this.http
      .put(url, connectionDetails, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        catchError((error) => {
          console.error('Error updating database connection:', error);
          return throwError(() => error);
        })
      );
  }

  // API to delete a database connection
  deleteDatabase(dbId: string): Observable<any> {
    const url = `${this.apiBaseUrl}/multiDBConfig/dbconfig/${dbId}`;

    return this.http
      .delete(url, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        catchError((error) => {
          console.error('Error deleting database connection:', error);
          return throwError(() => error);
        })
      );
  }

  // NEW: API to refresh database analytics
  refreshDatabaseAnalytics(dbId: string): Observable<any> {
    const url = `${this.apiBaseUrl}/database-objects/${dbId}/refresh`;

    return this.http
      .post(
        url,
        {},
        {
          headers: this.getAuthHeaders(),
        }
      )
      .pipe(
        catchError((error) => {
          console.error('Error refreshing database analytics:', error);
          return throwError(() => error);
        })
      );
  }

  // NEW: API to get connection status counts
  getConnectionStatusCounts(): Observable<any> {
    const user_id = localStorage.getItem('user_id');
    if (!user_id) {
      console.error('No user_id found in localStorage');
      return of({ connected: 0, disconnected: 0 });
    }

    const url = `${this.apiBaseUrl}/dashboard/connection-status/${user_id}`;

    return this.http
      .get(url, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        catchError((error) => {
          console.error('Error fetching connection status counts:', error);
          return of({ connected: 0, disconnected: 0 });
        })
      );
  }

  regenerateTransformedCode(
    databaseId: string,
    objectName: string,
    objectType: string,
    regenerateApi: boolean = false,
    regenerateRdbms: boolean = false,
    targetDbType: string = '',
    prompts: string[] = []
  ): Observable<any> {
    const url = `${this.apiBaseUrl}/code-transformation/regenerate/`;

    const requestBody = {
      database_id: databaseId,
      object_name: objectName,
      object_type: objectType,
      regenerate_api: regenerateApi,
      regenerate_rdbms: regenerateRdbms,
      target_db_type: targetDbType,
      prompts: prompts,
    };

    return this.http
      .post<any>(url, requestBody, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        catchError((error) => {
          console.error('Error regenerating transformed code:', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Get or generate a summary for a database object
   * @param database_id The UUID of the database
   * @param object_name The name of the database object
   * @param object_type The type of the database object (e.g., 'procedure')
   * @returns Observable with the summary response
   */
  getOrGenerateSummary(
    database_id: string,
    object_name: string,
    object_type: string
  ): Observable<any> {
    const url = `${this.apiBaseUrl}/documentation/summary/`;

    const payload = {
      database_id,
      object_name,
      object_type,
    };

    return this.http
      .post<any>(url, payload, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        catchError((error) => {
          console.error('Error getting or generating summary:', error);
          return throwError(() => error);
        })
      );
  }
}

import { RouterModule, Routes } from '@angular/router';
import { NotFoundComponent } from './screens/not-found/not-found.component';
import { NgModule } from '@angular/core';
import { LayoutComponent } from './layout/layout/layout.component';
import { AuthComponent } from './screens/auth/auth/auth.component';
import { AuthGuard } from './screens/auth/auth.guard';
import { AdminGuard } from './core/guards/admin.guard';

const routes: Routes = [
  { path: '', component: AuthComponent, canActivate: [AuthGuard] }, // Public login page

  {
    path: '',
    component: LayoutComponent,
    canActivate: [AuthGuard], // Protects all child routes
    children: [
      {
        path: 'dashboard',
        loadChildren: () =>
          import('./screens/dashboard/dashboard.module').then(
            (m) => m.DashboardModule
          ),
      },
      {
        path: 'settings',
        loadChildren: () =>
          import('./screens/settings/settings.module').then(
            (m) => m.SettingsModule
          ),
      },
      {
        path: 'database',
        loadChildren: () =>
          import('./screens/database/database.module').then(
            (m) => m.DatabaseModule
          ),
      },
      {
        path: 'profile',
        loadChildren: () =>
          import('./screens/profile/profile.module').then(
            (m) => m.ProfileModule
          ),
      },
      {
        path: 'prompt-vault',
        loadChildren: () =>
          import('./screens/prompt-vault/prompt-vault.module').then(
            (m) => m.PromptVaultModule
          ),
      },
      {
        path: 'admin',
        loadChildren: () =>
          import('./screens/user-management/user-management.module').then(
            (m) => m.UserManagementModule
          ),
        canActivate: [AdminGuard], // Only allow superusers or admin role
      },
      {
        path: 'doc-hub',
        loadChildren: () =>
          import('./screens/doc-hub/doc-hub.module').then(
            (m) => m.DocHubModule
          ),
      },
      {
        path: 'help',
        loadChildren: () =>
          import('./screens/help/help.module').then((m) => m.HelpModule),
      },
      {
        path: 'chat-bot',
        loadChildren: () =>
          import('./screens/chat-bot/chat-bot.module').then(
            (m) => m.ChatBotModule
          ),
      },

      {
        path: 'not-found', // Keep 404 inside authenticated layout
        component: NotFoundComponent,
      },
      {
        path: 'not-authorized', // Access denied page
        loadChildren: () =>
          import('./screens/not-authorized/not-authorized.module').then(
            (m) => m.NotAuthorizedModule
          ),
      },
      { path: '**', redirectTo: 'not-found' }, // Redirect unknown routes to 404
    ],
  },
  {
    path: 'database',
    loadChildren: () =>
      import('./screens/database/database.module').then(
        (m) => m.DatabaseModule
      ),
  },

  { path: '**', redirectTo: '' }, // If not logged in, send to login
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}

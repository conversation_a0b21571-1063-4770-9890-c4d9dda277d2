import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  TemplateRef,
} from '@angular/core';
import {
  trigger,
  transition,
  style,
  animate,
  query,
  stagger,
} from '@angular/animations';

interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
}

interface TableRow {
  [key: string]: any;
  selected?: boolean;
  status?: 'success' | 'warning' | 'error';
}

@Component({
  selector: 'app-table',
  templateUrl: './table.component.html',
  styleUrls: ['./table.component.css'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms ease-in', style({ opacity: 1 })),
      ]),
    ]),
    trigger('listAnimation', [
      transition('* => *', [
        query(
          ':enter',
          [
            style({ opacity: 0, transform: 'translateY(10px)' }),
            stagger('60ms', [
              animate(
                '300ms ease-out',
                style({ opacity: 1, transform: 'translateY(0)' })
              ),
            ]),
          ],
          { optional: true }
        ),
      ]),
    ]),
  ],
})
export class TableComponent {
  @Input() columns: { key: string; label: string; custom?: boolean }[] = [];
  @Input() tableData: any[] = [];

  @Input() customTemplates: { [key: string]: TemplateRef<any> | null } = {};
  @Input() enableNavigation: boolean = false;

  @Output() rowClicked = new EventEmitter<{
    DB_MainConnection: string;
    database_id: string;
    dbType: string;
  }>();

  onRowClick(DB_MainConnection: string, database_id: string, dbType: string) {
    if (this.enableNavigation) {
      this.rowClicked.emit({ DB_MainConnection, database_id, dbType });
    }
  }

  selectedCount = 0;
  sortColumn: string = '';
  sortAsc = true;

  pageSizeOptions = [5, 10, 15];
  pageSize = 5;
  currentPage = 1;

  get totalEntries(): number {
    return this.tableData.length;
  }

  get currentPageStart(): number {
    return (this.currentPage - 1) * this.pageSize + 1;
  }

  get currentPageEnd(): number {
    return Math.min(
      this.currentPageStart + this.pageSize - 1,
      this.totalEntries
    );
  }

  get totalPages(): number {
    return Math.ceil(this.totalEntries / this.pageSize);
  }

  get paginatedData(): any[] {
    const start = (this.currentPage - 1) * this.pageSize;
    return this.tableData.slice(start, start + this.pageSize);
  }

  selectAll(event: any) {
    const checked = event.target.checked;
    this.tableData.forEach((item) => (item.selected = checked));
    this.updateSelection();
  }

  updateSelection() {
    this.selectedCount = this.tableData.filter((item) => item.selected).length;
  }

  sort(column: string) {
    this.sortAsc = this.sortColumn === column ? !this.sortAsc : true;
    this.sortColumn = column;
    this.tableData.sort(
      (a, b) => (a[column] > b[column] ? 1 : -1) * (this.sortAsc ? 1 : -1)
    );
  }

  prevPage() {
    if (this.currentPage > 1) this.currentPage--;
  }

  nextPage() {
    if (this.currentPage < this.totalPages) this.currentPage++;
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  changePageSize(event: any) {
    this.pageSize = event.target.value;
    this.currentPage = 1;
  }
  /** Fix: Remove console log to prevent infinite logs */
  getStatusClass(status: string): string {
    switch (status) {
      case 'success':
        return 'dot-success';
      case 'warning':
        return 'dot-warning';
      case 'error':
        return 'dot-error';
      default:
        return '';
    }
  }
}

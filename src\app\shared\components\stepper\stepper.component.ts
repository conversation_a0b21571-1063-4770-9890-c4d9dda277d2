import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-stepper',
  templateUrl: './stepper.component.html',
  styleUrls: ['./stepper.component.css'],
})
export class StepperComponent {
  @Input() steps: { title: string }[] = []; // List of steps
  @Input() activeStep: number = 0; // Current active step (0-based index)
  @Input() completedSteps: number[] = []; // Array of completed step indices

  isStepCompleted(index: number): boolean {
    return this.completedSteps.includes(index);
  }

  isStepActive(index: number): boolean {
    return index === this.activeStep;
  }
}

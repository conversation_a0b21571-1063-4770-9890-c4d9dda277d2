import { Injectable } from '@angular/core';
import {
  CanActivate,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
} from '@angular/router';
import { AuthService } from '../../core/auth/auth.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    const isLoggedIn = this.authService.isLoggedIn();

    if (!isLoggedIn) {
      if (state.url === '/') {
        return true; // Allow non-logged-in users to access login
      }
      this.router.navigate(['/']); // Redirect to login
      return false;
    }

    if (state.url === '/') {
      this.router.navigate(['/dashboard']); // Redirect logged-in users to dashboard
      return false;
    }

    return true;
  }
}

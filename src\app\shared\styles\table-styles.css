/* Global Table Styles based on prompt-vault-table */

/* Base table container - exactly matching prompt vault */
.enhanced-table {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
  border: 1px solid var(--border-color);
  display: block !important;
  overflow-x: auto !important;
}

.enhanced-table:hover {
  transform: none !important;
  box-shadow: none !important;
}

.enhanced-table::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0.5rem;
  pointer-events: none;
  border: 1px solid transparent;
  background: linear-gradient(
      to bottom right,
      rgba(148, 0, 255, 0.1),
      rgba(44, 206, 138, 0.1)
    )
    border-box;
  -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-table:hover::after {
  opacity: 1;
}

/* Table element */
.enhanced-table table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  width: 100% !important;
  empty-cells: show !important;
  border: 1px solid var(--border-color) !important;
}

/* Table headers - exactly matching prompt vault */
.enhanced-table th,
.enhanced-table thead tr th,
.enhanced-table .table thead tr th,
.enhanced-table table thead tr th {
  font-weight: 600 !important;
  letter-spacing: 0.01em !important;
  border-bottom: 1px solid var(--border-color) !important;
  background-color: rgba(148, 0, 255, 0.03) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.08),
    rgba(44, 206, 138, 0.03)
  ) !important;
  box-sizing: border-box !important;
  white-space: nowrap !important;
  padding: 0.75rem 1rem !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 10 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  transition: background-color 0.2s ease !important;
  text-align: left !important;
  color: var(--content1) !important;
}

/* Table cells */
.enhanced-table td {
  border-bottom: 1px solid var(--border-color) !important;
  border-right: 1px solid var(--border-color) !important;
  padding: 0.75rem 1rem !important;
  box-sizing: border-box !important;
  position: relative !important;
}

/* Last row cells */
.enhanced-table tr:last-child td {
  border-bottom: none !important;
}

/* Last column cells */
.enhanced-table tr td:last-child,
.enhanced-table tr th:last-child {
  border-right: none !important;
}

/* Row hover effect - exactly matching prompt vault */
.enhanced-table .table-hover tbody tr {
  transition: background-color 0.2s ease-in-out;
  transform: none !important;
}

/* Direct override for RippleUI table hover styles */
.enhanced-table .table.table-hover > tbody > tr:hover > td,
.enhanced-table .table.table-hover > tbody > tr:hover > th {
  --tw-bg-opacity: 1;
  background-color: rgba(148, 0, 255, 0.03) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.03),
    transparent
  ) !important;
}

.enhanced-table .table-hover tbody tr:hover {
  background-color: rgba(148, 0, 255, 0.03) !important;
  transform: none !important;
  box-shadow: none !important;
  background-image: none !important;
  border-color: var(--border-color) !important;
  z-index: 1;
  position: relative;
}

/* Row animation - exactly matching prompt vault */
.enhanced-table tbody tr {
  transition: all 0.3s ease;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  animation: fadeInRow 0.5s ease-out forwards;
}

@keyframes fadeInRow {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Empty state styling */
.enhanced-table .empty-state {
  padding: 4rem 2rem;
  text-align: center;
}

.enhanced-table .empty-state-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--primary);
}

/* Pagination section */
.enhanced-table .pagination-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--backgroundPrimary);
}

/* Dark mode specific styles - exactly matching prompt vault */
[data-theme="dark"] .enhanced-table {
  border-color: var(--border-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .enhanced-table::after {
  background: linear-gradient(
      to bottom right,
      rgba(148, 0, 255, 0.2),
      rgba(44, 206, 138, 0.2)
    )
    border-box;
}

[data-theme="dark"] .enhanced-table th,
[data-theme="dark"] .enhanced-table thead tr th,
[data-theme="dark"] .enhanced-table .table thead tr th,
[data-theme="dark"] .enhanced-table table thead tr th {
  color: var(--content1) !important;
  border-bottom: 1px solid var(--border-color) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  background-color: rgba(148, 0, 255, 0.05) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.12),
    rgba(44, 206, 138, 0.05)
  ) !important;
}

[data-theme="dark"] .enhanced-table tr td,
[data-theme="dark"] .enhanced-table tr th {
  border-right-color: var(--border-color) !important;
  border-bottom-color: var(--border-color) !important;
  color: var(--content1, #ffffff) !important;
}

/* Dark mode direct override for RippleUI table hover styles */
[data-theme="dark"] .enhanced-table .table.table-hover > tbody > tr:hover > td,
[data-theme="dark"] .enhanced-table .table.table-hover > tbody > tr:hover > th {
  --tw-bg-opacity: 1;
  background-color: rgba(148, 0, 255, 0.05) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.05),
    transparent
  ) !important;
}

[data-theme="dark"] .enhanced-table .table-hover tbody tr:hover {
  background-color: rgba(148, 0, 255, 0.05) !important;
  box-shadow: none !important;
  border-color: var(--border-color) !important;
  background-image: none !important;
  z-index: 1;
  position: relative;
  transform: none !important;
}

[data-theme="dark"] .enhanced-table .table-hover tbody tr:hover td {
  border-color: var(--border-color) !important;
}

/* Selected row styling */
.enhanced-table tbody tr.selected {
  background-color: rgba(148, 0, 255, 0.1) !important;
  border-left: 3px solid rgb(var(--primary));
}

/* Dependency chart selected row styling - more visible */
.enhanced-table tbody tr.dependency-selected-row {
  background-color: rgba(0, 0, 0, 0.05) !important;
  border-left: 3px solid transparent !important;
}

[data-theme="dark"] .enhanced-table tbody tr.dependency-selected-row {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-left: 3px solid transparent !important;
}

/* Fix for RippleUI table-hover class that uses gray background */
.enhanced-table .table-hover tbody tr:hover td {
  background-color: transparent !important;
  border-color: var(--border-color) !important;
}

[data-theme="dark"] .enhanced-table .table-hover tbody tr:hover td {
  background-color: transparent !important;
  border-color: var(--border-color) !important;
}

/* Fix for text colors in dark mode */
[data-theme="dark"] .enhanced-table .text-primary {
  color: var(--primary, #9400ff) !important;
}

[data-theme="dark"] .enhanced-table .text-content1 {
  color: var(--content1, #ffffff) !important;
}

[data-theme="dark"] .enhanced-table .text-content2 {
  color: var(--content2, #e0e0e0) !important;
}

[data-theme="dark"] .enhanced-table .text-gray-500 {
  color: #bbbbbb !important;
}

import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-select-card',
  templateUrl: './select-card.component.html',
  styleUrls: ['./select-card.component.css'],
})
export class SelectCardComponent {
  @Input() isSelected: boolean = false;
  @Output() selected = new EventEmitter<void>();

  // Get current theme from localStorage
  getTheme(): string {
    return localStorage.getItem('theme') || 'light';
  }

  selectCard() {
    this.selected.emit(); // Notify parent when this card is selected
  }
}

import { Component, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DatabaseService } from 'src/app/core/services/database/database.service';
import { AuthService } from 'src/app/core/auth/auth.service';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-database',
  templateUrl: './database.component.html',
  styleUrls: ['./database.component.css'],
})
export class DatabaseComponent {
  @ViewChild('action') actionTemplate!: TemplateRef<any>;
  @ViewChild('statusTemplate') statusTemplate!: TemplateRef<any>;
  selectedCard: number = 1;
  stepIndex: number = 0;
  showPassword: boolean = false;
  stats: any;
  disconnectedCount = 0;
  loading: boolean = false;

  enableNavigation: boolean = true;
  action: TemplateRef<any> | null = null;
  analysisStatus: TemplateRef<any> | null = null;

  DBType: string = 'sybase'; // Initialize to match the default selectedCard (1 = Sybase)
  ws: WebSocket | null = null;

  // This will store button visibility state for each row
  buttonStates: { [key: number]: string } = {};

  // Search and filter properties
  searchTerm: string = '';
  dbTypeFilter: string = 'all';
  dbStatusFilter: string = 'all';
  dbListSearchTerm: string = '';

  // Analysis modal properties
  analysisModalMode: 'loading' | 'success' | 'error' | null = null;
  selectedDatabaseName: string = '';
  currentAnalysisOperation: string = 'Initializing analysis...';
  analysisTimeElapsed: string = '0:00';
  analysisStartTime: number = 0;
  analysisObjectCount: number = 0;
  analysisErrorMessage: string = '';
  longLoadingMessage: boolean = false;
  runInBackground: boolean = false;
  analysisTimerId: any = null;
  currentAnalysisIndex: number = -1;
  currentAnalysisDbId: string = '';

  // Table functionality properties
  selectedCount = 0;
  sortColumn: string = '';
  sortAsc = true;
  pageSizeOptions = [5, 10, 15];
  pageSize = 5;
  currentPage = 1;

  // User permissions
  isSuperUser: boolean = false;
  isAdmin: boolean = false;
  userPermissions: string[] = [];
  permissionsLoaded: boolean = false;

  // Permission flags for specific actions
  canCreateConnection: boolean = false;
  canEditConnection: boolean = false;
  canAnalyzeConnection: boolean = false;
  canDeleteConnection: boolean = false;
  canViewConnection: boolean = true; // View is typically allowed for all users

  // Action menu properties
  activeActionMenu: boolean = false;
  activeActionMenuItem: any = null;
  actionMenuPosition = { top: 0, left: 0 };

  // Database detail modal properties
  showDatabaseModal: boolean = false;
  modalMode: 'view' | 'edit' | 'delete' = 'view';
  selectedDatabase: any = null;
  editConnectionDetails: any = {
    connection_name: '',
    database_host_name: '',
    database_port: '',
    database_username: '',
    database_password: '',
    DB_MainConnection: '',
    database_type: '',
    org_id: '',
    organization_id: '',
  };

  // Table view modal properties
  selectedTableInfo: any = null;

  constructor(
    private router: Router,
    private dbService: DatabaseService,
    private authService: AuthService
  ) {
    // Retrieve organisation_id from localStorage
    const storedOrgId = localStorage.getItem('organization_id');
    const userId = localStorage.getItem('user_id');

    // Set both org_id and organization_id in connectionDetails
    this.connectionDetails.organization_id = storedOrgId || '';

    if (storedOrgId) {
      this.connectionDetails.org_id = storedOrgId;
    } else {
      // Fallback to user_id if organization_id is not available
      if (userId) {
        this.connectionDetails.org_id = userId;
        console.warn(
          'No organisation_id found in localStorage, using user_id as fallback.'
        );
      } else {
        console.warn('No organisation_id or user_id found in localStorage.');
      }
    }

    // Initialize with Sybase selected by default (first item in DBList)
    if (this.DBList && this.DBList.length > 0) {
      this.selectedCard = this.DBList[0].id;
      this.DBType = this.DBList[0].key;
      console.log('Default database type set to:', this.DBType);
    }
  }

  calculatePercentage(part: number = 0, total: number = 0): any {
    if (!total || total === 0) return 0;
    const percent = (part / total) * 100;
    return percent.toFixed(1);
  }

  // Get current theme from localStorage
  getTheme(): string {
    return localStorage.getItem('theme') || 'light';
  }

  connectionDetails = {
    connection_name: '',
    database_host_name: '',
    database_port: '',
    database_username: '',
    database_password: '',
    DB_MainConnection: '',
    org_id: '',
    organization_id: '',
  };

  // Form validation errors
  formErrors = {
    DB_MainConnection: '',
    connection_name: '',
    database_host_name: '',
    database_port: '',
    database_username: '',
    database_password: '',
  };

  // Track if fields have been touched
  touchedFields = {
    DB_MainConnection: false,
    connection_name: false,
    database_host_name: false,
    database_port: false,
    database_username: false,
    database_password: false,
  };

  setSelectedCard(cardNumber: number, key: string) {
    this.selectedCard = cardNumber;
    this.DBType = key;
    console.log(
      'Selected database card:',
      cardNumber,
      'with type:',
      this.DBType
    );
  }

  ngOnInit(): void {
    // Load user permissions first
    this.loadUserPermissions();

    this.fetchDatabases();
    this.calculatePercentage();

    this.dbService.getDatabaseStats().subscribe({
      next: (stats) => {
        console.log('Dashboard Stats:', stats);
        this.stats = stats;

        // Calculate disconnected count based on actual db status
        this.disconnectedCount = this.tableData.filter(
          (item) => item.connectionStatus === 'Disconnected'
        ).length;
      },
      error: (err) => console.error('Failed to load stats', err),
    });
  }

  /**
   * Load user permissions from the auth service
   */
  loadUserPermissions(): void {
    this.authService.getUserRoleDetails().subscribe({
      next: (roleDetails) => {
        // Set basic user information
        this.isSuperUser = roleDetails.is_superuser || false;

        // Case-insensitive check for Admin role
        const roleName = roleDetails.role?.name || '';
        this.isAdmin = roleName.toLowerCase() === 'admin';

        // Get permissions
        if (roleDetails.role && roleDetails.role.permissions) {
          // Handle permissions in array format (from user-role endpoint)
          if (Array.isArray(roleDetails.role.permissions)) {
            this.userPermissions = roleDetails.role.permissions;
          }
          // Handle permissions in object format (for backward compatibility)
          else {
            this.userPermissions = Object.entries(roleDetails.role.permissions)
              .filter(([_, value]) => value === true)
              .map(([key, _]) => key);
          }
        }

        // Check specific permissions
        this.canCreateConnection =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('create_connection');

        this.canEditConnection =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('edit_connection');

        this.canAnalyzeConnection =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('analyze_connection');

        this.canDeleteConnection =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('delete_connection');

        // View is typically allowed for all authenticated users
        this.canViewConnection = true;

        // Mark permissions as loaded
        this.permissionsLoaded = true;

        console.log('User permissions loaded:', {
          isSuperUser: this.isSuperUser,
          isAdmin: this.isAdmin,
          permissions: this.userPermissions,
          canCreateConnection: this.canCreateConnection,
          canEditConnection: this.canEditConnection,
          canAnalyzeConnection: this.canAnalyzeConnection,
          canDeleteConnection: this.canDeleteConnection,
          canViewConnection: this.canViewConnection,
        });
      },
      error: (err) => {
        console.error('Error loading user permissions:', err);
        // Fallback to getUserDetails if getUserRoleDetails fails
        this.fallbackToUserDetails();
      },
    });
  }

  /**
   * Fallback to getUserDetails if getUserRoleDetails fails
   */
  fallbackToUserDetails(): void {
    this.authService.getUserDetails().subscribe({
      next: (user) => {
        // Set basic user information
        this.isSuperUser = user.is_superuser || false;

        // Case-insensitive check for Admin role
        const roleName = user.role?.name || '';
        this.isAdmin = roleName.toLowerCase() === 'admin';

        // Get permissions
        if (user.role && user.role.permissions) {
          // Handle permissions in array format
          if (Array.isArray(user.role.permissions)) {
            this.userPermissions = user.role.permissions;
          }
          // Handle permissions in object format
          else {
            this.userPermissions = Object.entries(user.role.permissions)
              .filter(([_, value]) => value === true)
              .map(([key, _]) => key);
          }
        }

        // Check specific permissions
        this.canCreateConnection =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('create_connection');

        this.canEditConnection =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('edit_connection');

        this.canAnalyzeConnection =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('analyze_connection');

        this.canDeleteConnection =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('delete_connection');

        // View is typically allowed for all authenticated users
        this.canViewConnection = true;

        // Mark permissions as loaded
        this.permissionsLoaded = true;

        console.log('User permissions loaded (fallback):', {
          isSuperUser: this.isSuperUser,
          isAdmin: this.isAdmin,
          permissions: this.userPermissions,
          canCreateConnection: this.canCreateConnection,
          canEditConnection: this.canEditConnection,
          canAnalyzeConnection: this.canAnalyzeConnection,
          canDeleteConnection: this.canDeleteConnection,
          canViewConnection: this.canViewConnection,
        });
      },
      error: (err) => {
        console.error('Error loading user details:', err);
        // Even if permissions fail to load, set defaults to allow basic viewing
        this.permissionsLoaded = true;
      },
    });
  }

  /**
   * Check if the user has a specific permission
   * @param permission The permission to check
   * @returns True if the user has the permission, false otherwise
   */
  hasPermission(permission: string): boolean {
    return (
      this.isSuperUser ||
      this.isAdmin ||
      this.userPermissions.includes(permission)
    );
  }

  ngAfterViewInit(): void {
    // Now you can safely assign the template after view initialization
    this.customTemplates = {
      action: this.actionTemplate,
      analysisStatus: this.statusTemplate,
    };
  }

  customTemplates = {
    action: this.action,
    analysisStatus: this.analysisStatus,
  };

  // Table functionality methods
  get totalEntries(): number {
    return this.filteredTableData.length;
  }

  get currentPageStart(): number {
    return (this.currentPage - 1) * this.pageSize + 1;
  }

  get currentPageEnd(): number {
    return Math.min(
      this.currentPageStart + this.pageSize - 1,
      this.totalEntries
    );
  }

  get totalPages(): number {
    return Math.ceil(this.totalEntries / this.pageSize);
  }

  // Generate an array of page numbers for pagination
  get pageNumbers(): number[] {
    const totalPages = this.totalPages;
    const currentPage = this.currentPage;

    // If total pages is 5 or less, show all pages
    if (totalPages <= 5) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // If current page is near the beginning
    if (currentPage <= 3) {
      return [1, 2, 3, 4, 5];
    }

    // If current page is near the end
    if (currentPage >= totalPages - 2) {
      return Array.from({ length: 5 }, (_, i) => totalPages - 4 + i);
    }

    // If current page is in the middle
    return [
      currentPage - 2,
      currentPage - 1,
      currentPage,
      currentPage + 1,
      currentPage + 2,
    ];
  }

  get paginatedData(): any[] {
    const start = (this.currentPage - 1) * this.pageSize;
    return this.filteredTableData.slice(start, start + this.pageSize);
  }

  // Get filtered database list based on search term
  get filteredDBList(): any[] {
    if (!this.dbListSearchTerm) {
      return this.DBList;
    }
    const search = this.dbListSearchTerm.toLowerCase();
    return this.DBList.filter(
      (db) =>
        db.name.toLowerCase().includes(search) ||
        db.key.toLowerCase().includes(search)
    );
  }

  // Get filtered table data based on search and filters
  get filteredTableData(): any[] {
    let result = [...this.tableData];

    // Apply search term
    if (this.searchTerm) {
      const search = this.searchTerm.toLowerCase();
      result = result.filter(
        (item) =>
          item.DB_MainConnection.toLowerCase().includes(search) ||
          item.dbHost.toLowerCase().includes(search) ||
          item.dbName.toLowerCase().includes(search) ||
          item.dbType.toLowerCase().includes(search)
      );
    }

    // Apply DB type filter
    if (this.dbTypeFilter !== 'all') {
      result = result.filter((item) => item.dbType === this.dbTypeFilter);
    }

    // Apply status filter
    if (this.dbStatusFilter !== 'all') {
      if (this.dbStatusFilter === 'connected') {
        result = result.filter((item) => item.connectionStatus === 'Connected');
      } else if (this.dbStatusFilter === 'disconnected') {
        result = result.filter(
          (item) => item.connectionStatus === 'Disconnected'
        );
      }
    }

    return result;
  }

  resetFilters() {
    this.searchTerm = '';
    this.dbTypeFilter = 'all';
    this.dbStatusFilter = 'all';
    this.currentPage = 1;
  }

  selectAll(event: any) {
    const checked = event.target.checked;
    this.tableData.forEach((item) => (item.selected = checked));
    this.updateSelection();
  }

  updateSelection() {
    this.selectedCount = this.tableData.filter((item) => item.selected).length;
  }

  sort(column: string) {
    this.sortAsc = this.sortColumn === column ? !this.sortAsc : true;
    this.sortColumn = column;

    // Fixed sort method with type safety
    this.tableData.sort((a, b) => {
      const aValue = a[column as keyof typeof a];
      const bValue = b[column as keyof typeof b];

      if (aValue > bValue) {
        return 1 * (this.sortAsc ? 1 : -1);
      } else if (aValue < bValue) {
        return -1 * (this.sortAsc ? 1 : -1);
      } else {
        return 0;
      }
    });
  }

  prevPage() {
    if (this.currentPage > 1) this.currentPage--;
  }

  nextPage() {
    if (this.currentPage < this.totalPages) this.currentPage++;
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  changePageSize(event: any) {
    this.pageSize = parseInt(event.target.value, 10);
    this.currentPage = 1;
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'success':
        return 'dot-success';
      case 'warning':
        return 'dot-warning';
      case 'error':
        return 'dot-error';
      default:
        return '';
    }
  }

  // Function to change button state per row
  showButton(rowId: number, newState: string) {
    this.buttonStates[rowId] = newState;
  }

  nextStep() {
    // Ensure a database type is selected before proceeding
    if (this.stepIndex === 0) {
      if (!this.selectedCard) {
        alert('Please select a database type first.');
        return;
      }

      // Double-check that DBType is set correctly based on the selected card
      const selectedDb = this.DBList.find((db) => db.id === this.selectedCard);
      if (selectedDb && (!this.DBType || this.DBType !== selectedDb.key)) {
        this.DBType = selectedDb.key;
        console.log(
          'Updated database type to match selected card:',
          this.DBType
        );
      }

      console.log('Moving to next step with database type:', this.DBType);
    }

    if (this.stepIndex < 1) this.stepIndex++;
  }

  prevStep() {
    if (this.stepIndex > 0) this.stepIndex--;
  }

  // Toggle connection status
  toggleConnectionStatus(id: string, _currentStatus: string) {
    // Only allow admin and superuser to toggle connection status
    if (!this.isAdmin && !this.isSuperUser) {
      alert('Only administrators can change connection status');
      return;
    }

    // Use the new toggle endpoint instead of manually setting the status
    // Note: currentStatus parameter is kept for backward compatibility but not used
    this.dbService.toggleConnectionStatus(id).subscribe({
      next: (response) => {
        console.log('Connection status toggled:', response);

        // Update the local state after successful update
        const index = this.tableData.findIndex(
          (item) => item.database_id === id
        );
        if (index !== -1) {
          // Update with the status returned from the API
          this.tableData[index].connectionStatus = response.connection_status;

          // Recalculate disconnected count
          this.disconnectedCount = this.tableData.filter(
            (item) => item.connectionStatus === 'Disconnected'
          ).length;
        }
      },
      error: (error) => {
        console.error('Error toggling connection status:', error);
        alert('Failed to toggle connection status');
      },
    });
  }

  /**
   * Edit a database connection
   * @param item The database connection to edit
   */
  editConnection(item: any) {
    if (!this.canEditConnection) {
      alert('You do not have permission to edit connections');
      return;
    }

    // Close the action menu
    this.closeActionMenu();

    console.log('Edit connection:', item);

    // Fetch the full database details
    this.dbService.getDatabaseById(item.database_id).subscribe({
      next: (response) => {
        this.selectedDatabase = response;

        // Prepare the edit form
        this.prepareEditForm(response);

        // Show the modal in edit mode
        this.modalMode = 'edit';
        this.showDatabaseModal = true;

        // Open the modal
        const modalCheckbox = document.getElementById(
          'database-modal'
        ) as HTMLInputElement;
        if (modalCheckbox) {
          modalCheckbox.checked = true;
        }
      },
      error: (error) => {
        console.error('Error fetching database details:', error);
        alert('Failed to fetch database details');
      },
    });
  }

  /**
   * Delete a database connection
   * @param item The database connection to delete
   */
  deleteConnection(item: any) {
    if (!this.canDeleteConnection) {
      alert('You do not have permission to delete connections');
      return;
    }

    // Close the action menu
    this.closeActionMenu();

    console.log('Delete connection:', item);

    // Set the selected database and show the modal in delete mode
    this.selectedDatabase = item;
    this.modalMode = 'delete';
    this.showDatabaseModal = true;

    // Open the modal
    const modalCheckbox = document.getElementById(
      'database-modal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  /**
   * View database connection details
   * @param item The database connection to view
   */
  viewConnection(item: any) {
    console.log('View connection:', item);

    // Close the action menu
    this.closeActionMenu();

    // First, set the basic information we already have
    this.selectedDatabase = { ...item };

    // Show the modal in view mode
    this.modalMode = 'view';
    this.showDatabaseModal = true;

    // Open the modal immediately with what we have
    const modalCheckbox = document.getElementById(
      'database-modal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }

    // Show loading indicator
    this.loading = true;

    // Then fetch the full database details
    this.dbService.getDatabaseById(item.database_id).subscribe({
      next: (response) => {
        // Update with complete information
        this.selectedDatabase = response;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error fetching database details:', error);

        // We already have basic info, so just show what we have
        this.loading = false;

        // Show error message in the modal instead of alert
        this.selectedDatabase.error = true;
        this.selectedDatabase.errorMessage =
          'Could not fetch complete database details. Showing limited information.';
      },
    });
  }

  /**
   * View table information
   * @param databaseId The database ID
   * @param objectName The table name
   * @param objectType The object type (table, view, etc.)
   */
  viewTableInfo(databaseId: string, objectName: string, objectType: string) {
    console.log('View table info:', { databaseId, objectName, objectType });

    // Don't show loading indicator initially
    this.loading = false;

    // Create the table info object directly
    this.selectedTableInfo = {
      objectName: objectName,
      objectType: objectType,
      transformed_into: 'not transformed',
      doc: 'not generated',
      description: '',
      created_at: null,
    };

    // Open the table view modal immediately
    const modalCheckbox = document.getElementById(
      'table-view-modal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }
  }

  /**
   * Save edited database connection
   */
  saveEditedConnection() {
    if (!this.selectedDatabase) {
      alert('No database selected');
      return;
    }

    // Show loading indicator
    this.loading = true;

    // Create payload for update
    const payload: any = {
      connection_name: this.editConnectionDetails.connection_name,
      database_host_name: this.editConnectionDetails.database_host_name,
      database_port: this.editConnectionDetails.database_port,
      database_username: this.editConnectionDetails.database_username,
      database_type: this.editConnectionDetails.database_type,
      DB_MainConnection: this.editConnectionDetails.DB_MainConnection,
      org_id: this.editConnectionDetails.org_id,
      organization_id: this.editConnectionDetails.organization_id,
    };

    // Only include password if it was changed
    if (this.editConnectionDetails.database_password) {
      payload.database_password = this.editConnectionDetails.database_password;
    }

    // Update the database connection
    this.dbService
      .updateDatabase(this.selectedDatabase.database_id, payload)
      .subscribe({
        next: (response) => {
          console.log('Database updated:', response);

          // Refresh the database list
          this.fetchDatabases();

          // Close the modal
          const modalCheckbox = document.getElementById(
            'database-modal'
          ) as HTMLInputElement;
          if (modalCheckbox) {
            modalCheckbox.checked = false;
          }

          // Show success message
          this.showSuccessToast('Database connection updated successfully!');
          this.loading = false;
        },
        error: (error) => {
          this.loading = false;
          console.error('Error updating database connection:', error);
          alert('Failed to update database connection');
        },
      });
  }

  /**
   * Confirm database deletion
   */
  confirmDeleteConnection() {
    if (!this.selectedDatabase) {
      alert('No database selected');
      return;
    }

    // Show loading indicator
    this.loading = true;

    // Delete the database connection
    this.dbService.deleteDatabase(this.selectedDatabase.database_id).subscribe({
      next: (response) => {
        console.log('Database deleted:', response);

        // Refresh the database list
        this.fetchDatabases();

        // Close the modal
        const modalCheckbox = document.getElementById(
          'database-modal'
        ) as HTMLInputElement;
        if (modalCheckbox) {
          modalCheckbox.checked = false;
        }

        // Show success message
        this.showSuccessToast('Database connection deleted successfully!');
        this.loading = false;
      },
      error: (error) => {
        this.loading = false;
        console.error('Error deleting database connection:', error);
        alert('Failed to delete database connection');
      },
    });
  }

  /**
   * Prepare the edit form with the selected database's details
   * @param database The database to edit
   */
  prepareEditForm(database: any) {
    if (!database) return;

    // Get organization_id from localStorage, fallback to user_id if not available
    const orgId = localStorage.getItem('user_id');
    // Copy the database details to the edit form
    this.editConnectionDetails = {
      connection_name: database.connection_name || '',
      database_host_name: database.database_host_name || '',
      database_port: database.database_port || '',
      database_username: database.database_username || '',
      database_password: '', // Don't pre-fill password for security reasons
      DB_MainConnection: database.DB_MainConnection || '',
      database_type: database.database_type || '',
      org_id: orgId,
    };
  }

  // Connect/disconnect selected databases
  async connectSelectedDatabases() {
    // Only allow admin and superuser to connect databases
    if (!this.isAdmin && !this.isSuperUser) {
      alert('Only administrators can change connection status');
      return;
    }

    const selectedItems = this.tableData.filter((item) => item.selected);
    if (selectedItems.length === 0) return;

    try {
      // For each disconnected database, toggle its connection status
      const disconnectedItems = selectedItems.filter(
        (item) => item.connectionStatus !== 'Connected'
      );

      if (disconnectedItems.length === 0) {
        alert('All selected databases are already connected.');
        return;
      }

      // Toggle connection status for all disconnected databases
      const updatePromises = disconnectedItems.map((item) =>
        firstValueFrom(this.dbService.toggleConnectionStatus(item.database_id))
      );

      const results = await Promise.all(updatePromises);

      // Update local state based on API responses
      results.forEach((response, index) => {
        const dbId = disconnectedItems[index].database_id;
        const tableIndex = this.tableData.findIndex(
          (item) => item.database_id === dbId
        );

        if (tableIndex !== -1) {
          this.tableData[tableIndex].connectionStatus =
            response.connection_status;
        }
      });

      alert(`Connected ${disconnectedItems.length} databases`);
      this.updateSelection();

      // Recalculate disconnected count
      this.disconnectedCount = this.tableData.filter(
        (item) => item.connectionStatus === 'Disconnected'
      ).length;
    } catch (error) {
      console.error('Error connecting databases:', error);
      alert('Failed to connect some databases');
    }
  }

  async disconnectSelectedDatabases() {
    // Only allow admin and superuser to disconnect databases
    if (!this.isAdmin && !this.isSuperUser) {
      alert('Only administrators can change connection status');
      return;
    }

    const selectedItems = this.tableData.filter((item) => item.selected);
    if (selectedItems.length === 0) return;

    try {
      // For each connected database, toggle its connection status
      const connectedItems = selectedItems.filter(
        (item) => item.connectionStatus === 'Connected'
      );

      if (connectedItems.length === 0) {
        alert('All selected databases are already disconnected.');
        return;
      }

      // Toggle connection status for all connected databases
      const updatePromises = connectedItems.map((item) =>
        firstValueFrom(this.dbService.toggleConnectionStatus(item.database_id))
      );

      const results = await Promise.all(updatePromises);

      // Update local state based on API responses
      results.forEach((response, index) => {
        const dbId = connectedItems[index].database_id;
        const tableIndex = this.tableData.findIndex(
          (item) => item.database_id === dbId
        );

        if (tableIndex !== -1) {
          this.tableData[tableIndex].connectionStatus =
            response.connection_status;
        }
      });

      alert(`Disconnected ${connectedItems.length} databases`);
      this.updateSelection();

      // Recalculate disconnected count
      this.disconnectedCount = this.tableData.filter(
        (item) => item.connectionStatus === 'Disconnected'
      ).length;
    } catch (error) {
      console.error('Error disconnecting databases:', error);
      alert('Failed to disconnect some databases');
    }
  }

  // Validate a single field
  validateField(field: string, value: string): string {
    // Mark field as touched
    this.touchedFields[field as keyof typeof this.touchedFields] = true;

    // Reset error
    let error = '';

    // Validate based on field type
    switch (field) {
      case 'DB_MainConnection':
        if (!value.trim()) {
          error = 'Connection Name is required';
        } else if (value.length < 3) {
          error = 'Connection Name must be at least 3 characters';
        } else if (value.length > 50) {
          error = 'Connection Name must be less than 50 characters';
        } else {
          // Check for duplicate connection name
          const duplicateConnection = this.tableData.find(
            (db) => db.DB_MainConnection.toLowerCase() === value.toLowerCase()
          );

          if (duplicateConnection) {
            error = 'A connection with this name already exists';
          }
        }
        break;

      case 'connection_name':
        if (!value.trim()) {
          error = 'Database Name is required';
        } else if (value.length < 2) {
          error = 'Database Name must be at least 2 characters';
        } else if (value.length > 50) {
          error = 'Database Name must be less than 50 characters';
        }
        break;

      case 'database_host_name':
        if (!value.trim()) {
          error = 'Host is required';
        } else if (
          !/^[a-zA-Z0-9.-]+$/.test(value) &&
          !/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/.test(value)
        ) {
          error = 'Host must be a valid hostname or IP address';
        }
        break;

      case 'database_port':
        if (!value.trim()) {
          error = 'Port is required';
        } else if (!/^\d+$/.test(value)) {
          error = 'Port must be a number';
        } else {
          const port = parseInt(value, 10);
          if (port < 1 || port > 65535) {
            error = 'Port must be between 1 and 65535';
          }
        }
        break;

      case 'database_username':
        if (!value.trim()) {
          error = 'Username is required';
        }
        break;

      case 'database_password':
        if (!value.trim()) {
          error = 'Password is required';
        }
        break;
    }

    // Update error state
    this.formErrors[field as keyof typeof this.formErrors] = error;
    return error;
  }

  // Validate all form fields
  validateForm(): boolean {
    let isValid = true;

    // Validate each field
    for (const field in this.connectionDetails) {
      if (field === 'org_id') continue; // Skip org_id

      const value =
        this.connectionDetails[field as keyof typeof this.connectionDetails];
      if (typeof value === 'string') {
        const error = this.validateField(field, value);
        if (error) {
          isValid = false;
        }
      }
    }

    return isValid;
  }

  // Handle input change
  onInputChange(field: string, value: string) {
    // Update the field value
    this.connectionDetails[field as keyof typeof this.connectionDetails] =
      value;

    // Validate if touched
    if (this.touchedFields[field as keyof typeof this.touchedFields]) {
      this.validateField(field, value);
    }
  }

  // Mark all fields as touched and validate
  markAllTouched() {
    for (const field in this.touchedFields) {
      this.touchedFields[field as keyof typeof this.touchedFields] = true;
      const value =
        this.connectionDetails[field as keyof typeof this.connectionDetails];
      if (typeof value === 'string') {
        this.validateField(field, value);
      }
    }
  }

  connectDB() {
    // Get both organization_id and user_id from localStorage
    const orgId = localStorage.getItem('user_id');
    const organization_id = localStorage.getItem('organization_id');

    // Update both org_id and organization_id in connectionDetails
    this.connectionDetails.org_id = orgId || '';
    this.connectionDetails.organization_id = organization_id || '';

    if (
      !this.connectionDetails.org_id &&
      !this.connectionDetails.organization_id
    ) {
      alert('Organization ID is missing. Please log in again.');
      return;
    }

    // Mark all fields as touched and validate
    this.markAllTouched();

    // Check if form is valid
    if (!this.validateForm()) {
      return;
    }

    // Ensure DBType is set - if not, use the default from the first item in DBList
    if (!this.DBType && this.DBList && this.DBList.length > 0) {
      this.DBType = this.DBList[0].key;
      console.log('DBType was not set, defaulting to:', this.DBType);
    }

    // Show loading indicator
    this.loading = true;

    // Create payload with both org_id and organization_id to support the updated API
    const payload = {
      connection_name: this.connectionDetails.connection_name,
      database_host_name: this.connectionDetails.database_host_name,
      database_port: this.connectionDetails.database_port,
      database_username: this.connectionDetails.database_username,
      database_password: this.connectionDetails.database_password,
      database_type: this.DBType,
      DB_MainConnection: this.connectionDetails.DB_MainConnection,
      org_id: this.connectionDetails.org_id, // Include org_id
      organization_id: this.connectionDetails.organization_id, // Include organization_id as well
    };

    console.log('Sending API request with database_type:', this.DBType);

    this.dbService.createDbConfig(payload).subscribe({
      next: (response) => {
        console.log('Database Connected:', response);

        // Reset form and close modal
        this.connectionDetails = {
          connection_name: '',
          database_host_name: '',
          database_port: '',
          database_username: '',
          database_password: '',
          DB_MainConnection: '',
          org_id: this.connectionDetails.org_id,
          organization_id: this.connectionDetails.organization_id,
        };
        this.stepIndex = 0;

        // Refresh the database list
        this.fetchDatabases();

        // Close the modal
        const modalCheckbox = document.getElementById(
          'connect-DB'
        ) as HTMLInputElement;
        if (modalCheckbox) {
          modalCheckbox.checked = false;
        }

        // Show success message
        this.showSuccessToast('Database connected successfully!');
        this.loading = false;
      },
      error: (error) => {
        this.loading = false;
        console.error('Error connecting database:', error);

        // Handle specific error cases
        if (
          error.status === 409 ||
          (error.error &&
            error.error.detail &&
            error.error.detail.includes('already exists'))
        ) {
          alert(
            'This database connection already exists. Please use different connection details.'
          );
        } else if (error.status === 401) {
          alert('Authentication error. Please log in again.');
        } else if (error.status === 400) {
          alert(
            'Invalid connection details. Please check your inputs and try again.'
          );
        } else {
          alert('Failed to connect database. Please try again later.');
        }
      },
    });
  }

  // Database List
  DBList = [
    { id: 1, key: 'sybase', name: 'Sybase', icon: 'assets/sybase.png' },
    {
      id: 2,
      key: 'postgresql',
      name: 'PostgreSQL',
      icon: 'assets/postgrace.png',
    },
    {
      id: 3,
      key: 'sql_server',
      name: 'SQL Server',
      icon: 'assets/sql server.png',
    },
    { id: 4, key: 'mysql', name: 'MySQL', icon: 'assets/mysql.png' },
    {
      id: 5,
      key: 'amazon_rds_pg',
      name: 'Amazon RDS PostgreSQL',
      icon: 'assets/rds.png',
    },
    {
      id: 6,
      key: 'amazon_athena',
      name: 'Amazon Athena',
      icon: 'assets/amzathena.png',
    },
    { id: 7, key: 'oracle', name: 'Oracle', icon: 'assets/oracle.png' },
  ];

  fetchDatabases() {
    this.loading = true;
    // Try to get organization_id first
    const storedOrgId = localStorage.getItem('organization_id');

    // If organization_id is not available, fall back to user_id
    const userId = localStorage.getItem('user_id');

    // Use organization_id if available, otherwise use user_id as fallback
    const idToUse = storedOrgId || userId;

    if (!idToUse) {
      console.warn('No organisation_id or user_id found in localStorage.');
      this.loading = false;
      // Try to refresh user details to get the IDs
      this.refreshUserDetails();
      return;
    }

    console.log(
      `Fetching databases using ${
        storedOrgId ? 'organization_id' : 'user_id'
      }: ${idToUse}`
    );

    // Pass the ID to use to the service
    this.dbService.getDatabases(idToUse).subscribe({
      next: (response: any[]) => {
        console.log('Fetched Databases:', response);
        this.databases = response;
        this.loading = false;

        // Initialize tableData with proper mapping from API response
        this.tableData = response.map((db) => ({
          id: db.database_id,
          database_id: db.database_id,
          DB_MainConnection: db.DB_MainConnection || 'Unknown',
          dbHost: db.database_host_name || 'N/A',
          dbName: db.connection_name || 'N/A',
          dbType: db.database_type || 'N/A',
          objectCount: db.total_count || 0,
          selected: false,
          analysisStatus: db.analysis_status || 'Awaiting Analysis',
          connectionStatus: db.connection_status || 'Connected',
          dependencyStatus: db.dependency_status || 'Not Generated',
        }));

        // Calculate disconnected count
        this.disconnectedCount = this.tableData.filter(
          (item) => item.connectionStatus === 'Disconnected'
        ).length;
      },
      error: (error) => {
        console.error('Error fetching databases:', error);
        this.loading = false;

        // Check if the error is due to invalid ID (403 or 404)
        if (error.status === 403 || error.status === 404) {
          console.warn(
            'Possible invalid ID used. Attempting to refresh user details...'
          );
          this.refreshUserDetails();
        }
      },
    });
  }

  /**
   * Refresh user details from the server to get valid IDs
   * This is called when there's an error with the stored IDs
   */
  refreshUserDetails() {
    console.log('Refreshing user details to get valid IDs');
    this.authService.getUserDetails().subscribe({
      next: (user) => {
        if (user && user.id) {
          console.log('User details refreshed successfully');
          // After refreshing user details, try fetching databases again
          setTimeout(() => this.fetchDatabases(), 500);
        }
      },
      error: (err) => {
        console.error('Failed to refresh user details:', err);
        // If we can't refresh user details, the user might need to log in again
        alert('Session may have expired. Please log in again.');
        this.authService.logout();
      },
    });
  }

  databases: any[] = [];

  columns = [
    { key: 'DB_MainConnection', label: 'Connection Name' },
    { key: 'dbHost', label: 'DB Host' },
    { key: 'dbName', label: 'DB Name' },
    { key: 'dbType', label: 'DB Type' },
    { key: 'objectCount', label: 'Object Count' },
    { key: 'analysisStatus', label: 'Analysis Status', custom: true },
    { key: 'dependencyStatus', label: 'Chart Status' },
    { key: 'connectionStatus', label: 'Connection Status', custom: true },
    { key: 'action', label: '', custom: true },
  ];

  // Get unique DB types for filter dropdown
  get uniqueDbTypes(): string[] {
    const types = new Set<string>();
    this.tableData.forEach((item) => {
      if (item.dbType) {
        types.add(item.dbType);
      }
    });
    return Array.from(types);
  }

  // Add index signature to allow string indexing
  tableData: {
    id: string;
    database_id: string;
    DB_MainConnection: any;
    dbHost: any;
    dbName: any;
    dbType: any;
    objectCount: any;
    analysisStatus?: any;
    connectionStatus?: string;
    dependencyStatus?: string;
    selected: boolean;
    [key: string]: any; // Add index signature to allow string indexing
  }[] = [];

  // Analyze database using WebSocket and update row data
  analyzeDatabase(index: number) {
    const dbId = this.tableData[index].database_id;
    const dbName = this.tableData[index].DB_MainConnection;

    // Store current analysis information
    this.currentAnalysisIndex = index;
    this.currentAnalysisDbId = dbId;
    this.selectedDatabaseName = dbName;

    // Using DB_MainConnection for logging purposes
    console.log('Analyzing Database:', dbId, 'Connection:', dbName);

    // Change status to "Analyzing..." immediately after clicking
    this.tableData[index].analysisStatus = 'Analyzing...';

    // Disable all other analyze buttons by setting a global analyzing state
    this.disableAllAnalyzeButtons();

    // Show the analysis progress modal
    this.showAnalysisModal('loading');

    // Start the analysis timer
    this.startAnalysisTimer();

    // Show long loading message after 10 seconds
    setTimeout(() => {
      if (this.analysisModalMode === 'loading') {
        this.longLoadingMessage = true;
      }
    }, 10000);

    // First connect to WebSocket for analysis
    this.ws = this.dbService.connectToWebSocket(dbId);

    // Handle WebSocket errors
    this.ws.onerror = (error) => {
      console.error('WebSocket Error in component:', error);

      // Update the UI to show the error
      this.tableData[index].analysisStatus = 'Analysis Failed';

      // Show error in modal if not in background mode
      if (!this.runInBackground) {
        this.analysisErrorMessage =
          'WebSocket connection failed. Trying alternative method...';
        this.showAnalysisModal('error');
      }

      // Try to use REST API as fallback
      this.fallbackToRestApi(index, dbId);
    };

    this.ws.onmessage = (event) => {
      console.log('WebSocket Message:', event.data);

      try {
        // Try to parse the message as JSON
        const jsonData = JSON.parse(event.data);

        // Handle structured data if available
        if (jsonData.total_objects) {
          this.tableData[index].objectCount = jsonData.total_objects;
          this.analysisObjectCount = jsonData.total_objects;
          this.currentAnalysisOperation = `Found ${jsonData.total_objects} database objects`;
        }

        // Update operation status if available
        if (jsonData.operation) {
          this.currentAnalysisOperation = jsonData.operation;
        }

        if (jsonData.status && jsonData.status.includes('complete')) {
          // Analysis is complete, update status
          this.tableData[index].analysisStatus = 'Analysis Complete';

          // Show toast if in background mode, otherwise keep the loading modal open
          // (success modal will be shown after chart generation completes)
          if (this.runInBackground) {
            this.showSuccessToast(
              `Analysis of ${dbName} completed successfully`
            );
          }

          // Now trigger dependency chart generation
          this.generateDependencyChart(index, dbId);

          // Refresh table data to get the latest information
          this.fetchDatabases();

          // Re-enable analyze buttons
          this.enableAllAnalyzeButtons();
        }
      } catch (e) {
        // Handle text-based messages
        // Update object count if present in the message
        if (event.data.match(/\d+/)) {
          const object_count = parseInt(event.data.match(/\d+/)[0], 10);
          if (object_count > 0) {
            this.tableData[index].objectCount = object_count;
            this.analysisObjectCount = object_count;
            this.currentAnalysisOperation = `Found ${object_count} database objects`;
          }
        }

        // Update operation status based on message content
        if (event.data.includes('Scanning')) {
          this.currentAnalysisOperation = 'Scanning database objects...';
        } else if (event.data.includes('Analyzing')) {
          this.currentAnalysisOperation = 'Analyzing database structure...';
        } else if (event.data.includes('Processing')) {
          this.currentAnalysisOperation = 'Processing database metadata...';
        }

        // Check if the message contains the analysis completion notification
        if (
          event.data.includes('Analysis status:') ||
          event.data.includes('Analysis complete') ||
          event.data.includes('complete')
        ) {
          // Initial database analysis is complete
          this.tableData[index].analysisStatus = 'Analysis Complete';

          // Show toast if in background mode, otherwise keep the loading modal open
          // (success modal will be shown after chart generation completes)
          if (this.runInBackground) {
            this.showSuccessToast(
              `Analysis of ${dbName} completed successfully`
            );
          }

          // Now trigger dependency chart generation
          this.generateDependencyChart(index, dbId);

          // Refresh table data to get the latest information
          this.fetchDatabases();

          // Re-enable analyze buttons
          this.enableAllAnalyzeButtons();
        }
      }
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket Closed with code:', event.code);

      // If this was an abnormal closure and not already handled by onerror
      if (
        event.code !== 1000 &&
        this.tableData[index].analysisStatus === 'Analyzing...'
      ) {
        this.tableData[index].analysisStatus = 'Analysis Failed';

        // Show error in modal if not in background mode
        if (!this.runInBackground) {
          this.analysisErrorMessage =
            'WebSocket connection closed unexpectedly. Trying alternative method...';
          this.showAnalysisModal('error');
        }

        // Try to use REST API as fallback
        this.fallbackToRestApi(index, dbId);
      }
    };
  }

  /**
   * Show the analysis modal with the specified mode
   * @param mode The modal mode to show (loading, success, error)
   */
  showAnalysisModal(mode: 'loading' | 'success' | 'error') {
    this.analysisModalMode = mode;

    // Open the modal
    const modalCheckbox = document.getElementById(
      'analysis-progress-modal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }

    // If showing success or error, stop the timer
    if (mode === 'success' || mode === 'error') {
      this.stopAnalysisTimer();
    }
  }

  /**
   * Start the analysis timer to track elapsed time
   */
  startAnalysisTimer() {
    // Clear any existing timer
    this.stopAnalysisTimer();

    // Set the start time
    this.analysisStartTime = Date.now();

    // Start the timer
    this.analysisTimerId = setInterval(() => {
      const elapsedMs = Date.now() - this.analysisStartTime;
      const elapsedSec = Math.floor(elapsedMs / 1000);
      const minutes = Math.floor(elapsedSec / 60);
      const seconds = elapsedSec % 60;
      this.analysisTimeElapsed = `${minutes}:${seconds
        .toString()
        .padStart(2, '0')}`;
    }, 1000);
  }

  /**
   * Stop the analysis timer
   */
  stopAnalysisTimer() {
    if (this.analysisTimerId) {
      clearInterval(this.analysisTimerId);
      this.analysisTimerId = null;
    }
  }

  /**
   * Run the analysis in background mode
   */
  runAnalysisInBackground() {
    this.runInBackground = true;

    // Close the modal
    const modalCheckbox = document.getElementById(
      'analysis-progress-modal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = false;
    }

    // Show a toast notification
    this.showSuccessToast(
      `Analysis of ${this.selectedDatabaseName} continues in the background`
    );
  }

  /**
   * Retry the analysis after an error
   */
  retryAnalysis() {
    // Close the error modal
    const modalCheckbox = document.getElementById(
      'analysis-progress-modal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = false;
    }

    // Retry the analysis if we have valid index
    if (this.currentAnalysisIndex >= 0) {
      setTimeout(() => {
        this.analyzeDatabase(this.currentAnalysisIndex);
      }, 500);
    }
  }

  /**
   * Navigate to the database details page after successful analysis
   */
  viewDatabaseDetails() {
    // Close the success modal
    const modalCheckbox = document.getElementById(
      'analysis-progress-modal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = false;
    }

    // Navigate to details page if we have valid index
    if (this.currentAnalysisIndex >= 0) {
      const item = this.tableData[this.currentAnalysisIndex];
      this.goToDatabaseDetails(
        item.DB_MainConnection,
        item.database_id,
        item.dbType
      );
    }
  }

  /**
   * Disable all analyze buttons during analysis
   */
  disableAllAnalyzeButtons() {
    this.tableData.forEach((item) => {
      if (item.database_id !== this.currentAnalysisDbId) {
        item['_previousAnalysisStatus'] = item.analysisStatus;
        item['_analyzeDisabled'] = true;
      }
    });
  }

  /**
   * Re-enable all analyze buttons after analysis completes
   */
  enableAllAnalyzeButtons() {
    this.tableData.forEach((item) => {
      if (item['_analyzeDisabled']) {
        item['_analyzeDisabled'] = false;
        // Restore previous status if it was saved
        if (item['_previousAnalysisStatus']) {
          item.analysisStatus = item['_previousAnalysisStatus'];
          delete item['_previousAnalysisStatus'];
        }
      }
    });

    // Reset background mode
    this.runInBackground = false;
  }

  // Fallback to REST API if WebSocket fails
  fallbackToRestApi(index: number, dbId: string) {
    console.log('Falling back to REST API for database analysis');

    // Update UI to show we're trying an alternative method
    this.tableData[index].analysisStatus = 'Retrying Analysis...';

    // Update the modal if it's still open
    if (
      this.analysisModalMode === 'loading' ||
      this.analysisModalMode === 'error'
    ) {
      this.currentAnalysisOperation = 'Trying alternative analysis method...';
      this.showAnalysisModal('loading');
    }

    // Call the refresh analytics endpoint as a fallback
    this.dbService.refreshDatabaseAnalytics(dbId).subscribe({
      next: () => {
        // After successful refresh, fetch the object count
        const DB_Main = this.tableData[index].DB_MainConnection;
        this.fetchObjectCount(dbId, DB_Main, index);
      },
      error: (error) => {
        console.error('Error in fallback analysis:', error);
        this.tableData[index].analysisStatus = 'Analysis Failed';

        // Show error in modal if not in background mode
        if (!this.runInBackground) {
          this.analysisErrorMessage =
            'Analysis failed. Please try again later.';
          if (error.error && error.error.detail) {
            this.analysisErrorMessage = error.error.detail;
          }
          this.showAnalysisModal('error');
        }

        // Re-enable analyze buttons
        this.enableAllAnalyzeButtons();
      },
    });
  }

  // Generate dependency chart after analysis is complete
  generateDependencyChart(index: number, dbId: string) {
    this.tableData[index].dependencyStatus = 'Generating...';

    // Update the modal if it's still open and not in background mode
    if (this.analysisModalMode === 'loading' && !this.runInBackground) {
      this.currentAnalysisOperation = 'Generating dependency chart...';
    }

    this.dbService.generateDependencyChart(dbId).subscribe({
      next: (data) => {
        // Both analysis and dependency chart generation are complete
        console.log('Dependency chart data:', data);
        this.tableData[index].dependencyStatus = 'Generated';

        // Update the modal if it's still open and not in background mode
        if (this.analysisModalMode === 'loading' && !this.runInBackground) {
          this.currentAnalysisOperation =
            'Dependency chart generated successfully';
          // Now show success modal
          this.showAnalysisModal('success');
        }
      },
      error: (error) => {
        console.error('Error generating dependency chart:', error);
        // Even if dependency chart fails, mark as analyzed but log the error
        this.tableData[index].dependencyStatus = 'Generation Failed';

        // Update the modal if it's still open and not in background mode
        if (this.analysisModalMode === 'loading' && !this.runInBackground) {
          // Still show success for the analysis, but note the chart generation failure
          this.currentAnalysisOperation =
            'Analysis complete, but dependency chart generation failed';
          this.showAnalysisModal('success');
        }
      },
    });
  }

  // Fetch object count only after analysis is completed
  fetchObjectCount(dbId: string, DB_Main: string, index: number) {
    this.dbService.getDatabaseAnalytics(dbId, DB_Main).subscribe({
      next: (response) => {
        console.log('Object Count:', response.total_objects);
        this.tableData[index].objectCount = response.total_objects;
        this.tableData[index].analysisStatus = 'Analysis Complete';

        // Update the object count for the success modal
        this.analysisObjectCount = response.total_objects;

        // Show toast if in background mode, otherwise keep the loading modal open
        // (success modal will be shown after chart generation completes)
        if (this.runInBackground) {
          this.showSuccessToast(
            `Analysis of ${this.selectedDatabaseName} completed successfully`
          );
        }

        // Now trigger dependency chart generation
        this.generateDependencyChart(index, dbId);

        // Refresh table data to get the latest information
        this.fetchDatabases();

        // Re-enable analyze buttons
        this.enableAllAnalyzeButtons();
      },
      error: (error) => {
        console.error('Error fetching object count:', error);

        // Show error in modal if not in background mode
        if (!this.runInBackground) {
          this.analysisErrorMessage =
            'Failed to fetch analysis results. Please try again later.';
          this.showAnalysisModal('error');
        }

        // Re-enable analyze buttons
        this.enableAllAnalyzeButtons();
      },
    });
  }

  goToDatabaseDetails(
    DB_MainConnection: string,
    database_id: string,
    db_Type: string
  ) {
    console.log(DB_MainConnection, database_id, db_Type);
    if (DB_MainConnection && database_id) {
      // Store the latest database info (overwrites previous one)
      localStorage.setItem(
        'activeDatabaseInfo',
        JSON.stringify({
          connection: DB_MainConnection,
          id: database_id,
          type: db_Type,
        })
      );
      this.router.navigate(['/database', DB_MainConnection], {
        queryParams: { id: database_id, type: db_Type },
      });
    } else {
      alert('Missing required database details.');
    }
  }

  // Toast notification for success messages
  showSuccessToast(message: string) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className =
      'fixed bottom-4 right-4 bg-success text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center';
    toast.style.animation =
      'fadeInUp 0.3s ease-out forwards, fadeOut 0.3s ease-in forwards 3s';

    // Add success icon
    const icon = document.createElement('i');
    icon.className = 'ti ti-check-circle mr-2 text-xl';
    toast.appendChild(icon);

    // Add message
    const text = document.createElement('span');
    text.textContent = message;
    toast.appendChild(text);

    // Add toast to body
    document.body.appendChild(toast);

    // Remove toast after animation completes
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 3500);
  }

  /**
   * Toggle the action menu for a specific item
   * @param id The database ID
   * @param event The click event
   */
  toggleActionMenu(id: string, event?: MouseEvent) {
    // Find the item in the table data
    const item = this.tableData.find((item) => item.database_id === id);

    if (!item) {
      this.closeActionMenu();
      return;
    }

    // If the menu is already open for this item, close it
    if (
      this.activeActionMenu &&
      this.activeActionMenuItem?.database_id === id
    ) {
      this.closeActionMenu();
      return;
    }

    // Otherwise, open the menu for this item
    this.activeActionMenuItem = item;
    this.activeActionMenu = true;

    // Position the menu near the button that was clicked, but to the left
    if (event) {
      // Position relative to the click event, but offset to the left
      const menuWidth = 150; // Approximate width of the menu
      let leftPosition = event.clientX - menuWidth; // Position to the left of the click

      // Ensure the menu doesn't go off the left edge of the screen
      if (leftPosition < 10) {
        leftPosition = 10;
      }

      this.actionMenuPosition = {
        top: event.clientY,
        left: leftPosition,
      };
    } else {
      // Find the button element by its data attribute
      const button = document.querySelector(
        `button[data-id="${id}"]`
      ) as HTMLElement;
      if (button) {
        const rect = button.getBoundingClientRect();
        const menuWidth = 150; // Approximate width of the menu
        let leftPosition = rect.left - menuWidth - 5; // Position to the left of the button with a small gap

        // Ensure the menu doesn't go off the left edge of the screen
        if (leftPosition < 10) {
          leftPosition = 10;
        }

        this.actionMenuPosition = {
          top: rect.top + window.scrollY, // Align with top of button
          left: leftPosition,
        };
      } else {
        // Fallback position
        this.actionMenuPosition = {
          top: 100,
          left: 100,
        };
      }
    }

    // Add a click event listener to the document to close the menu when clicking outside
    setTimeout(() => {
      document.addEventListener('click', this.handleDocumentClick);
    }, 0);
  }

  /**
   * Close the action menu
   */
  closeActionMenu() {
    this.activeActionMenu = false;
    this.activeActionMenuItem = null;
    document.removeEventListener('click', this.handleDocumentClick);
  }

  /**
   * Handle document click to close the action menu when clicking outside
   */
  handleDocumentClick = (event: MouseEvent) => {
    // Check if the click was inside the action menu
    const actionMenu = document.querySelector(
      '.action-menu-portal'
    ) as HTMLElement;
    if (actionMenu && !actionMenu.contains(event.target as Node)) {
      this.closeActionMenu();
    }
  };

  /**
   * Close all open dropdowns and menus
   */
  closeDropdowns() {
    this.closeActionMenu();
  }
}

import {
  <PERSON><PERSON><PERSON>,
  ViewChild,
  OnInit,
  AfterViewInit,
  On<PERSON><PERSON>roy,
  Renderer2,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DatabaseService } from 'src/app/core/services/database/database.service';
import { ChartComponent } from 'src/app/shared/components/chart/chart.component';
import { Subject } from 'rxjs';
import { takeUntil, finalize } from 'rxjs/operators';

@Component({
  selector: 'app-dependency-chart',
  templateUrl: './dependency-chart.component.html',
  styleUrls: ['./dependency-chart.component.css'],
  styles: [
    `
      @keyframes progress {
        0% {
          width: 15%;
        }
        50% {
          width: 85%;
        }
        100% {
          width: 15%;
        }
      }

      .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      .animate-spin-slow {
        animation: spin 8s linear infinite;
      }

      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      .animate-bounce-slow {
        animation: bounce 3s infinite;
      }

      @keyframes bounce {
        0%,
        100% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(-10px);
        }
      }

      .animate-ping-slow {
        animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
      }

      @keyframes ping {
        75%,
        100% {
          transform: scale(1.2);
          opacity: 0;
        }
      }

      @keyframes shimmer {
        0% {
          transform: translateX(-100%);
        }
        100% {
          transform: translateX(100%);
        }
      }

      .delay-100 {
        animation-delay: 0.1s;
      }

      .delay-200 {
        animation-delay: 0.2s;
      }

      .delay-300 {
        animation-delay: 0.3s;
      }
    `,
  ],
})
export class DependencyChartComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  connectionName: string | null = '';
  @ViewChild('chartComp') chartComponent!: ChartComponent;

  private destroy$ = new Subject<void>();

  // Toggle controls for UI
  showTable = true;
  showChart = true;
  isLoading = false;

  // Table data and metadata
  tableData: {
    id: any;
    objectName: any;
    objectType: any;
    selected?: boolean;
  }[] = [];

  columns = [
    { key: 'objectName', label: 'Object Name' },
    { key: 'objectType', label: 'Object Type' },
  ];

  // Table pagination properties
  selectedCount = 0;
  sortColumn: string = '';
  sortAsc = true;
  searchTerm: string = '';
  selectedType: string = 'All';
  pageSizeOptions = [20, 50, 100];
  pageSize = 50;
  currentPage = 1;
  totalItems: number = 0;
  totalPages: number = 1;
  hasNextPage: boolean = false;
  hasPreviousPage: boolean = false;

  // Type counts from API
  typeCounts: {
    procedure: number;
    function: number;
    view: number;
    table: number;
  } = { procedure: 0, function: 0, view: 0, table: 0 };

  // Chart pagination properties
  chartTotalItems: number = 0;
  chartTotalPages: number = 1;
  chartCurrentPage: number = 1;
  chartPageSize: number = 50;
  chartHasNextPage: boolean = false;
  chartHasPreviousPage: boolean = false;

  // Chart data
  nodes: any[] = [];
  edges: any[] = [];

  // Track the currently selected node ID
  selectedNodeId: string | null = null;

  // Summary related properties
  isLoadingSummary: boolean = false;
  summaryError: string | null = null;
  objectSummary: string | null = null;
  selectedObjectName: string = '';
  selectedObjectType: string = '';
  copySuccess: boolean = false;
  isCopying: boolean = false;
  longLoadingMessage: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private dbService: DatabaseService,
    private renderer: Renderer2
  ) {
    console.log('DependencyChartComponent initialized');
    // Ensure the service is properly initialized
    if (this.dbService) {
      console.log('DatabaseService is available');
    } else {
      console.error('DatabaseService is not available');
    }
  }

  ngOnInit(): void {
    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      this.connectionName = params.get('connectionName');
    });

    this.route.queryParamMap
      .pipe(takeUntil(this.destroy$))
      .subscribe((queryParams) => {
        let databaseId = queryParams.get('id');

        if (!databaseId || databaseId.length < 36) {
          console.warn('Invalid or missing database ID. Redirecting...');
          this.router.navigate(['/database']);
          return;
        }

        // Store database info in localStorage
        localStorage.setItem(
          'activeDatabaseInfo',
          JSON.stringify({ connection: this.connectionName, id: databaseId })
        );

        // Get initial node type from query params if available
        const nodeTypeParam = queryParams.get('nodeType') || '';
        const page = parseInt(queryParams.get('page') || '1', 10);
        const pageSize = parseInt(queryParams.get('pageSize') || '50', 10);

        // Get the node to highlight if specified
        const highlightNode = queryParams.get('highlightNode');

        // Set component state from URL parameters - use 'procedure' as default instead of 'All'
        this.selectedType = nodeTypeParam ? nodeTypeParam : 'procedure';
        this.currentPage = page;
        this.chartCurrentPage = page;
        this.pageSize = pageSize;
        this.chartPageSize = pageSize;

        // Always use 'procedure' as the nodeType to ensure the chart loads
        console.log('Initializing with procedure filter');
        this.loadData(
          databaseId,
          this.selectedType,
          page,
          pageSize,
          highlightNode
        );

        console.log('Connected to:', this.connectionName);
      });
  }

  ngAfterViewInit(): void {
    // Check if chart component is available
    setTimeout(() => {
      if (!this.chartComponent) {
        console.warn('Chart component not found via ViewChild!');
      } else {
        console.log('Chart component found via ViewChild');

        // Check if we have data but chart hasn't been initialized
        if (this.nodes.length > 0 && this.edges.length > 0) {
          console.log(
            'Data is available but chart may not be initialized. Refreshing chart...'
          );
          // Force chart to update with current data
          this.chartComponent.ngOnChanges({
            nodesData: { currentValue: this.nodes, firstChange: false } as any,
            edgesData: { currentValue: this.edges, firstChange: false } as any,
          });
        }
      }
    }, 300);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Load all data (both table and chart)
  loadData(
    databaseId: string,
    nodeType: string,
    page: number,
    pageSize: number,
    highlightNode?: string | null
  ): void {
    this.isLoading = true;
    console.log('Loading data with params:', {
      databaseId,
      nodeType,
      page,
      pageSize,
      highlightNode,
    });

    // Add a small delay to ensure component is fully initialized
    setTimeout(() => {
      // Fetch chart elements
      this.fetchChartElements(databaseId, page, pageSize, nodeType);

      // Load table data
      this.loadDiagramTypes(nodeType, page, pageSize);

      // If a specific node should be highlighted, set up a timer to do it after data is loaded
      if (highlightNode) {
        this.setupNodeHighlighting(highlightNode);
      }
    }, 300);
  }

  // Set up a timer to highlight a specific node after data is loaded
  private setupNodeHighlighting(nodeName: string): void {
    // Wait for data to be loaded and chart to be initialized
    const checkInterval = setInterval(() => {
      if (!this.isLoading && this.nodes.length > 0 && this.chartComponent) {
        clearInterval(checkInterval);

        // Find the node with the matching name
        const nodeToHighlight = this.nodes.find(
          (node) => node.data.label.toLowerCase() === nodeName.toLowerCase()
        );

        if (nodeToHighlight) {
          console.log('Highlighting node:', nodeToHighlight.data.id);
          setTimeout(() => {
            this.highlightSubGraphFromParent(nodeToHighlight.data.id);

            // Scroll the table to show the selected row if table is visible
            if (this.showTable) {
              this.scrollToSelectedRow();
            }
          }, 500); // Additional delay to ensure chart is fully rendered
        } else {
          console.warn('Node not found for highlighting:', nodeName);

          // Try to find the node in the table data
          const tableNode = this.tableData.find(
            (item) => item.objectName.toLowerCase() === nodeName.toLowerCase()
          );

          if (tableNode) {
            console.log(
              'Found node in table data, highlighting:',
              tableNode.id
            );
            setTimeout(() => {
              this.highlightSubGraphFromParent(tableNode.id);

              // Scroll the table to show the selected row if table is visible
              if (this.showTable) {
                this.scrollToSelectedRow();
              }
            }, 500);
          }
        }
      }
    }, 300);

    // Set a timeout to clear the interval if it takes too long
    setTimeout(() => clearInterval(checkInterval), 10000);
  }

  // Highlight a node in the chart
  highlightSubGraphFromParent(id: string) {
    console.log('Attempting to highlight subgraph for node:', id);

    // Set the selected node ID
    this.selectedNodeId = id;

    // Update the table to highlight the selected row
    this.updateTableSelection(id);

    if (this.chartComponent) {
      try {
        this.chartComponent.highlightSubGraph(id);
      } catch (err) {
        console.error('Error highlighting subgraph:', err);
      }
    } else {
      console.error('Cannot highlight subgraph: Chart component not available');
    }
  }

  // Show summary modal and fetch summary data
  showSummary() {
    if (!this.selectedNodeId) {
      console.warn('No node selected for summary');
      return;
    }

    // Find the selected node in the table data
    const selectedItem = this.tableData.find(
      (item) => item.id === this.selectedNodeId
    );
    if (!selectedItem) {
      console.error('Selected node not found in table data');
      return;
    }

    // Set selected object details
    this.selectedObjectName = selectedItem.objectName;
    this.selectedObjectType = selectedItem.objectType;

    // Reset summary state
    this.objectSummary = null;
    this.summaryError = null;
    this.isLoadingSummary = true;
    this.copySuccess = false;

    // Open the modal
    const modalCheckbox = document.getElementById(
      'summaryModal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
    }

    // Get database ID from localStorage
    const activeDbInfo = localStorage.getItem('activeDatabaseInfo');
    const databaseId = activeDbInfo ? JSON.parse(activeDbInfo).id : null;

    if (!databaseId) {
      this.isLoadingSummary = false;
      this.summaryError = 'Database ID not found';
      return;
    }

    // Reset the loading state flags
    this.longLoadingMessage = false;

    // Set a timeout to show a more detailed message if generation takes too long
    const loadingMessageTimeout = setTimeout(() => {
      if (this.isLoadingSummary) {
        // Update the loading message in the component
        this.longLoadingMessage = true;
        console.log('Summary generation is taking longer than expected...');
      }
    }, 3500);

    // Add a minimum loading time to prevent flickering for fast responses
    const minLoadingTime = 1200; // 1.2 seconds minimum loading time
    const startTime = Date.now();

    // Fetch summary from API
    this.dbService
      .getOrGenerateSummary(
        databaseId,
        this.selectedObjectName,
        this.selectedObjectType
      )
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          // Calculate elapsed time
          const elapsedTime = Date.now() - startTime;

          // If response was too fast, add a delay to show the loading animation
          if (elapsedTime < minLoadingTime) {
            const remainingTime = minLoadingTime - elapsedTime;
            setTimeout(() => {
              this.isLoadingSummary = false;
              clearTimeout(loadingMessageTimeout);
            }, remainingTime);
          } else {
            // If enough time has passed, update immediately
            this.isLoadingSummary = false;
            clearTimeout(loadingMessageTimeout);
          }
        })
      )
      .subscribe({
        next: (response) => {
          if (response && response.summary) {
            // Ensure the summary is properly formatted
            this.objectSummary = this.formatSummary(response.summary);
          } else {
            this.summaryError = 'No summary available for this object';
          }
        },
        error: (error) => {
          console.error('Error fetching summary:', error);
          this.summaryError = 'Failed to generate summary. Please try again.';
        },
      });
  }

  // Copy summary to clipboard
  copySummary() {
    if (!this.objectSummary) {
      return;
    }

    this.isCopying = true;

    navigator.clipboard
      .writeText(this.objectSummary)
      .then(() => {
        this.copySuccess = true;
        setTimeout(() => {
          this.copySuccess = false;
          this.isCopying = false;
        }, 2000);
      })
      .catch((error) => {
        console.error('Failed to copy summary:', error);
        this.isCopying = false;
      });
  }

  // Format the summary to ensure proper markdown rendering
  private formatSummary(summary: string): string {
    // If the summary is already well-formatted markdown, return it as is
    if (
      summary.includes('###') ||
      summary.includes('##') ||
      summary.includes('#')
    ) {
      return summary;
    }

    try {
      // Try to parse as JSON to see if it's a JSON string
      const jsonObj = JSON.parse(summary);
      if (typeof jsonObj === 'object') {
        // It's a valid JSON object, format it as markdown
        if (jsonObj.summary) {
          // If it has a summary field, use that directly
          return jsonObj.summary;
        } else {
          // Format the JSON object as a markdown document
          let formattedSummary = '### Database Object Summary\n\n';

          // Add each property as a section
          Object.keys(jsonObj).forEach((key) => {
            if (key === 'object_name' || key === 'objectName') {
              formattedSummary += `**Object Name:** ${jsonObj[key]}\n\n`;
            } else if (key === 'object_type' || key === 'objectType') {
              formattedSummary += `**Object Type:** ${jsonObj[key]}\n\n`;
            } else if (key === 'summary') {
              formattedSummary += `${jsonObj[key]}\n\n`;
            } else if (key === 'dependencies' && Array.isArray(jsonObj[key])) {
              formattedSummary += `### Dependencies\n\n`;
              jsonObj[key].forEach((dep: any) => {
                formattedSummary += `* **${
                  dep.name || dep.object_name
                }** (Type: ${dep.type || dep.object_type})\n`;
              });
              formattedSummary += '\n';
            } else {
              // Format other properties
              formattedSummary += `**${key
                .replace(/_/g, ' ')
                .replace(/\b\w/g, (l) => l.toUpperCase())}:** ${
                jsonObj[key]
              }\n\n`;
            }
          });

          return formattedSummary;
        }
      }
    } catch (e) {
      // Not valid JSON, continue with other formatting
    }

    // Add some basic formatting if it's plain text
    if (
      !summary.includes('\n\n') &&
      !summary.includes('*') &&
      !summary.includes('_')
    ) {
      return `### Summary\n\n${summary}`;
    }

    return summary;
  }

  // Update the table selection based on the selected node ID
  private updateTableSelection(nodeId: string): void {
    // Reset all selections first
    this.tableData.forEach((item) => {
      item.selected = false;
    });

    // Find the matching item in the table data and mark it as selected
    const selectedItem = this.tableData.find((item) => item.id === nodeId);
    if (selectedItem) {
      selectedItem.selected = true;
      console.log('Selected table item:', selectedItem.objectName);

      // Scroll to the selected row
      this.scrollToSelectedRow();
    } else {
      console.warn('Selected node not found in table data:', nodeId);
    }
  }

  // Scroll to the selected row in the table
  private scrollToSelectedRow(): void {
    // Give the DOM a moment to update
    setTimeout(() => {
      const selectedRow = document.querySelector('tr.selected-row');
      if (selectedRow) {
        selectedRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
        console.log('Scrolled to selected row');
      } else {
        console.warn('Selected row element not found for scrolling');
      }
    }, 100);
  }

  // Toggle UI components
  toggleTable() {
    this.showTable = !this.showTable;
  }

  toggleChart() {
    this.showChart = !this.showChart;
  }

  // Load diagram types for the table
  loadDiagramTypes(
    nodeType: string = 'procedure',
    page: number = 1,
    pageSize: number = 50
  ): void {
    // Force nodeType to be 'procedure' if it's empty
    if (!nodeType) {
      nodeType = 'procedure';
    }

    this.isLoading = true;
    console.log(`Loading diagram types with nodeType=${nodeType}`);

    this.dbService
      .getDiagramNodeTypes(nodeType, page, pageSize)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          const tableDataArray: {
            id: any;
            objectName: any;
            objectType: any;
          }[] = [];
          const { data, pagination } = response;

          // Loop through each category in the data object
          for (const type in data) {
            if (data.hasOwnProperty(type) && Array.isArray(data[type])) {
              const items = data[type];
              items.forEach((item: any) => {
                tableDataArray.push({
                  id: item.id,
                  objectName: item.label,
                  objectType: type, // 'procedures', 'functions', 'views', 'table'
                });
              });
            }
          }

          this.tableData = tableDataArray;
          console.log('Table data loaded:', this.tableData.length, 'items');

          // Store pagination info
          if (pagination) {
            this.totalItems = pagination.total_nodes;
            this.totalPages = pagination.total_pages;
            this.currentPage = pagination.current_page;
            this.pageSize = pagination.page_size;
            this.hasNextPage = pagination.has_next;
            this.hasPreviousPage = pagination.has_previous;

            // Store type counts
            if (pagination.type_counts) {
              this.typeCounts = pagination.type_counts;
            }
          }

          this.isLoading = false;
        },
        error: (error) => {
          console.error('Failed to load diagram types:', error);
          this.isLoading = false;
          this.tableData = [];
        },
      });
  }

  // Fetch chart elements
  fetchChartElements(
    dbId: string,
    page: number = 1,
    pageSize: number = 50,
    nodeType: string = 'procedure'
  ): void {
    // Force nodeType to be 'procedure' if it's empty
    if (!nodeType) {
      nodeType = 'procedure';
    }

    console.log(
      `Fetching chart elements: dbId=${dbId}, page=${page}, pageSize=${pageSize}, nodeType=${nodeType}`
    );

    if (!dbId) {
      console.error('Cannot fetch chart elements: No database ID provided');
      this.nodes = [];
      this.edges = [];
      this.isLoading = false;
      return;
    }

    this.isLoading = true;

    // Add a delay to ensure the component is fully initialized
    setTimeout(() => {
      console.log(`Making API call with nodeType=${nodeType}`);
      this.dbService
        .getDatabaseElements(dbId, page, pageSize, nodeType)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            console.log('Processed API Response:', response);

            if (response?.nodes?.length) {
              console.log(`Found ${response.nodes.length} nodes`);

              try {
                this.nodes = response.nodes.map((node: any) => ({
                  data: {
                    id: node.data.id,
                    label: node.data.label,
                    type: node.data.type,
                  },
                }));

                // Even if edges array is empty, make sure we initialize it
                this.edges = response.edges
                  ? response.edges.map((edge: any) => ({
                      data: {
                        id: edge.data.id,
                        label: edge.data.label || '', // Ensure label is never undefined
                        source: edge.data.source,
                        target: edge.data.target,
                      },
                    }))
                  : [];

                console.log('Formatted Nodes:', this.nodes.length);
                console.log('Formatted Edges:', this.edges.length);

                // Skip validation for now
                // this.validateGraphData();

                // Store pagination info if available
                if (response.pagination) {
                  this.chartTotalItems = response.pagination.total_nodes;
                  this.chartTotalPages = response.pagination.total_pages;
                  this.chartCurrentPage = response.pagination.current_page;
                  this.chartPageSize = response.pagination.page_size;
                  this.chartHasNextPage = response.pagination.has_next;
                  this.chartHasPreviousPage = response.pagination.has_previous;
                }
              } catch (err) {
                console.error('Error processing chart data:', err);
                this.nodes = [];
                this.edges = [];
              }
            } else {
              console.warn('No nodes found in the response:', response);
              // Initialize empty arrays to avoid undefined errors
              this.nodes = [];
              this.edges = [];
            }

            this.isLoading = false;
          },
          error: (error) => {
            console.error('Error fetching chart elements:', error);
            // Initialize empty arrays on error
            this.nodes = [];
            this.edges = [];
            this.isLoading = false;
          },
        });
    }, 300); // Add a small delay to ensure component is ready
  }

  // Validate graph data integrity
  validateGraphData(): void {
    // Check for missing IDs in nodes
    const nodesWithoutIds = this.nodes.filter((node) => !node.data.id);
    if (nodesWithoutIds.length > 0) {
      console.error('Found nodes without IDs:', nodesWithoutIds);
    }

    // Check for missing source/target in edges
    const invalidEdges = this.edges.filter(
      (edge) => !edge.data.source || !edge.data.target || !edge.data.id
    );
    if (invalidEdges.length > 0) {
      console.error('Found invalid edges:', invalidEdges);
    }

    // Check for edges referencing non-existent nodes
    const nodeIds = new Set(this.nodes.map((node) => node.data.id));
    const danglingEdges = this.edges.filter(
      (edge) => !nodeIds.has(edge.data.source) || !nodeIds.has(edge.data.target)
    );
    if (danglingEdges.length > 0) {
      console.error(
        'Found edges referencing non-existent nodes:',
        danglingEdges
      );
    }
  }

  // Server-side filtering method
  onTypeChange(event: any): void {
    this.selectedType = event.target.value;
    this.applyFilter(this.selectedType.toLowerCase());
  }

  // Server-side search (if supported by API)
  onSearchChange(event: any): void {
    this.searchTerm = event.target.value;
    // For now, keep client-side search if API doesn't support searching
    // If API adds search support, modify this to call the API with search term
  }

  // Server-side pagination method for table
  changePage(newPage: number): void {
    if (newPage >= 1 && newPage <= this.totalPages) {
      this.currentPage = newPage;

      // Get current node type filter
      const nodeType = this.selectedType.toLowerCase();

      // Call API for new page of table data
      this.loadDiagramTypes(nodeType, this.currentPage, this.pageSize);

      // Update URL to reflect current page
      this.updateUrl(nodeType, this.currentPage, this.pageSize);
    }
  }

  // Server-side page size change for table
  onPageSizeChange(event: any): void {
    const newSize = Number(event.target.value);
    if (this.pageSize !== newSize) {
      this.pageSize = newSize;
      this.currentPage = 1; // Reset to first page

      // Get current node type filter
      const nodeType = this.selectedType.toLowerCase();

      // Call API with new page size
      this.loadDiagramTypes(nodeType, this.currentPage, this.pageSize);

      // Update URL to reflect new page size
      this.updateUrl(nodeType, this.currentPage, this.pageSize);
    }
  }

  // Server-side chart pagination method
  changeChartPage(newPage: number): void {
    if (newPage >= 1 && newPage <= this.chartTotalPages) {
      this.chartCurrentPage = newPage;

      // Get current database ID and node type filter
      const activeDbInfo = localStorage.getItem('activeDatabaseInfo');
      const databaseId = activeDbInfo ? JSON.parse(activeDbInfo).id : null;
      const nodeType = this.selectedType.toLowerCase();

      if (databaseId) {
        // Fetch new page of chart data
        this.fetchChartElements(
          databaseId,
          this.chartCurrentPage,
          this.chartPageSize,
          nodeType
        );

        // Update URL to reflect current chart page
        this.updateUrl(nodeType, this.chartCurrentPage, this.chartPageSize);
      }
    }
  }

  // Unified method to apply filters to both chart and table
  applyFilter(nodeType: string): void {
    // Reset both pagination components to page 1
    this.currentPage = 1;
    this.chartCurrentPage = 1;

    // Get current database ID
    const activeDbInfo = localStorage.getItem('activeDatabaseInfo');
    const databaseId = activeDbInfo ? JSON.parse(activeDbInfo).id : null;

    if (databaseId) {
      // Fetch filtered data for both components
      this.fetchChartElements(databaseId, 1, this.chartPageSize, nodeType);
      this.loadDiagramTypes(nodeType, 1, this.pageSize);

      // Update URL with filter
      this.updateUrl(nodeType, 1, this.pageSize);
    }
  }

  // Helper method to update URL without reloading the page
  private updateUrl(nodeType: string, page: number, pageSize: number): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {
        nodeType: nodeType || null,
        page: page,
        pageSize: pageSize,
      },
      queryParamsHandling: 'merge', // Keep other params like 'id'
    });
  }

  // Client-side filter for local searching (if needed)
  get filteredData(): any[] {
    // If you still need client-side search while waiting for API support
    if (this.searchTerm) {
      return this.tableData.filter((item) =>
        item.objectName.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
    return this.tableData;
  }

  // Calculate current page range for display
  get currentPageStart(): number {
    return Math.min(
      (this.currentPage - 1) * this.pageSize + 1,
      this.totalItems
    );
  }

  get currentPageEnd(): number {
    return Math.min(this.currentPageStart + this.pageSize - 1, this.totalItems);
  }

  // Get unique types from data (can be replaced with API type_counts)
  get uniqueTypes(): string[] {
    // Using API type_counts is more efficient
    return Object.keys(this.typeCounts).filter(
      (type) => this.typeCounts[type as keyof typeof this.typeCounts] > 0
    );
  }

  // Pagination navigation methods
  prevPage() {
    if (this.currentPage > 1) this.changePage(this.currentPage - 1);
  }

  nextPage() {
    if (this.currentPage < this.totalPages)
      this.changePage(this.currentPage + 1);
  }

  goToPage(page: number) {
    this.changePage(page);
  }

  // Create pagination display with ellipsis for large page counts
  get visiblePageNumbers(): (number | string)[] {
    const total = this.totalPages;
    const current = this.currentPage;

    if (total <= 5) {
      return Array.from({ length: total }, (_, i) => i + 1);
    }

    const pages: (number | string)[] = [];

    pages.push(1); // Always show first

    if (current > 3) pages.push('...');

    const start = Math.max(2, current - 1);
    const end = Math.min(total - 1, current + 1);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    if (current < total - 2) pages.push('...');

    pages.push(total); // Always show last

    return pages;
  }
}

import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserManagementComponent } from './user-management.component';
import { OrganizationDetailsComponent } from './organization-details/organization-details.component';
import { RoleGuard } from '../../core/guards/role.guard';

const routes: Routes = [
  {
    path: '',
    component: UserManagementComponent,
    canActivate: [RoleGuard],
    data: {
      roles: ['Admin'],
      permissions: [
        'create_users',
        'edit_users',
        'delete_users',
        'create_roles',
        'edit_roles',
        'delete_roles',
      ],
    },
  },
  {
    path: 'organization/:id',
    component: OrganizationDetailsComponent,
    canActivate: [RoleGuard],
    data: {
      roles: ['Admin'],
      permissions: [
        'create_users',
        'edit_users',
        'delete_users',
        'create_roles',
        'edit_roles',
        'delete_roles',
      ],
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UserManagementRoutingModule {}

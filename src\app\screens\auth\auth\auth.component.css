.auth-container {
  min-height: 100vh; /* Ensure full viewport height */
  width: 100%;
  background-color: #f4f4f4;
  background-image: url("/assets/bg-login.png"); /* Update with your image path */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed; /* Keeps background in place */
}

@media (max-width: 768px) {
  .auth-container {
    background-attachment: scroll; /* Fix for mobile */
  }
}

.auth-card {
  border-radius: 20px;
  border: 1px solid var(--card-border);
  background: var(--card-background);
  backdrop-filter: blur(16px);
  transition: all 0.3s ease-in-out;
}

/* Light Theme */
.auth-card[data-theme="light"] {
  --card-background: rgba(255, 255, 255, 0.5);
  --card-border: #ffffff;
  --card-text: #080619;
  color: var(--card-text);
}

/* Dark Theme */
.auth-card[data-theme="dark"] {
  --card-background: rgba(0, 0, 0, 0.41);
  --card-border: #333333;
  --card-text: #ffffff;
  color: var(--card-text);
}

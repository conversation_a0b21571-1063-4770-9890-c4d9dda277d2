import { Component } from '@angular/core';
import { AuthService } from '../../../core/auth/auth.service';

@Component({
  selector: 'app-auth',
  templateUrl: './auth.component.html',
  styleUrls: ['./auth.component.css'],
})
export class AuthComponent {
  currentView: 'login' | 'register' | 'forgot-password' = 'login'; // Default view
  email: string = '';
  password: string = '';
  showPassword: boolean = false;
  full_name: string = '';
  errorMessage: string = '';
  theme: string = 'light';
  constructor(private authService: AuthService) {}

  switchView(view: 'login' | 'register' | 'forgot-password') {
    this.currentView = view;
  }

  ngOnInit() {
    // Load the theme from localStorage
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      this.theme = savedTheme;
      document.body.classList.add(this.theme); // Apply the theme to the body
    }
  }
  login() {
    this.authService.login(this.email, this.password).subscribe({
      next: () => {
        this.errorMessage = ''; // Clear error message on success
      },
      error: (error) => {
        console.error('Login failed:', error);
        this.errorMessage = 'Invalid email or password!';
      },
    });
  }
  signUp() {
    console.log(this.email, this.password, this.full_name);
    this.authService
      .signup(this.full_name, this.email, this.password)
      .subscribe({
        next: (response: any) => {
          console.log('User signed up successfully:', response);
          alert('Sign-up successful! You can now log in.');
          this.switchView('login');
        },
        error: (error: any) => {
          console.error('Sign-up error:', error);
          this.errorMessage = 'Sign-up failed. Please try again.';
        },
      });
  }

  toggleTheme() {
    // Toggle between 'light' and 'dark' themes
    this.theme = this.theme === 'light' ? 'dark' : 'light';

    // Save the theme in localStorage
    localStorage.setItem('theme', this.theme);

    // Update the body class
    document.body.classList.remove('light', 'dark');
    document.body.classList.add(this.theme);
  }
}

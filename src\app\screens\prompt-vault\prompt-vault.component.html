<!-- Main content -->
<div>
  <div class="flex items-center justify-between mb-4">
    <div class="form-control w-1/4 max-w-md relative">
      <select
        class="select select-sm h-9 min-h-0 w-full bg-backgroundPrimary text-content1 border-[var(--border-color)] pl-3 pr-8 rounded-md"
        [(ngModel)]="selectedType"
        (ngModelChange)="onFilterChange()"
        style="
          appearance: none;
          -webkit-appearance: none;
          -moz-appearance: none;
        "
      >
        <option value="">All Types</option>
        <option *ngFor="let type of types" [value]="type">{{ type }}</option>
      </select>
    </div>
    <div class="flex gap-2">
      <div class="form-control relative max-w-md">
        <input
          type="text"
          placeholder="Search..."
          class="input input-sm h-9 min-h-0 w-full pl-10 bg-backgroundPrimary text-content1 border-[var(--border-color)] rounded-md"
          [(ngModel)]="searchQuery"
          (keyup.enter)="onFilterChange()"
        />
        <button
          class="absolute inset-y-0 right-2 flex items-center text-content2"
          (click)="onFilterChange()"
          title="Search"
        >
          <i class="ti ti-search-check"></i>
        </button>
        <span class="absolute inset-y-0 left-3 flex items-center text-content2">
          <i class="ti ti-search"></i>
        </span>
      </div>
      <button
        *ngIf="permissionsLoaded && canCreatePrompt"
        class="btn btn-primary btn-sm h-9 min-h-0 rounded-md"
        (click)="openCreateModal()"
      >
        <i class="ti ti-plus text-base mr-1"></i>Create Prompt
      </button>
    </div>
  </div>
</div>
<!-- Loading state -->
<div *ngIf="loadingData" class="flex justify-center my-8">
  <div
    class="absolute animate-spin rounded-full h-8 w-8 border-b-2 border-primary top-[50%]"
  ></div>
</div>

<div
  *ngIf="!loadingData"
  class="prompt-vault-table overflow-hidden rounded-lg border shadow-sm"
>
  <table
    class="table table-compact table-hover w-full border-collapse custom-table border border-[var(--border-color)] table-fixed"
  >
    <thead class="bg-backgroundPrimary sticky top-0 z-10">
      <tr>
        <th
          class="text-left font-medium text-content1 border-r border-b border-[var(--border-color)]"
          style="width: 35%"
        >
          Prompt
        </th>
        <th
          class="text-left font-medium text-content1 border-r border-b border-[var(--border-color)]"
          style="width: 12%"
        >
          Type
        </th>
        <th
          class="text-left font-medium text-content1 border-r border-b border-[var(--border-color)]"
          style="width: 12%"
        >
          Source
        </th>
        <th
          class="text-left font-medium text-content1 border-r border-b border-[var(--border-color)]"
          style="width: 12%"
        >
          Target
        </th>
        <th
          class="text-right font-medium text-content1 border-b border-[var(--border-color)]"
          style="width: 12%"
        >
          Action
        </th>
      </tr>
    </thead>
    <tbody>
      <!-- Data rows -->
      <ng-container *ngIf="paginatedTableData && paginatedTableData.length > 0">
        <tr
          *ngFor="let item of paginatedTableData; let i = index"
          (click)="onRowClicked(item)"
          class="cursor-pointer border-b transition-all duration-200"
          [ngStyle]="{ 'animation-delay': i * 0.05 + 's' }"
          style="animation: fadeInRow 0.5s ease-out forwards"
        >
          <td class="border-r border-[var(--border-color)]">
            <div class="font-semibold text-content1 mb-1 truncate">
              {{ item.promptName }}
            </div>
            <div
              class="text-sm text-content2 text-wrap line-clamp-2 break-words"
            >
              {{ item.description }}
            </div>
          </td>
          <td
            class="text-content1 border-r border-[var(--border-color)] truncate"
          >
            {{ item.type }}
          </td>
          <td
            class="text-content1 border-r border-[var(--border-color)] truncate"
          >
            {{ item.source }}
          </td>
          <td
            class="text-content1 border-r border-[var(--border-color)] truncate"
          >
            {{ item.target }}
          </td>
          <td class="text-right">
            <!-- Edit button - only show if user has edit permission -->
            <button
              *ngIf="permissionsLoaded && canEditPrompt"
              (click)="editPrompt(item); $event.stopPropagation()"
              class="btn btn-sm btn-ghost mr-2 h-9 w-9 min-h-0 p-0 rounded-full"
              title="Edit"
            >
              <i class="ti ti-pencil text-content1 text-lg"></i>
            </button>

            <!-- Delete button - only show if user has delete permission -->
            <button
              *ngIf="permissionsLoaded && canDeletePrompt"
              (click)="openDeleteModal(item); $event.stopPropagation()"
              class="btn btn-sm btn-ghost h-9 w-9 min-h-0 p-0 rounded-full"
              title="Delete"
            >
              <i class="ti ti-trash text-error text-lg"></i>
            </button>

            <!-- View button - visible for all users -->
            <button
              (click)="viewPromptDetails(item); $event.stopPropagation()"
              class="btn btn-sm btn-ghost h-9 w-9 min-h-0 p-0 rounded-full"
              title="View Details"
            >
              <i class="ti ti-eye text-primary text-lg"></i>
            </button>
          </td>
        </tr>
      </ng-container>

      <!-- Empty state -->
      <tr *ngIf="!paginatedTableData || paginatedTableData.length === 0">
        <td colspan="5" class="py-16 text-center">
          <div class="flex flex-col items-center justify-center gap-4">
            <div class="rounded-full bg-backgroundSecondary p-4 mb-2 shadow-md">
              <i
                class="ti ti-file-search text-4xl text-primary empty-state-icon"
              ></i>
            </div>
            <h3 class="text-xl font-medium text-content1">No prompts found</h3>
            <p class="text-content2">
              {{
                searchQuery
                  ? "No prompts match your search criteria. Try adjusting your filters or search term."
                  : canCreatePrompt
                  ? "There are no prompts in the vault yet. Create your first prompt to get started."
                  : "There are no prompts in the vault yet."
              }}
            </p>
            <button
              *ngIf="permissionsLoaded && canCreatePrompt"
              class="btn btn-primary btn-sm mt-4 transition-all hover:shadow-md"
              (click)="openCreateModal()"
            >
              <i class="ti ti-plus text-base mr-1"></i>Create Prompt
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </table>

  <div
    *ngIf="paginatedTableData && paginatedTableData.length > 0"
    class="flex justify-between items-center bg-backgroundPrimary p-4 border-t"
  >
    <div class="flex flex-row items-center space-x-4">
      <div
        class="flex items-center px-3 py-1.5 bg-backgroundPrimary rounded-md border border-[var(--border-color)]"
      >
        <i class="ti ti-list text-primary mr-2"></i>
        <h2 class="text-sm font-medium text-content2 whitespace-nowrap">
          Showing
          <span class="text-content1 font-semibold">{{
            currentPageStart
          }}</span>
          to
          <span class="text-content1 font-semibold">{{ currentPageEnd }}</span>
          of
          <span class="text-content1 font-semibold">{{ totalEntries }}</span>
          prompts
        </h2>
      </div>
      <div class="relative dropdown" style="width: 120px">
        <div
          class="flex items-center bg-backgroundPrimary rounded-md border border-[var(--border-color)]"
        >
          <select
            class="select select-sm w-full rounded-md bg-backgroundPrimary text-content1 border-0 pl-3 pr-8 transition-all focus:ring-2 focus:ring-primary/20"
            [(ngModel)]="pageSize"
            (change)="changePageSize($event)"
            style="
              appearance: none;
              -webkit-appearance: none;
              -moz-appearance: none;
            "
          >
            <option *ngFor="let size of pageSizeOptions" [value]="size">
              {{ size }} per page
            </option>
          </select>
          <div
            class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-content2"
          >
            <i class="ti ti-chevron-down transition-transform text-primary"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Updated pagination section -->
    <div class="pagination flex items-center">
      <div
        class="flex items-center rounded-md bg-backgroundPrimary border border-[var(--border-color)] p-0.5"
      >
        <button
          class="btn btn-sm h-8 min-h-0 btn-ghost flex items-center text-content2 px-2 transition-all hover:bg-primary/10 hover:text-primary rounded-l-md rounded-r-none border-r border-[var(--border-color)]"
          (click)="prevPage()"
          [disabled]="currentPage === 1"
        >
          <span claass="flex items-center"
            ><i class="ti ti-chevron-left mr-1"></i>Prev</span
          >
        </button>

        <div class="flex items-center px-1">
          <ng-container *ngIf="totalPages <= 7">
            <button
              *ngFor="let page of getPagesArray()"
              class="btn btn-sm h-7 min-h-0 w-7 p-0 mx-0.5 transition-all"
              [ngClass]="{
                'btn-primary text-white shadow-sm': currentPage === page,
                'btn-ghost text-content2 hover:bg-primary/10 hover:text-primary':
                  currentPage !== page
              }"
              (click)="goToPage(page)"
            >
              {{ page }}
            </button>
          </ng-container>

          <ng-container *ngIf="totalPages > 7">
            <!-- First page -->
            <button
              *ngIf="currentPage > 3"
              class="btn btn-sm h-7 min-h-0 w-7 p-0 mx-0.5 btn-ghost text-content2 transition-all hover:bg-primary/10 hover:text-primary"
              (click)="goToPage(1)"
            >
              1
            </button>

            <!-- Ellipsis if needed -->
            <span *ngIf="currentPage > 4" class="px-1 text-content2">...</span>

            <!-- Pages around current page -->
            <ng-container *ngFor="let page of getVisiblePages()">
              <button
                class="btn btn-sm h-7 min-h-0 w-7 p-0 mx-0.5 transition-all"
                [ngClass]="{
                  'btn-primary text-white shadow-sm': currentPage === page,
                  'btn-ghost text-content2 hover:bg-primary/10 hover:text-primary':
                    currentPage !== page
                }"
                (click)="goToPage(page)"
              >
                {{ page }}
              </button>
            </ng-container>

            <!-- Ellipsis if needed -->
            <span
              *ngIf="currentPage < totalPages - 3"
              class="px-1 text-content2"
              >...</span
            >

            <!-- Last page -->
            <button
              *ngIf="currentPage < totalPages - 2"
              class="btn btn-sm h-7 min-h-0 w-7 p-0 mx-0.5 btn-ghost text-content2 transition-all hover:bg-primary/10 hover:text-primary"
              (click)="goToPage(totalPages)"
            >
              {{ totalPages }}
            </button>
          </ng-container>
        </div>

        <button
          class="btn btn-sm h-8 min-h-0 btn-ghost flex items-center text-content2 px-2 transition-all hover:bg-primary/10 hover:text-primary rounded-r-md rounded-l-none border-l border-[var(--border-color)]"
          (click)="nextPage()"
          [disabled]="currentPage === totalPages"
        >
          <span claass="flex items-center"
            >Next <i class="ti ti-chevron-right ml-1"></i
          ></span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Combined Modal for Create/Edit Prompt -->
<input class="modal-state" id="newPrompt" type="checkbox" />
<div class="modal w-screen">
  <label class="modal-overlay"></label>
  <div
    class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%] bg-backgroundPrimary"
  >
    <!-- Modal Header -->
    <div class="modal-header">
      <h2 class="text-xl font-medium">
        {{ isEditMode ? "Edit Prompt" : "Create New Prompt" }}
      </h2>
      <label for="newPrompt" class="modal-close-btn"
        ><i class="ti ti-x"></i
      ></label>
    </div>

    <!-- Modal Body -->
    <div class="py-4 px-6 justify-center border-b">
      <div class="form-group w-full mb-2">
        <!-- Prompt Title -->
        <div class="form-field">
          <label class="form-label text-content1 mb-1">Prompt Title*</label>
          <input
            placeholder="Prompt Title"
            type="text"
            class="input max-w-full bg-backgroundPrimary text-content1 border-[var(--border-color)]"
            [class.input-error]="formSubmitted && !newPrompt.title?.trim()"
            [(ngModel)]="newPrompt.title"
            [disabled]="loading"
          />
        </div>

        <!-- Description -->
        <div class="form-field mb-4">
          <div class="flex flex-row justify-between">
            <label class="form-label text-content1">Description*</label>
            <label
              class="form-label character-count"
              [ngClass]="getCharacterCountClass()"
            >
              {{ newPrompt.description?.length || 0 }}/600 Characters
            </label>
          </div>
          <textarea
            placeholder="Description"
            class="textarea max-w-full bg-backgroundPrimary text-content1 border-[var(--border-color)]"
            rows="4"
            [class.input-error]="
              formSubmitted && !newPrompt.description?.trim()
            "
            [(ngModel)]="newPrompt.description"
            [disabled]="loading"
            (input)="limitCharacters()"
            maxlength="600"
          ></textarea>
        </div>

        <!-- Prompt Type -->
        <div class="form-field mb-4">
          <label class="form-label text-content1 mb-1"
            >Prompt Type Can be Applied to*</label
          >
          <select
            class="select select-block bg-backgroundPrimary text-content1 border-[var(--border-color)]"
            [class.select-error]="formSubmitted && !newPrompt.prompt_type"
            [(ngModel)]="newPrompt.prompt_type"
            [disabled]="loading"
          >
            <option value="" disabled>Select Type</option>
            <option>All</option>
            <option>View</option>
            <option>Procedure</option>
            <option>Table</option>
            <option>Function</option>
            <option>Trigger</option>
            <option>API</option>
          </select>
        </div>

        <!-- Source DB -->
        <div class="form-field mb-4">
          <label class="form-label text-content1 mb-1">Source*</label>
          <select
            class="select select-block bg-backgroundPrimary text-content1 border-[var(--border-color)]"
            [class.select-error]="formSubmitted && !newPrompt.source_db_type"
            [(ngModel)]="newPrompt.source_db_type"
            (ngModelChange)="updateFilteredDBLists()"
            [disabled]="loading"
          >
            <option value="" disabled>Select Source DB</option>
            <option *ngFor="let db of filteredSourceDBList" [value]="db.key">
              {{ db.name }}
            </option>
          </select>
        </div>

        <!-- Target DB -->
        <div class="form-field mb-4">
          <label class="form-label text-content1 mb-1">Target*</label>
          <select
            class="select select-block bg-backgroundPrimary text-content1 border-[var(--border-color)]"
            [class.select-error]="formSubmitted && !newPrompt.target_db_type"
            [(ngModel)]="newPrompt.target_db_type"
            (ngModelChange)="updateFilteredDBLists()"
            [disabled]="loading"
          >
            <option value="" disabled>Select Target DB</option>
            <option *ngFor="let db of filteredTargetDBList" [value]="db.key">
              {{ db.name }}
            </option>
            <option value="api">API</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Footer Buttons -->
    <div class="modal-footer">
      <label for="newPrompt" class="btn btn-outline btn-sm">Cancel</label>
      <button
        class="btn btn-primary btn-sm"
        (click)="savePrompt()"
        [disabled]="loading"
      >
        <span *ngIf="!loading">{{ isEditMode ? "Update" : "Create" }}</span>
        <div class="spinner-dot-circle spinner-sm" *ngIf="loading">
          <div class="spinner-dot"></div>
          <div class="spinner-dot"></div>
          <div class="spinner-dot"></div>
          <div class="spinner-dot"></div>
          <div class="spinner-dot"></div>
          <div class="spinner-dot"></div>
          <div class="spinner-dot"></div>
          <div class="spinner-dot"></div>
        </div>
        <span *ngIf="loading" class="loading loading-spinner loading-sm">
          {{ isEditMode ? "Updating" : "Sending" }}
        </span>
      </button>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div
  *ngIf="showDeleteModal"
  class="fixed inset-0 z-50 flex items-center justify-center modal-overlay"
>
  <div
    class="modal-content p-0 flex flex-col max-w-md min-w-[40%] bg-backgroundPrimary rounded-lg shadow-lg"
  >
    <!-- Modal Header -->
    <div class="modal-header">
      <h2 class="text-xl font-medium">Confirm Delete</h2>
      <button class="modal-close-btn" (click)="showDeleteModal = false">
        <i class="ti ti-x"></i>
      </button>
    </div>

    <!-- Modal Body -->
    <div class="py-6 px-6 flex items-center justify-center">
      <div class="w-full text-center">
        <div
          class="mb-4 mx-auto w-16 h-16 flex items-center justify-center rounded-full bg-error/10"
        >
          <i class="ti ti-alert-triangle text-3xl text-error"></i>
        </div>
        <h3 class="text-lg font-medium text-content1 mb-2">Delete Prompt</h3>
        <p class="text-content2 mb-4">
          Are you sure you want to delete <br />
          <strong class="text-content1">{{ promptToDelete?.promptName }}</strong
          >? <br />This action cannot be undone.
        </p>
      </div>
    </div>

    <!-- Footer Buttons -->
    <div class="modal-footer">
      <button class="btn btn-outline btn-sm" (click)="showDeleteModal = false">
        Cancel
      </button>
      <button class="btn btn-error btn-sm" (click)="confirmDeletePrompt()">
        <i class="ti ti-trash mr-1"></i> Delete
      </button>
    </div>
  </div>
</div>

<!-- View Details Modal -->
<input class="modal-state" id="viewPrompt" type="checkbox" />
<div class="modal w-screen">
  <label class="modal-overlay"></label>
  <div
    class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%] bg-backgroundPrimary"
  >
    <!-- Modal Header -->
    <div class="modal-header">
      <h2 class="text-xl font-medium">Prompt Details</h2>
      <label for="viewPrompt" class="modal-close-btn"
        ><i class="ti ti-x"></i
      ></label>
    </div>

    <!-- Modal Body -->
    <div class="py-4 px-6 flex items-center justify-center overflow-y-auto">
      <div class="w-full">
        <div class="grid grid-cols-1 gap-4">
          <!-- Prompt Name -->
          <div class="mb-4">
            <h4 class="text-sm font-semibold text-content2 mb-1">
              Prompt Name
            </h4>
            <p class="text-content1 font-medium">
              {{ promptToView?.promptName }}
            </p>
          </div>

          <!-- Description -->
          <div class="mb-4">
            <h4 class="text-sm font-semibold text-content2 mb-1">
              Description
            </h4>
            <p class="text-content1">{{ promptToView?.description }}</p>
          </div>

          <!-- Type, Source, Target -->
          <div class="grid grid-cols-3 gap-4 mb-4">
            <div>
              <h4 class="text-sm font-semibold text-content2 mb-1">Type</h4>
              <p class="text-content1">{{ promptToView?.type }}</p>
            </div>
            <div>
              <h4 class="text-sm font-semibold text-content2 mb-1">Source</h4>
              <p class="text-content1">{{ promptToView?.source }}</p>
            </div>
            <div>
              <h4 class="text-sm font-semibold text-content2 mb-1">Target</h4>
              <p class="text-content1">{{ promptToView?.target }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Buttons -->
    <div class="modal-footer">
      <label for="viewPrompt" class="btn btn-primary btn-sm">Close</label>
    </div>
  </div>
</div>

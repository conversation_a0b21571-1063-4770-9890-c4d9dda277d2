.graph-container {
  width: 100%;
  height: calc(100% - 50px); /* Subtract legend height */
  min-height: 400px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--bg-backgroundPrimary);
}

.chart-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.legend {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px;
  background-color: var(--bg-backgroundPrimary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  margin-bottom: 8px;
  font-size: 12px;
  flex-shrink: 0;
  color: var(--text-primary);
}

.legend > div {
  display: flex;
  align-items: center;
  margin-right: 8px;
  cursor: default;
  user-select: none;
}

.legend-box {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  display: inline-block;
  margin-right: 6px;
}

.legend-box.view {
  background-color: #3498db;
}

.legend-box.table {
  background-color: #e74c3c;
}

.legend-box.procedure {
  background-color: #9b59b6;
}

.legend-box.function {
  background-color: #f39c12;
}

.legend-box.trigger {
  background-color: #2ecc71;
}

.legend-box.disabled {
  opacity: 0.4;
}

.strikethrough {
  text-decoration: line-through;
  opacity: 0.6;
}

.control-buttons {
  display: flex;
  gap: 5px;
  margin-left: auto;
}

/* Loading indicator */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-overlay);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.spinner {
  border: 5px solid var(--border-color);
  border-top: 5px solid var(--primary);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Improved button styles */
.btn-control {
  padding: 4px 8px;
  font-size: 14px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-control:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Utility classes */
.hidden {
  display: none !important;
}

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DatabaseRoutingModule } from './database-routing.module';
import { DatabaseComponent } from './database.component';
import { SharedModule } from 'src/app/shared/shared/shared.module';
import { DatabaseDetailsComponent } from './database-details/database-details.component';
import { DependencyChartComponent } from './dependency-chart/dependency-chart.component';
import { CodeReviewComponent } from './code-review/code-review.component';

@NgModule({
  declarations: [DatabaseComponent, DatabaseDetailsComponent, DependencyChartComponent, CodeReviewComponent],
  imports: [CommonModule, DatabaseRoutingModule, SharedModule],
})
export class DatabaseModule {}

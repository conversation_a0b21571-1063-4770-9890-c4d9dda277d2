import { Component } from '@angular/core';
@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.css'],
})
export class SettingsComponent {
  columns = [
    { key: 'connectionName', label: 'Connection Name' },
    { key: 'dbHost', label: 'DB Host' },
    { key: 'dbName', label: 'DB Name' },
    { key: 'dbType', label: 'DB Type' },
    { key: 'objectCount', label: 'Object Count' },
    { key: 'analysisStatus', label: 'Analysis Status' },
  ];

  tableData = [
    {
      id: 1,
      connectionName: 'CRM',
      dbHost: 'revupmysql.mysql.da...',
      dbName: 'eretail',
      dbType: 'MYSQL',
      objectCount: 0,
      analysisStatus: 'Not Analyzed',
      selected: false,
    },
    {
      id: 2,
      connectionName: 'CRM',
      dbHost: 'revupmysql.mysql.da...',
      dbName: 'eretail',
      dbType: 'MYSQL',
      objectCount: 190,
      analysisStatus: '1/190 Analyzing',
      selected: false,
    },
    {
      id: 3,
      connectionName: 'sybase',
      dbHost: 'WSAMZN-FF4KFSF9',
      dbName: 'master',
      dbType: 'Sybase Ase',
      objectCount: 23,
      analysisStatus: '15/23 Analyzed',
      selected: false,
    },
    {
      id: 4,
      connectionName: 'sybase',
      dbHost: 'WSAMZN-FF4KFSF9',
      dbName: 'master',
      dbType: 'Sybase Ase',
      objectCount: 23,
      analysisStatus: '23/23 Analyzed',
      selected: false,
    },
    {
      id: 4,
      connectionName: 'sybase',
      dbHost: 'WSAMZN-FF4KFSF9',
      dbName: 'master',
      dbType: 'Sybase Ase',
      objectCount: 23,
      analysisStatus: '23/23 Analyzed',
      selected: false,
    },
    {
      id: 4,
      connectionName: 'sybase',
      dbHost: 'WSAMZN-FF4KFSF9',
      dbName: 'master',
      dbType: 'Sybase Ase',
      objectCount: 23,
      analysisStatus: '23/23 Analyzed',
      selected: false,
    },
    {
      id: 4,
      connectionName: 'sybase',
      dbHost: 'WSAMZN-FF4KFSF9',
      dbName: 'master',
      dbType: 'Sybase Ase',
      objectCount: 23,
      analysisStatus: '23/23 Analyzed',
      selected: false,
    },
  ];
}

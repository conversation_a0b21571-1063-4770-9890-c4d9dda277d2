<section class="rounded-lg border" [@fadeIn]>
  <div class="flex flex-col">
    <!-- Top Toolbar -->
    <div
      class="flex justify-between items-center bg-backgroundPrimary p-4 rounded-lg"
    >
      <h2 class="text-base font-medium">{{ selectedCount }} Selected</h2>
      <div class="flex gap-3">
        <button
          class="btn btn-outline-default btn-xs transition-all hover-elevate"
          [disabled]="selectedCount === 0"
        >
          <i class="ti ti-plug text-xs mr-1"></i> Connect
        </button>
        <button
          class="btn btn-outline-default btn-xs transition-all hover-elevate"
          [disabled]="selectedCount === 0"
        >
          <i class="ti ti-plug-off text-xs mr-1"></i> Disconnect
        </button>
        <button
          class="btn btn-outline-primary rounded-md btn-xs transition-all hover-elevate"
        >
          <i class="ti ti-analyze text-xs mr-1"></i> Analyze
        </button>
      </div>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
      <table class="table-compact table table-hover min-w-full">
        <thead>
          <tr>
            <!-- Sticky Checkbox Column -->
            <th class="sticky left-0 z-20 text-sm font-medium p-2 border-r">
              <input
                type="checkbox"
                class="checkbox"
                (change)="selectAll($event)"
              />
            </th>

            <!-- Dynamic Columns -->
            <th
              *ngFor="let col of columns; let i = index"
              class="p-2 text-left text-sm font-medium cursor-pointer sticky top-0"
              [ngClass]="{
                'right-0 z-10 bg-backgroundPrimary border-l':
                  i === columns.length - 1
              }"
              (click)="sort(col.key)"
            >
              <div class="border-none flex justify-between items-center">
                <span class="border-none">{{ col.label }}</span>
                <span
                  *ngIf="sortColumn === col.key"
                  class="sort-indicator ml-1"
                  [ngClass]="{ 'sort-asc': sortAsc, 'sort-desc': !sortAsc }"
                >
                  <i class="ti ti-chevron-up text-xs"></i>
                </span>
              </div>
            </th>
          </tr>
        </thead>

        <tbody [@listAnimation]="paginatedData.length">
          <tr
            *ngFor="let item of paginatedData; let i = index"
            [class.bg-gray-50]="item.selected"
            [class.selected]="item.selected"
            class="transition-all"
          >
            <!-- Sticky Checkbox Cell -->
            <td class="sticky left-0 bg-backgroundPrimary z-10 p-2 border-r">
              <input
                type="checkbox"
                class="checkbox"
                [(ngModel)]="item.selected"
                (change)="updateSelection()"
              />
            </td>

            <!-- Dynamic Columns -->
            <td
              *ngFor="let col of columns; let i = index"
              class="p-2"
              [ngClass]="{
                'text-primary': i === 1,
                'sticky right-0 z-10 bg-backgroundPrimary border-l':
                  i === columns.length - 1
              }"
            >
              <ng-container *ngIf="!col.custom">
                <!-- First Data Column (clickable row link) -->
                <span
                  *ngIf="i === 0"
                  class="flex items-center gap-2 font-medium text-primary hover:underline"
                  [class.cursor-pointer]="enableNavigation"
                  (click)="
                    onRowClick(
                      item.DB_MainConnection,
                      item.database_id,
                      item.dbType
                    )
                  "
                >
                  <span
                    *ngIf="item.status"
                    class="dot"
                    [ngClass]="getStatusClass(item.status)"
                  ></span>
                  {{ item[col.key] }}
                </span>

                <!-- Other Columns -->
                <span *ngIf="i !== 0">{{ item[col.key] }}</span>
              </ng-container>

              <!-- Custom Template -->
              <ng-container *ngIf="col.custom">
                <ng-container
                  *ngTemplateOutlet="
                    customTemplates[col.key];
                    context: { $implicit: item }
                  "
                ></ng-container>
              </ng-container>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <!-- Pagination UI -->
    <div
      class="flex justify-between items-center bg-backgroundPrimary p-4 rounded-lg"
    >
      <!-- Left: Pagination Info & Page Size Selection -->
      <div class="flex flex-row flex-1 items-center">
        <h2 class="text-base text-gray-500 text-nowrap">
          Showing {{ currentPageStart }} to {{ currentPageEnd }} of
          {{ totalEntries }} DB’s
        </h2>

        <select
          class="select select-sm rounded-md text-gray-500 mx-3 text-nowrap max-w-16"
          [(ngModel)]="pageSize"
          (change)="changePageSize($event)"
        >
          <option *ngFor="let size of pageSizeOptions" [value]="size">
            {{ size }}
          </option>
        </select>

        <p class="text-base text-gray-500 text-nowrap">per page</p>
      </div>

      <!-- Right: Pagination Controls -->
      <div class="pagination text-gray-500 flex gap-2">
        <button
          class="btn btn-xs btn-ghost transition-all"
          (click)="prevPage()"
          [disabled]="currentPage === 1"
        >
          <i class="ti ti-chevron-left text-xs"></i>
        </button>

        <ng-container *ngFor="let page of [1, 2, 3, 4, 5]">
          <button
            *ngIf="page <= totalPages"
            class="btn btn-xs transition-all"
            [ngClass]="{
              'btn-active': currentPage === page,
              'btn-ghost text-gray-500': currentPage !== page
            }"
            (click)="goToPage(page)"
          >
            {{ page }}
          </button>
        </ng-container>

        <button
          class="btn btn-xs btn-ghost transition-all"
          (click)="nextPage()"
          [disabled]="currentPage === totalPages"
        >
          <i class="ti ti-chevron-right text-xs"></i>
        </button>
      </div>
    </div>
  </div>
</section>

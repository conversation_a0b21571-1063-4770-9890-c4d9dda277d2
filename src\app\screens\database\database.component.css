.badge-flat-error,
.badge-flat-warning {
  --tw-bg-opacity: 1;
  --tw-text-opacity: 1;
  border-style: none;
}
.badge-flat-warning {
  background-color: rgb(var(--yellow-4) / var(--tw-bg-opacity));
  color: rgb(var(--yellow-11) / var(--tw-text-opacity));
}
/* This ensures dropdown menus appear above any overflow containers */
.dropdown {
  position: relative;
}

/* Switch styling with dark mode support */
.switch {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 20px;
  border-radius: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.switch:disabled {
  --tw-border-opacity: 1;
  --tw-bg-opacity: 1;
  background-color: var(--switch-bg-checked);
  border-color: var(--switch-border-checked);
  cursor: not-allowed;
  opacity: 0.7;
}

.switch:checked {
  --circle-color: #fff;
  --o: 1;
  --r: 43deg;
  background-color: var(--switch-bg-checked);
  border-color: var(--switch-border-checked);
  --x: 17px;
}

.switch-success {
  --switch-bg-checked: rgb(var(--success));
  --switch-border-checked: rgb(var(--success));
}

/* Dark mode switch styles */
[data-theme="dark"] .switch:disabled {
  background-color: rgba(var(--success), 0.7);
  border-color: rgba(var(--success), 0.7);
  opacity: 0.9;
}

[data-theme="dark"] .switch:checked:disabled {
  background-color: rgba(var(--success), 0.7);
  border-color: rgba(var(--success), 0.7);
}

[data-theme="dark"] .switch:disabled::before {
  background-color: rgba(255, 255, 255, 0.9);
}

/* Connection status switch specific styling */
.connection-status-switch {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.connection-status-switch .switch:disabled {
  box-shadow: 0 0 5px rgba(var(--success), 0.3);
}

/* Connected state */
.connection-status-switch .switch:checked:disabled {
  opacity: 1;
}

/* Disconnected state */
.connection-status-switch .switch:not(:checked):disabled {
  background-color: rgba(var(--error), 0.3);
  border-color: rgba(var(--error), 0.3);
  opacity: 0.8;
}

/* Dark mode specific styles */
[data-theme="dark"] .connection-status-switch .switch:checked:disabled {
  background-color: rgba(var(--success), 0.8);
  border-color: rgba(var(--success), 0.8);
  opacity: 1;
  box-shadow: 0 0 8px rgba(var(--success), 0.4);
}

[data-theme="dark"] .connection-status-switch .switch:not(:checked):disabled {
  background-color: rgba(var(--error), 0.4);
  border-color: rgba(var(--error), 0.4);
  opacity: 0.9;
  box-shadow: 0 0 8px rgba(var(--error), 0.3);
}

/* Sticky column styling - matching database-detail component */
/* Sticky columns for horizontal scrolling */
.table th.sticky-left,
.table td.sticky-left {
  position: sticky !important;
  left: 0 !important;
  z-index: 10 !important;
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  border-right: 1px solid var(--border-color) !important;
  box-shadow: 2px 0 5px -2px rgba(0, 0, 0, 0.1) !important;
}

.table th.sticky-right,
.table td.sticky-right {
  position: sticky !important;
  right: 0 !important;
  z-index: 10 !important;
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  border-left: 1px solid var(--border-color) !important;
  box-shadow: -2px 0 5px -2px rgba(0, 0, 0, 0.1) !important;
}

/* Increase z-index for header cells to appear above body cells */
.table thead th.sticky-left {
  z-index: 20 !important;
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.08),
    rgba(44, 206, 138, 0.03)
  ) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), 2px 0 5px -2px rgba(0, 0, 0, 0.1) !important;
}

.table thead th.sticky-right {
  z-index: 20 !important;
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.08),
    rgba(44, 206, 138, 0.03)
  ) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), -2px 0 5px -2px rgba(0, 0, 0, 0.1) !important;
}

/* Fix for sticky columns in table */
.overflow-x-auto {
  overflow-x: auto;
  position: relative;
  width: 100%;
}

/* Ensure table takes full width */
.table {
  width: 100%;
  table-layout: auto;
  position: relative;
}

/* Fix for the enhanced-table overflow */
.enhanced-table {
  overflow-x: hidden !important;
}

.enhanced-table .relative.overflow-x-auto {
  max-width: 100% !important;
  overflow-x: auto !important;
}

/* Ensure the scroll bar is inside the table container */
.enhanced-table .flex.flex-col {
  max-width: 100% !important;
}

/* Fix for RippleUI table-hover class that uses gray background */
.enhanced-table .table-hover tbody tr:hover td {
  background-color: transparent !important;
  border-color: var(--border-color) !important;
}

/* Override any hover styles that might make sticky columns transparent */
.enhanced-table .table-hover tbody tr:hover td.sticky-left,
.enhanced-table .table-hover tbody tr:hover td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  opacity: 1 !important;
}

/* Animation for table rows */
@keyframes fadeInRow {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Empty state styling */
.empty-state-icon {
  font-size: 3rem;
  opacity: 0.7;
  margin-bottom: 1rem;
}

/* Dark mode header styles for sticky columns */
[data-theme="dark"] .table thead th.sticky-left,
[data-theme="dark"] .table thead th.sticky-right {
  background-color: rgba(148, 0, 255, 0.05) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.12),
    rgba(44, 206, 138, 0.05)
  ) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2), 2px 0 5px -2px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .table thead th.sticky-right {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2), -2px 0 5px -2px rgba(0, 0, 0, 0.3) !important;
}

/* Ensure proper background colors in dark mode */
[data-theme="dark"] .table th.sticky-left,
[data-theme="dark"] .table td.sticky-left {
  background-color: var(--backgroundPrimary) !important;
  box-shadow: 2px 0 5px -2px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .table th.sticky-right,
[data-theme="dark"] .table td.sticky-right {
  background-color: var(--backgroundPrimary) !important;
  box-shadow: -2px 0 5px -2px rgba(0, 0, 0, 0.3) !important;
}

/* Add shadow effect to indicate scrollable content */
.table td.sticky-left::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  pointer-events: none;
  box-shadow: inset -2px 0 4px -2px rgba(0, 0, 0, 0.1);
}

.table td.sticky-right::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
  pointer-events: none;
  box-shadow: inset 2px 0 4px -2px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .table td.sticky-left::after {
  box-shadow: inset -2px 0 4px -2px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .table td.sticky-right::after {
  box-shadow: inset 2px 0 4px -2px rgba(0, 0, 0, 0.3);
}

/* Fix for hover state with sticky columns */
.table tr:hover td.sticky-left,
.table tr:hover td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.03),
    rgba(148, 0, 255, 0.01)
  ) !important;
  opacity: 1 !important;
}

/* Ensure hover doesn't make sticky columns transparent */
.table-hover tbody tr:hover td.sticky-left,
.table-hover tbody tr:hover td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  opacity: 1 !important;
}

/* Direct override for RippleUI table hover styles */
.table.table-hover > tbody > tr:hover > td.sticky-left,
.table.table-hover > tbody > tr:hover > td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.03),
    rgba(148, 0, 255, 0.01)
  ) !important;
  opacity: 1 !important;
}

/* Dark mode hover styles */
[data-theme="dark"] .table tr:hover td.sticky-left,
[data-theme="dark"] .table tr:hover td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.05),
    rgba(148, 0, 255, 0.02)
  ) !important;
  opacity: 1 !important;
}

/* Dark mode direct override for RippleUI table hover styles */
[data-theme="dark"] .table.table-hover > tbody > tr:hover > td.sticky-left,
[data-theme="dark"] .table.table-hover > tbody > tr:hover > td.sticky-right {
  background-color: rgb(
    var(--backgroundPrimary) / var(--tw-bg-opacity, 1)
  ) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.05),
    rgba(148, 0, 255, 0.02)
  ) !important;
  opacity: 1 !important;
}

/* Toast animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Form validation styles */
.input-error {
  border-color: rgb(var(--error)) !important;
  background-color: rgba(var(--error), 0.05) !important;
}

.text-error {
  color: rgb(var(--error)) !important;
}

/* Shake animation for invalid inputs */
@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-5px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(5px);
  }
}

.input-error:focus {
  animation: shake 0.5s ease-in-out;
  box-shadow: 0 0 0 2px rgba(var(--error), 0.2) !important;
}

import { Component, Input } from '@angular/core';
interface caroselImage {
  text: string;
  desc?: string;
  imageSrc: string;
  imageAlt: string;
}
@Component({
  selector: 'app-carousel',
  templateUrl: './carousel.component.html',
  styleUrls: ['./carousel.component.css'],
})
export class CarouselComponent {
  @Input() images: caroselImage[] = [];
  @Input() indicators = true;
  @Input() autoSlider = false;
  @Input() sliderInterval = 3000; //
  selectedIndex = 0;

  ngOnInit(): void {
    if (this.autoSlider) {
      this.autoSliderImage();
    }
  }

  //auto Change slider
  autoSliderImage(): void {
    setInterval(() => {
      this.onNextCLick();
    }, this.sliderInterval);
  }

  selectImage(index: number): void {
    this.selectedIndex = index;
  }

  onNextCLick(): void {
    if (this.selectedIndex === this.images.length - 1) {
      this.selectedIndex = 0;
    } else {
      this.selectedIndex++;
    }
  }

  // Get current theme from localStorage
  getTheme(): string {
    return localStorage.getItem('theme') || 'light';
  }
}

/* Use global CSS variables for consistency */
:root {
  --border: 226 226 226;
  --card-shadow: rgba(0, 0, 0, 0.1);
  --text-muted: rgba(0, 0, 0, 0.6);
  --hover-elevation: 0 8px 15px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] {
  --border: 52 52 52;
  --card-shadow: rgba(0, 0, 0, 0.3);
  --text-muted: rgba(255, 255, 255, 0.7);
  --hover-elevation: 0 8px 15px rgba(0, 0, 0, 0.3);
}

.border {
  border-color: rgb(var(--border) / var(--tw-bg-opacity));
}

.border-t {
  border-color: rgb(var(--border) / var(--tw-bg-opacity));
}

/* Text colors that adapt to theme */
.text-adaptive {
  color: var(--text-muted);
}

/* Enhanced Stats Card Styles */
div.hover-shadow {
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}

div.hover-shadow::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0;
  background: linear-gradient(to bottom, rgba(148, 0, 255, 0.05), transparent);
  transition: height 0.3s ease-in-out;
  z-index: 0;
  pointer-events: none;
}

[data-theme="dark"] div.hover-shadow::before {
  background: linear-gradient(to bottom, rgba(148, 0, 255, 0.1), transparent);
}

div.hover-shadow:hover {
  transform: translateY(-2px);
  box-shadow: var(--hover-elevation);
}

[data-theme="dark"] div.hover-shadow:hover {
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.5);
  background-color: rgba(
    36,
    36,
    36,
    0.95
  ); /* Slightly lighter than backgroundPrimary */
  border-color: rgba(75, 75, 75, 1) !important; /* Slightly lighter border */
}

div.hover-shadow:hover::before {
  height: 100%;
}

/* Icon container animation */
div.py-1.px-2.rounded-md {
  transition: all 0.3s ease;
}

div.hover-shadow:hover div.py-1.px-2.rounded-md {
  transform: scale(1.05);
}

/* Icon animation */
i.transition-all {
  transition: all 0.3s ease;
}

div.hover-shadow:hover i.transition-all {
  transform: scale(1.1);
}

/* Badge animation */
.badge {
  transition: all 0.3s ease;
}

div.hover-shadow:hover .badge {
  box-shadow: 0 0 8px rgba(148, 0, 255, 0.3);
}

[data-theme="dark"] div.hover-shadow:hover .badge {
  box-shadow: 0 0 8px rgba(148, 0, 255, 0.5);
}

/* Progress bar styles are now in global CSS for consistency */

/* Progress bar animations are now in global CSS for consistency */

/* Pulse animation for badges */
@keyframes subtle-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

div.hover-shadow:hover .badge {
  animation: subtle-pulse 2s ease-in-out infinite;
}

import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DatabaseService } from 'src/app/core/services/database/database.service';
import { PromptsService } from 'src/app/core/services/prompts/prompts.service';
import { AuthService } from 'src/app/core/auth/auth.service';

@Component({
  selector: 'app-database-details',
  templateUrl: './database-details.component.html',
  styleUrls: ['./database-details.component.css'],
})
export class DatabaseDetailsComponent implements OnInit {
  connectionName: string | null = '';
  enableNavigation: boolean = true;
  // Track document generation status for each object
  generatingDocs: { [objectName: string]: boolean } = {};

  // Table data properties with index signature to allow string indexing
  tableData: {
    id: string;
    objectName: any;
    objectType: any;
    deployed: any;
    transform: any;
    doc: string;
    transformed_into: string;
    [key: string]: any; // Index signature to allow accessing properties by string
  }[] = [];

  // Database connection status
  isConnected: boolean = false;

  // Table view modal properties
  selectedTableInfo: any = null;

  // Filtering variables
  searchTerm: string = '';
  objectTypeFilter: string = 'all';
  transformStatusFilter: string = 'all';
  uniqueObjectTypes: string[] = [];

  // Pagination variables
  currentPage: number = 1;
  pageSize: number = 5;
  pageSizeOptions: number[] = [5, 10, 15];

  // Template references
  @ViewChild('action') actionTemplate!: TemplateRef<any>;
  @ViewChild('dependencyChart') dependencyChartTemplate!: TemplateRef<any>;
  @ViewChild('businessDoc') businessDocTemplate!: TemplateRef<any>;
  @ViewChild('code') codeTemplate!: TemplateRef<any>;
  @ViewChild('deployed') deployedTemplate!: TemplateRef<any>;

  // For Transform modal
  selectedObjectName: string = '';
  selectedObjectType: string = '';
  selectedDbType: string = '';
  transformToApi: boolean = true;
  transformToRdbms: boolean = true;
  stepIndex: number = 0;
  completedSteps: number[] = [];
  selectedCard: number = 1;
  searchDB: string = '';
  promptData: any[] = [];
  selectedOptions: string[] = [];
  data: any;

  // Modal state
  loading: boolean = false;
  isLoading: boolean = false; // Enhanced loading state
  longLoadingMessage: boolean = false; // Show additional message for long loading times
  loadingTimeout: any; // For tracking loading time
  success: boolean = false;
  successMessage: string = 'Transformation successful!';
  errorMessage: string = '';
  errorState: boolean = false; // Track error state
  errorDetails: string = ''; // Detailed error information

  // User permissions
  isSuperUser: boolean = false;
  isAdmin: boolean = false;
  userPermissions: string[] = [];
  permissionsLoaded: boolean = false;

  // Permission flags for specific actions
  canGenerateDocument: boolean = false;
  canDownloadDocument: boolean = false;
  canTransformCode: boolean = false;
  canViewCode: boolean = false;
  canDeployCode: boolean = false;

  // Database List for target databases
  DBList = [
    { id: 1, key: 'sybase', name: 'Sybase', icon: 'assets/sybase.png' },
    {
      id: 2,
      key: 'postgresql',
      name: 'PostgreSQL',
      icon: 'assets/postgrace.png',
    },
    {
      id: 3,
      key: 'sql_server',
      name: 'SQL Server',
      icon: 'assets/sql server.png',
    },
    { id: 4, key: 'mysql', name: 'MySQL', icon: 'assets/mysql.png' },
    {
      id: 5,
      key: 'amazon_rds_pg',
      name: 'Amazon RDS PostgrSQL',
      icon: 'assets/rds.png',
    },
    {
      id: 6,
      key: 'amazon_athena',
      name: 'Amazon Athena',
      icon: 'assets/amzathena.png',
    },
    { id: 7, key: 'oracle', name: 'Oracle', icon: 'assets/oracle.png' },
  ];

  // Table columns definition
  columns = [
    { key: 'objectName', label: 'Object Name' },
    { key: 'objectType', label: 'Object Type' },
    { key: 'dependencyChart', label: 'Dependency Chart', custom: true },
    { key: 'businessDoc', label: 'Business Doc', custom: true },
    { key: 'code', label: 'Code', custom: true },
    { key: 'deployed', label: 'Cloud Deployment', custom: true },
    { key: 'action', label: '', custom: true },
  ];
  totalEntries: number | undefined;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private dbService: DatabaseService,
    private promptsService: PromptsService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      this.connectionName = params.get('connectionName');
    });

    // Load user permissions first, then load database info
    this.loadUserPermissions();

    // Load database info and other data
    this.loadDatabaseInfo();

    this.dbService.getDbIdAnalytics().subscribe((res) => {
      console.log('Summary:', res);
      this.data = res;
    });

    this.loadPrompts();
  }

  ngAfterViewInit(): void {
    // Assign the template after view initialization
    this.customTemplates = {
      action: this.actionTemplate,
      dependencyChart: this.dependencyChartTemplate,
      businessDoc: this.businessDocTemplate,
      code: this.codeTemplate,
      deployed: this.deployedTemplate,
    };
  }

  customTemplates: { [key: string]: TemplateRef<any> | null } = {};

  // Method to extract unique object types for filter dropdown
  extractUniqueObjectTypes(): void {
    const types = new Set<string>();
    this.tableData.forEach((item) => {
      if (item.objectType) {
        types.add(item.objectType);
      }
    });
    this.uniqueObjectTypes = Array.from(types);
  }

  // Reset all filters method
  resetFilters(): void {
    this.searchTerm = '';
    this.objectTypeFilter = 'all';
    this.transformStatusFilter = 'all';
    this.currentPage = 1;
  }

  // Get the filtered and paginated data
  get filteredData(): any[] {
    return this.tableData.filter((item) => {
      // Apply search filter
      const matchesSearch =
        !this.searchTerm ||
        item.objectName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        item.objectType.toLowerCase().includes(this.searchTerm.toLowerCase());

      // Apply object type filter
      const matchesObjectType =
        this.objectTypeFilter === 'all' ||
        item.objectType === this.objectTypeFilter;

      // Apply transform status filter
      const matchesTransformStatus =
        this.transformStatusFilter === 'all' ||
        (this.transformStatusFilter === 'transformed' &&
          item.transformed_into !== 'not transformed') ||
        (this.transformStatusFilter === 'not_transformed' &&
          item.transformed_into === 'not transformed');

      return matchesSearch && matchesObjectType && matchesTransformStatus;
    });
  }

  // Get paginated data
  get paginatedData(): any[] {
    const filtered = this.filteredData;
    this.totalEntries = filtered.length;

    const startIndex = (this.currentPage - 1) * this.pageSize;
    return filtered.slice(startIndex, startIndex + this.pageSize);
  }

  // For pagination display
  get currentPageStart(): number {
    return this.filteredData.length === 0
      ? 0
      : (this.currentPage - 1) * this.pageSize + 1;
  }

  get currentPageEnd(): number {
    return Math.min(this.currentPage * this.pageSize, this.filteredData.length);
  }

  get totalPages(): number {
    return Math.ceil(this.filteredData.length / this.pageSize);
  }

  // Generate an array of visible page numbers
  get pageNumbers(): number[] {
    const pages = [];
    const maxVisiblePages = 5;

    if (this.totalPages <= maxVisiblePages) {
      // If we have fewer pages than our maximum, show all
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Otherwise show a window around the current page
      let start = Math.max(1, this.currentPage - 2);
      let end = Math.min(this.totalPages, start + maxVisiblePages - 1);

      // Adjust if we're near the end
      if (end === this.totalPages) {
        start = Math.max(1, end - maxVisiblePages + 1);
      }

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
    }

    return pages;
  }

  // Get visible pages for pagination with ellipsis
  getVisiblePages(): number[] {
    const total = this.totalPages;
    const current = this.currentPage;
    const pages: number[] = [];

    // Show pages around current page
    const start = Math.max(2, current - 1);
    const end = Math.min(total - 1, current + 1);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    // Add last page if not included
    if (end < total - 1) {
      pages.push(total);
    }

    return pages;
  }

  // Pagination navigation methods
  goToPage(page: number): void {
    this.currentPage = page;
  }

  prevPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  changePageSize(event: any): void {
    this.pageSize = parseInt(event.target.value);
    this.currentPage = 1; // Reset to first page when changing page size
  }

  // Sort functionality
  sortColumn: string = '';
  sortAsc: boolean = true;

  sort(column: string): void {
    if (this.sortColumn === column) {
      this.sortAsc = !this.sortAsc;
    } else {
      this.sortColumn = column;
      this.sortAsc = true;
    }

    this.tableData = [...this.tableData].sort((a, b) => {
      const valA = a[column];
      const valB = b[column];

      if (valA < valB) return this.sortAsc ? -1 : 1;
      if (valA > valB) return this.sortAsc ? 1 : -1;
      return 0;
    });
  }

  // Load database info
  loadDatabaseInfo() {
    this.route.queryParamMap.subscribe((queryParams) => {
      let databaseId = queryParams.get('id');
      let databaseType = queryParams.get('type');

      if (!databaseId || databaseId.length < 36) {
        console.warn('Invalid or missing database ID. Redirecting...');
        this.router.navigate(['/database']);
        return;
      }

      if (databaseId) {
        // If ID is present in the URL, store it
        localStorage.setItem(
          'activeDatabaseInfo',
          JSON.stringify({
            connection: this.connectionName,
            id: databaseId,
            type: databaseType,
          })
        );
      } else {
        // If no ID in URL, retrieve the last stored one
        const storedData = localStorage.getItem('activeDatabaseInfo');
        if (storedData) {
          const parsedData = JSON.parse(storedData);

          if (parsedData.connection === this.connectionName) {
            databaseId = parsedData.id;
          }
        }
      }

      this.fetchDBdetails(databaseId, this.connectionName);
    });
  }

  // Fetch database details
  fetchDBdetails(dbId: any, DB_Main: any) {
    this.loading = true;
    this.dbService.getDatabaseAnalytics(dbId, DB_Main).subscribe(
      (response) => {
        console.log('Object Count:', response);

        // Check if the database is connected - handle case sensitivity
        if (response && response.connection_status) {
          const status = response.connection_status.toString().toLowerCase();
          this.isConnected = status === 'connected';
        } else {
          // Default to disconnected if status is not available
          this.isConnected = false;
        }

        console.log(
          'Database connection status:',
          this.isConnected ? 'Connected' : 'Disconnected'
        );
        console.log('Database connection details:', {
          response_connection_status: response?.connection_status,
          response_connection_status_lowercase: response?.connection_status
            ?.toString()
            .toLowerCase(),
          isConnected: this.isConnected,
          canTransformCode: this.canTransformCode,
          permissionsLoaded: this.permissionsLoaded,
          transformButtonShouldBeVisible:
            this.canTransformCode && this.isConnected,
        });

        // Check if response contains an 'objects' array
        if (response && Array.isArray(response.objects)) {
          this.tableData = response.objects.map((db: any, index: number) => ({
            id: index + 1,
            objectName: db.object_name,
            objectType: db.object_type,
            deployed: db.deployed,
            doc: db.doc,
            transformed_into: db.transformed_into,
          }));

          // After loading data, extract unique object types for filters
          this.extractUniqueObjectTypes();
          this.loading = false;
        } else {
          this.loading = false;
          console.error(
            'Error: Expected an array under "objects" but got',
            response
          );
          this.tableData = []; // Fallback to empty data
        }
      },
      (error) => {
        console.error('Error fetching object count:', error);
        this.isConnected = false; // Set to disconnected on error
        this.loading = false;
      }
    );
  }

  /**
   * View table information
   * @param objectName The table name
   * @param objectType The object type (table, view, etc.)
   */
  viewTableInfo(objectName: string, objectType: string) {
    console.log('View table info:', { objectName, objectType });

    // Don't show loading indicator initially
    this.loading = false;

    // Find the object directly in the existing tableData
    const existingObject = this.tableData.find(
      (obj) =>
        obj['objectName'] === objectName && obj['objectType'] === objectType
    );

    console.log('Found object in tableData:', existingObject);

    // Create the table info object directly from the table data
    this.selectedTableInfo = {
      objectName: objectName,
      objectType: objectType,
      transformed_into: existingObject
        ? existingObject['transformed_into'] || 'not transformed'
        : 'not transformed',
      doc: existingObject
        ? existingObject['doc'] || 'not generated'
        : 'not generated',
      description: existingObject ? existingObject['description'] || '' : '',
      created_at: existingObject ? existingObject['created_at'] || null : null,
    };

    console.log('Selected table info:', this.selectedTableInfo);

    // Open the table view modal immediately
    const modalCheckbox = document.getElementById(
      'table-view-modal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = true;
      console.log('Modal opened successfully');
    } else {
      console.error('Modal checkbox element not found');
    }
  }

  // Prompts related methods
  loadPrompts() {
    const skip = 0;
    const limit = 1000;

    // Get organization ID from localStorage
    const organization_id =
      localStorage.getItem('organization_id') || undefined;

    this.promptsService
      .getPrompts(undefined, undefined, skip, limit, organization_id)
      .subscribe({
        next: (res) => {
          const promptList = res ?? [];
          this.promptData = promptList.map((item: any) => ({
            ...item,
            promptName: item.title,
            type: item.prompt_type,
            description: item.description,
            source: item.source_db_type,
            target: item.target_db_type,
            status: 'Active',
          }));
        },
        error: (err) => console.error('Paginated data error:', err),
      });
  }

  // For filtered prompt options
  get filteredOptions(): any[] {
    return this.promptData.filter(
      (prompt) =>
        prompt.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        prompt.description
          .toLowerCase()
          .includes(this.searchTerm.toLowerCase()) ||
        prompt.source_db_type
          .toLowerCase()
          .includes(this.searchTerm.toLowerCase()) ||
        prompt.target_db_type
          .toLowerCase()
          .includes(this.searchTerm.toLowerCase())
    );
  }

  // For filtered DB options
  getFilteredDBList(): any[] {
    return this.DBList.filter((db) =>
      db.name.toLowerCase().includes(this.searchDB.toLowerCase())
    );
  }

  // Toggle selection for prompts
  toggleSelection(item: { title: string; description: string }): void {
    const itemString = item.title + ' ' + item.description;

    if (this.selectedOptions.includes(itemString)) {
      this.selectedOptions = this.selectedOptions.filter(
        (opt) => opt !== itemString
      );
    } else {
      this.selectedOptions.push(itemString);
    }
  }

  // Set selected database card
  setSelectedCard(cardNumber: number, key: string) {
    this.selectedCard = cardNumber;
    this.selectedDbType = key;
  }

  // Navigation methods
  goBack(): void {
    this.router.navigate(['/database']);
  }

  goToDependencyChart(objectType?: string, objectName?: string) {
    const storedData = localStorage.getItem('activeDatabaseInfo');

    if (storedData) {
      const { connection, id } = JSON.parse(storedData);

      // Check if ID is valid (should be 36 characters for a UUID)
      if (connection && id && id.length === 36) {
        // If objectType is provided, include it in the query params
        const queryParams: any = { id };
        if (objectType) {
          queryParams.nodeType = objectType.toLowerCase();
        }

        // If objectName is provided, include it to highlight the specific node
        if (objectName) {
          queryParams.highlightNode = objectName;
        }

        this.router.navigate(['/database', connection, 'dependency chart'], {
          queryParams: queryParams,
        });
      } else {
        console.warn('Invalid or missing database ID. Redirecting...');
        this.router.navigate(['/database', connection], {
          queryParams: { id },
        });
      }
    } else {
      console.warn('No active database found. Redirecting to database list...');
      this.router.navigate(['/database']);
    }
  }

  goToCodeReview(objectName: string, objectType: string) {
    console.log('Going to code review for:', { objectName, objectType });
    const storedData = localStorage.getItem('activeDatabaseInfo');

    console.log('Stored database info:', storedData);
    if (storedData) {
      const { connection, id } = JSON.parse(storedData);
      console.log('Parsed connection info:', { connection, id });

      if (connection && id && id.length === 36) {
        console.log('Navigating to code review page with params:', {
          connection,
          id,
          objectName,
          objectType: objectType.toLowerCase(),
        });

        // Close the modal if it's open
        const modalCheckbox = document.getElementById(
          'table-view-modal'
        ) as HTMLInputElement;
        if (modalCheckbox && modalCheckbox.checked) {
          console.log('Closing modal before navigation');
          modalCheckbox.checked = false;
        }

        this.router
          .navigate(['/database', connection, 'code review'], {
            queryParams: {
              id,
              objectName: objectName,
              objectType: objectType.toLowerCase(),
            },
          })
          .then((success) => {
            console.log('Navigation result:', success ? 'Success' : 'Failed');
          })
          .catch((error) => {
            console.error('Navigation error:', error);
          });
      } else {
        console.warn('Invalid or missing database ID. Redirecting...');
        this.router.navigate(['/database', connection], {
          queryParams: { id },
        });
      }
    } else {
      console.warn('No active database found. Redirecting to database list...');
      this.router.navigate(['/database']);
    }
  }

  // Document generation methods
  generateDoc(procedure_name: string) {
    const storedData = localStorage.getItem('activeDatabaseInfo');

    if (storedData) {
      const { connection, id } = JSON.parse(storedData);

      if (connection && id && id.length === 36) {
        // Set generating flag to true for this specific object
        this.generatingDocs[procedure_name] = true;

        this.dbService.generateDocumentation(id, procedure_name).subscribe(
          (response) => {
            console.log('Generated Documentation:', response);

            // Update the local table data to show document is generated
            const objectIndex = this.tableData.findIndex(
              (obj) => obj.objectName === procedure_name
            );
            if (objectIndex !== -1) {
              this.tableData[objectIndex].doc = 'generated';
            }

            // Reset generating flag
            this.generatingDocs[procedure_name] = false;
          },
          (error) => {
            console.error('Error generating documentation:', error);
            // Reset generating flag on error
            this.generatingDocs[procedure_name] = false;
          }
        );
      } else {
        console.warn('Invalid or missing database ID.');
        this.router.navigate(['/database', connection], {
          queryParams: { id },
        });
      }
    } else {
      console.warn('No active database found.');
      this.router.navigate(['/database']);
    }
  }

  downloadDocumentation(procedure_name: string) {
    const storedData = localStorage.getItem('activeDatabaseInfo');

    if (storedData) {
      const { connection, id } = JSON.parse(storedData);

      if (connection && id && id.length === 36) {
        this.dbService.getDocumentation(id, procedure_name);
      } else {
        console.warn('Invalid or missing database ID.');
        this.router.navigate(['/database', connection], {
          queryParams: { id },
        });
      }
    } else {
      console.warn('No active database found.');
      this.router.navigate(['/database']);
    }
  }

  // Transform modal methods
  openTransformModal(objectName: string, objectType: string): void {
    this.selectedObjectName = objectName;
    this.selectedObjectType = objectType.toLowerCase();
    this.stepIndex = 0;
    this.selectedOptions = [];
    console.log('Opening modal for:', objectName);
  }

  nextStep() {
    if (this.stepIndex === 0) {
      if (!this.transformToApi && !this.transformToRdbms) {
        alert('Please select at least one option (API or DB Code Objects).');
        return;
      }

      if (this.transformToApi && !this.transformToRdbms) {
        this.completedSteps = [0]; // Only Step 0 is completed
        this.stepIndex = 2; // Skip directly to Step 3
        return;
      }
    }

    if (this.stepIndex === 1) {
      if (!this.selectedCard) {
        alert('Please select a target database.');
        return;
      }
    }

    if (this.stepIndex < 2) {
      this.completedSteps.push(this.stepIndex);
      this.completedSteps = [...new Set(this.completedSteps)];
      this.stepIndex++;
    }
  }

  prevStep() {
    if (this.stepIndex === 2 && this.transformToApi && !this.transformToRdbms) {
      this.stepIndex = 0;
    } else if (this.stepIndex > 0) {
      this.stepIndex--;
    }

    // Clean up future completed steps
    this.completedSteps = this.completedSteps.filter((i) => i < this.stepIndex);
  }

  get dynamicSteps() {
    const baseSteps = [{ title: 'Transform Into' }, { title: 'Add Prompts' }];
    const allSteps = [
      { title: 'Transform Into' },
      { title: 'Target Database' },
      { title: 'Add Prompts' },
    ];
    return this.transformToApi && !this.transformToRdbms ? baseSteps : allSteps;
  }

  // Get current theme from localStorage
  getTheme(): string {
    return localStorage.getItem('theme') || 'light';
  }

  // Get stored database ID from localStorage
  getStoredDatabaseId(): string {
    const storedData = localStorage.getItem('activeDatabaseInfo');
    if (storedData) {
      const { id } = JSON.parse(storedData);
      return id;
    }
    return '';
  }

  // Close the table view modal
  closeTableViewModal(): void {
    const modalCheckbox = document.getElementById(
      'table-view-modal'
    ) as HTMLInputElement;
    if (modalCheckbox) {
      modalCheckbox.checked = false;
    }
  }

  /**
   * Load user permissions from the auth service
   */
  loadUserPermissions(): void {
    this.authService.getUserRoleDetails().subscribe({
      next: (roleDetails) => {
        // Set basic user information
        this.isSuperUser = roleDetails.is_superuser || false;

        // Case-insensitive check for Admin role
        const roleName = roleDetails.role?.name || '';
        this.isAdmin = roleName.toLowerCase() === 'admin';

        // Get permissions
        if (roleDetails.role && roleDetails.role.permissions) {
          // Handle permissions in array format (from user-role endpoint)
          if (Array.isArray(roleDetails.role.permissions)) {
            this.userPermissions = roleDetails.role.permissions;
          }
          // Handle permissions in object format (for backward compatibility)
          else {
            this.userPermissions = Object.entries(roleDetails.role.permissions)
              .filter(([_, value]) => value === true)
              .map(([key, _]) => key);
          }
        }

        // Check specific permissions
        this.canGenerateDocument =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('generate_document');

        this.canDownloadDocument =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('download_document');

        this.canTransformCode =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('transform_code');

        this.canViewCode =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('transform_code');

        this.canDeployCode =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('deploy_code');

        // Mark permissions as loaded
        this.permissionsLoaded = true;

        console.log('User permissions loaded:', {
          isSuperUser: this.isSuperUser,
          isAdmin: this.isAdmin,
          permissions: this.userPermissions,
          canGenerateDocument: this.canGenerateDocument,
          canDownloadDocument: this.canDownloadDocument,
          canTransformCode: this.canTransformCode,
          canViewCode: this.canViewCode,
          canDeployCode: this.canDeployCode,
          isConnected: this.isConnected,
          transformButtonVisible: this.canTransformCode && this.isConnected,
          permissionsLoaded: this.permissionsLoaded,
        });
      },
      error: (err) => {
        console.error('Error loading user permissions:', err);
        // Fallback to getUserDetails if getUserRoleDetails fails
        this.fallbackToUserDetails();
      },
    });
  }

  /**
   * Fallback to getUserDetails if getUserRoleDetails fails
   */
  fallbackToUserDetails(): void {
    this.authService.getUserDetails().subscribe({
      next: (user) => {
        // Set basic user information
        this.isSuperUser = user.is_superuser || false;

        // Case-insensitive check for Admin role
        const roleName = user.role?.name || '';
        this.isAdmin = roleName.toLowerCase() === 'admin';

        // Get permissions
        if (user.role && user.role.permissions) {
          // Handle permissions in array format
          if (Array.isArray(user.role.permissions)) {
            this.userPermissions = user.role.permissions;
          }
          // Handle permissions in object format
          else {
            this.userPermissions = Object.entries(user.role.permissions)
              .filter(([_, value]) => value === true)
              .map(([key, _]) => key);
          }
        }

        // Check specific permissions
        this.canGenerateDocument =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('generate_document');

        this.canDownloadDocument =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('download_document');

        this.canTransformCode =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('transform_code');

        this.canViewCode =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('transform_code');

        this.canDeployCode =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('deploy_code');

        // Mark permissions as loaded
        this.permissionsLoaded = true;

        console.log('User permissions loaded (fallback):', {
          isSuperUser: this.isSuperUser,
          isAdmin: this.isAdmin,
          permissions: this.userPermissions,
          canGenerateDocument: this.canGenerateDocument,
          canDownloadDocument: this.canDownloadDocument,
          canTransformCode: this.canTransformCode,
          canViewCode: this.canViewCode,
          canDeployCode: this.canDeployCode,
          isConnected: this.isConnected,
          transformButtonVisible: this.canTransformCode && this.isConnected,
          permissionsLoaded: this.permissionsLoaded,
        });
      },
      error: (err) => {
        console.error('Error loading user details:', err);
        // Even if permissions fail to load, set defaults to allow basic viewing
        this.permissionsLoaded = true;
      },
    });
  }

  /**
   * Check if the user has a specific permission
   * @param permission The permission to check
   * @returns True if the user has the permission, false otherwise
   */
  hasPermission(permission: string): boolean {
    return (
      this.isSuperUser ||
      this.isAdmin ||
      this.userPermissions.includes(permission)
    );
  }

  /**
   * Debug method to check transform button visibility
   * @returns Debug information about transform button state
   */
  getTransformButtonDebugInfo(): any {
    const debugInfo = {
      canTransformCode: this.canTransformCode,
      isConnected: this.isConnected,
      isSuperUser: this.isSuperUser,
      isAdmin: this.isAdmin,
      userPermissions: this.userPermissions,
      hasTransformPermission: this.userPermissions.includes('transform_code'),
      transformButtonVisible: this.canTransformCode && this.isConnected,
      permissionsLoaded: this.permissionsLoaded,
    };
    console.log('Transform Button Debug Info:', debugInfo);
    return debugInfo;
  }

  transformCode() {
    const storedData = localStorage.getItem('activeDatabaseInfo');
    this.success = false;
    this.errorState = false;
    this.errorDetails = '';

    if (storedData) {
      const { connection, id } = JSON.parse(storedData);

      // Create the objects array with name and type
      const selectedObject = this.tableData.find(
        (obj) => obj.objectName === this.selectedObjectName
      );
      const objectType = selectedObject?.objectType;

      const objectsToTransform = [
        {
          name: this.selectedObjectName,
          type: objectType.toLowerCase(),
        },
      ];

      if (connection && id && id.length === 36) {
        // Set both loading states to true
        this.loading = true;
        this.isLoading = true;
        this.longLoadingMessage = false;

        // Set a timeout to show the long loading message after 5 seconds
        this.loadingTimeout = setTimeout(() => {
          this.longLoadingMessage = true;
        }, 5000);

        this.dbService
          .transformCode(
            id,
            objectsToTransform,
            this.transformToApi,
            this.transformToRdbms,
            this.selectedDbType,
            this.selectedOptions
          )
          .subscribe(
            (response) => {
              console.log('Transformation Success:', response);
              // Clear the timeout
              if (this.loadingTimeout) {
                clearTimeout(this.loadingTimeout);
              }

              // Set loading states to false
              this.loading = false;
              this.isLoading = false;
              this.longLoadingMessage = false;

              localStorage.setItem(
                'transformationResponse',
                JSON.stringify(response)
              );

              this.loadDatabaseInfo();
              this.dbService.getDbIdAnalytics().subscribe((res) => {
                console.log('Summary:', res);
                this.data = res;
              });

              // Display success screen or message
              this.success = true;
              this.successMessage = 'Transformation was successful!';
            },
            (error) => {
              console.error('Error in transformation:', error);
              // Clear the timeout
              if (this.loadingTimeout) {
                clearTimeout(this.loadingTimeout);
              }

              // Set loading states to false
              this.loading = false;
              this.isLoading = false;
              this.longLoadingMessage = false;

              // Set error states
              this.success = false;
              this.errorState = true;
              this.errorMessage = 'An error occurred during transformation.';

              // Extract more detailed error information if available
              if (error.error && error.error.detail) {
                this.errorDetails = error.error.detail;
              } else if (error.message) {
                this.errorDetails = error.message;
              }
            }
          );
      } else {
        console.warn('Invalid or missing database ID.');
        this.router.navigate(['/database', connection], {
          queryParams: { id },
        });
      }
    } else {
      console.warn('No active database found.');
      this.router.navigate(['/database']);
    }
  }
}

<div class="grid grid-cols-3 gap-2">
  <app-stats
    title="Connections"
    icon="ti ti-database"
    [badge]="stats?.total_databases"
    iconSize="text-xl"
    iconColor="text-gray-500"
    [onlyHeader]="false"
  >
    <div class="my-0">
      <div class="flex flex-fill justify-between text-content3 gap-2">
        <div
          class="badge badge-flat-success flex justify-between flex-1 rounded-md py-2 px-4"
          [attr.data-theme]="getTheme()"
        >
          <p class="text-base font-semibold text-success">Connected</p>
          <p class="text-base font-semibold text-success">
            {{ stats?.total_databases - disconnectedCount }}
          </p>
        </div>
        <div
          class="badge badge-flat-error flex justify-between flex-1 rounded-md py-2 px-4"
          [attr.data-theme]="getTheme()"
        >
          <p class="text-base font-semibold text-error">Disconnected</p>
          <p class="text-base font-semibold text-error">
            {{ disconnectedCount }}
          </p>
        </div>
      </div>
    </div>
  </app-stats>

  <app-stats
    title="Analyzed Database"
    icon="ti ti-hourglass-empty"
    [badge]="stats?.analyzed_databases"
    iconSize="text-xl"
    iconColor="text-[#FB6340]"
    [onlyHeader]="false"
  >
    <div class="my-0">
      <div
        class="flex flex-fill flex-col justify-between text-content3mt-1 gap-2"
      >
        <div
          class="relative w-full h-2 rounded"
          [ngClass]="{
            'bg-gray-200': getTheme() === 'light',
            'bg-backgroundSecondary': getTheme() === 'dark'
          }"
        >
          <div
            class="absolute top-0 left-0 h-2 rounded bg-gradient-to-r from-orange-400 to-green-500"
            [ngStyle]="{
              width:
                calculatePercentage(
                  stats?.analyzed_databases,
                  stats?.total_databases
                ) + '%'
            }"
          ></div>
        </div>
        <div
          class="flex justify-between text-sm"
          [ngClass]="{
            'text-gray-700': getTheme() === 'light',
            'text-content1 opacity-90': getTheme() === 'dark'
          }"
        >
          <p class="text-base">
            {{
              calculatePercentage(
                stats?.analyzed_databases,
                stats?.total_databases
              )
            }}%
          </p>
          <p class="text-base">
            {{ stats?.analyzed_databases }}/{{ stats?.total_databases }}
          </p>
        </div>
      </div>
    </div>
  </app-stats>

  <app-stats
    title="DB Objects Transformed"
    icon="ti ti-discount-check"
    [badge]="stats?.summary.transformed_objects"
    iconSize="text-xl"
    iconColor="text-[#2CCE8A]"
    [onlyHeader]="false"
  >
    <div class="my-0">
      <div
        class="flex flex-fill flex-col justify-between text-content3mt-1 gap-2"
      >
        <div
          class="relative w-full h-2 rounded"
          [ngClass]="{
            'bg-gray-200': getTheme() === 'light',
            'bg-backgroundSecondary': getTheme() === 'dark'
          }"
        >
          <div
            class="absolute top-0 left-0 h-2 rounded bg-gradient-to-r from-orange-400 to-green-500"
            [ngStyle]="{
              width:
                calculatePercentage(
                  stats?.summary?.transformed_objects,
                  stats?.summary?.total_objects
                ) + '%'
            }"
          ></div>
        </div>
        <div
          class="flex justify-between text-sm"
          [ngClass]="{
            'text-gray-700': getTheme() === 'light',
            'text-content1 opacity-90': getTheme() === 'dark'
          }"
        >
          <p class="text-base">
            {{
              calculatePercentage(
                stats?.summary.transformed_objects,
                stats?.summary.total_objects
              )
            }}%
          </p>
          <p class="text-base">
            {{ stats?.summary.transformed_objects }}/
            {{ stats?.summary.total_objects }}
          </p>
        </div>
      </div>
    </div>
  </app-stats>
</div>

<!-- Table implementation directly in the database component -->
<div class="py-4">
  <!-- Search and Filter Bar above the table -->
  <div class="flex flex-col justify-between md:flex-row gap-3 py-3 mb-4">
    <!-- DB Type Filter -->
    <div class="flex flex-row gap-3">
      <div class="form-control min-w-[200px]">
        <select
          class="select select-sm"
          [(ngModel)]="dbTypeFilter"
          (change)="currentPage = 1"
        >
          <option value="all">All DB Types</option>
          <option *ngFor="let type of uniqueDbTypes" [value]="type">
            {{ type }}
          </option>
        </select>
      </div>

      <!-- Connection Status Filter -->
      <div class="form-control min-w-[200px]">
        <select
          class="select select-sm"
          [(ngModel)]="dbStatusFilter"
          (change)="currentPage = 1"
        >
          <option value="all">All Statuses</option>
          <option value="connected">Connected</option>
          <option value="disconnected">Disconnected</option>
        </select>
      </div>

      <!-- Reset Filters Button -->
      <button
        class="btn btn-sm btn-outline-secondary"
        *ngIf="
          searchTerm != '' ||
          dbTypeFilter != 'all' ||
          dbStatusFilter != 'all' ||
          currentPage > 1
        "
        (click)="resetFilters()"
      >
        Reset Filters
      </button>
    </div>
    <!-- Search Bar -->
    <div class="flex flex-row gap-3">
      <div class="form-control relative w-[270px]">
        <input
          type="text"
          class="input input-sm max-w-full pl-8"
          placeholder="Search by name, host, or type..."
          [(ngModel)]="searchTerm"
          (input)="currentPage = 1"
        />
        <span class="absolute inset-y-0 left-3 inline-flex items-center">
          <i class="ti ti-search"></i>
        </span>
      </div>
      <label
        *ngIf="canCreateConnection"
        class="btn btn-primary btn-sm rounded-md"
        for="connect-DB"
        ><i class="ti ti-plus text-base pr-2"></i>Connect DB
      </label>
      <button
        *ngIf="!canCreateConnection && permissionsLoaded"
        class="btn btn-primary btn-sm rounded-md cursor-not-allowed opacity-60"
        disabled
        title="You don't have permission to create connections"
      >
        <i class="ti ti-plus text-base pr-2"></i>Connect DB
      </button>
    </div>
  </div>
  <!-- Table implementation (was previously app-table) -->
  <div *ngIf="loading" class="flex justify-center my-8">
    <div
      class="absolute animate-spin rounded-full h-8 w-8 border-b-2 border-primary top-[50%]"
    ></div>
  </div>
  <div
    class="enhanced-table rounded-lg border shadow-sm"
    style="overflow: visible"
    *ngIf="!loading"
  >
    <div class="flex flex-col">
      <!-- Table -->
      <div
        class="relative overflow-x-auto"
        style="max-width: 100%; position: relative; overflow: visible"
      >
        <table
          class="table table-compact table-hover w-full border-collapse custom-table border border-[var(--border-color)]"
          style="position: relative"
        >
          <thead>
            <tr>
              <!-- Dynamic Columns -->
              <th
                *ngFor="let col of columns; let i = index; let isLast = last"
                class="text-left font-medium text-content1 border-b border-[var(--border-color)] whitespace-nowrap"
                [class.border-r]="!isLast"
                [class.sticky-left]="i === 0"
                [class.sticky-right]="isLast && columns.length > 2"
                (click)="sort(col.key)"
              >
                <div class="flex justify-between border-none">
                  <span class="border-none max-w-[150px]">{{ col.label }}</span>
                  <span *ngIf="sortColumn === col.key" class="border-none ml-1">
                    {{ sortAsc ? "↑" : "↓" }}
                  </span>
                </div>
              </th>
            </tr>
          </thead>

          <!-- No Data State -->
          <tbody *ngIf="paginatedData.length === 0">
            <tr>
              <td [attr.colspan]="columns.length" class="text-center py-8">
                <div class="flex flex-col items-center justify-center gap-3">
                  <i
                    class="ti ti-database-off text-4xl text-content3 opacity-70"
                  ></i>
                  <p class="text-content2 font-medium">
                    No database connections found
                  </p>
                  <p class="text-content3 text-sm">
                    Try adjusting your filters or connect a new database
                  </p>
                  <label
                    *ngIf="canCreateConnection"
                    class="btn btn-primary btn-sm rounded-md mt-2"
                    for="connect-DB"
                  >
                    <i class="ti ti-plus text-base pr-2"></i>Connect DB
                  </label>
                  <button
                    *ngIf="!canCreateConnection && permissionsLoaded"
                    class="btn btn-primary btn-sm rounded-md mt-2 cursor-not-allowed opacity-60"
                    disabled
                    title="You don't have permission to create connections"
                  >
                    <i class="ti ti-plus text-base pr-2"></i>Connect DB
                  </button>
                </div>
              </td>
            </tr>
          </tbody>

          <!-- Data Rows -->
          <tbody *ngIf="paginatedData.length > 0">
            <tr
              *ngFor="let item of paginatedData; let i = index"
              class="cursor-pointer border-b transition-all duration-200"
              [ngStyle]="{ 'animation-delay': i * 0.05 + 's' }"
              style="animation: fadeInRow 0.5s ease-out forwards"
              [class.selected-row]="item.selected"
            >
              <!-- Dynamic Columns -->
              <td
                *ngFor="let col of columns; let i = index; let isLast = last"
                class="whitespace-nowrap"
                [class.text-primary]="i === 1"
                [class.sticky-left]="i === 0"
                [class.sticky-right]="isLast && columns.length > 2"
                [class.border-r]="i === 0"
                [class.border-l]="isLast && columns.length > 2"
              >
                <ng-container *ngIf="!col.custom">
                  <!-- First Data Column (clickable row link) -->
                  <span
                    *ngIf="i === 0"
                    class="flex items-center gap-2 font-medium text-primary hover:underline truncate max-w-[200px]"
                    [class.cursor-pointer]="enableNavigation"
                    [title]="item[col.key]"
                    (click)="
                      goToDatabaseDetails(
                        item.DB_MainConnection,
                        item.database_id,
                        item.dbType
                      )
                    "
                  >
                    <span
                      *ngIf="item.connectionStatus"
                      class="dot flex-shrink-0"
                      [ngClass]="
                        item.connectionStatus === 'Connected'
                          ? 'dot-success'
                          : 'dot-error'
                      "
                    ></span>
                    <span class="truncate">{{ item[col.key] }}</span>
                  </span>

                  <!-- Other Regular Columns -->
                  <span *ngIf="i !== 0" class="block" [title]="item[col.key]">{{
                    item[col.key]
                  }}</span>
                </ng-container>

                <!-- Custom Template for Analysis Status -->
                <ng-container *ngIf="col.key === 'analysisStatus'">
                  <span
                    class="badge"
                    [ngClass]="{
                      'badge-flat-error':
                        item.analysisStatus === 'Awaiting Analysis',
                      'badge-flat-warning':
                        item.analysisStatus === 'Analyzing...',
                      'badge-flat-success':
                        item.analysisStatus === 'Analysis Complete'
                    }"
                  >
                    {{ item.analysisStatus }}
                  </span>
                </ng-container>

                <!-- Custom Template for Connection Status -->
                <ng-container *ngIf="col.key === 'connectionStatus'">
                  <div class="connection-status-switch">
                    <!-- Interactive switch for admin and superuser only -->
                    <input
                      *ngIf="isAdmin || isSuperUser"
                      type="checkbox"
                      class="switch switch-success"
                      [checked]="item.connectionStatus === 'Connected'"
                      (change)="
                        toggleConnectionStatus(
                          item.database_id,
                          item.connectionStatus
                        )
                      "
                    />
                    <!-- Read-only switch for non-admin users -->
                    <input
                      *ngIf="!isAdmin && !isSuperUser"
                      type="checkbox"
                      class="switch switch-success cursor-not-allowed"
                      [checked]="item.connectionStatus === 'Connected'"
                      disabled
                      title="Only administrators can change connection status"
                    />
                    <span class="ml-2 text-xs text-content2">
                      {{
                        item.connectionStatus === "Connected"
                          ? "Connected"
                          : "Disconnected"
                      }}
                    </span>
                  </div>
                </ng-container>

                <!-- Custom Template for Actions -->
                <ng-container *ngIf="col.key === 'action'">
                  <div class="flex items-center gap-2">
                    <!-- Analyze Button (only if connected) -->
                    <button
                      *ngIf="
                        canAnalyzeConnection &&
                        item.connectionStatus === 'Connected'
                      "
                      class="btn btn-xs btn-out min-w-[76px]"
                      [ngClass]="{
                        'btn-outline-primary':
                          item.analysisStatus === 'Awaiting Analysis',
                        'btn-primary': item.analysisStatus === 'Analyzing...',
                        'btn-outline-default':
                          item.analysisStatus === 'Analysis Complete',
                        'opacity-60': item['_analyzeDisabled']
                      }"
                      (click)="analyzeDatabase(tableData.indexOf(item))"
                      [disabled]="
                        item.analysisStatus === 'Analyzing...' ||
                        item['_analyzeDisabled']
                      "
                      [title]="
                        item['_analyzeDisabled']
                          ? 'Analysis in progress for another database'
                          : ''
                      "
                    >
                      {{
                        item.analysisStatus === "Analysis Complete"
                          ? "Reanalyze"
                          : "Analyze"
                      }}
                    </button>

                    <!-- Disabled Analyze Button (disconnected) -->
                    <button
                      *ngIf="
                        canAnalyzeConnection &&
                        item.connectionStatus !== 'Connected'
                      "
                      class="btn btn-xs btn-out min-w-[76px] cursor-not-allowed opacity-60"
                      [ngClass]="{
                        'btn-outline-primary':
                          item.analysisStatus === 'Awaiting Analysis',
                        'btn-primary': item.analysisStatus === 'Analyzing...',
                        'btn-outline-default':
                          item.analysisStatus === 'Analysis Complete'
                      }"
                      disabled
                      title="Database must be connected to analyze"
                    >
                      {{
                        item.analysisStatus === "Analysis Complete"
                          ? "Reanalyze"
                          : "Analyze"
                      }}
                    </button>

                    <!-- Disabled Analyze Button (no permission) -->
                    <button
                      *ngIf="!canAnalyzeConnection && permissionsLoaded"
                      class="btn btn-xs btn-out min-w-[76px] cursor-not-allowed opacity-60"
                      [ngClass]="{
                        'btn-outline-primary':
                          item.analysisStatus === 'Awaiting Analysis',
                        'btn-primary': item.analysisStatus === 'Analyzing...',
                        'btn-outline-default':
                          item.analysisStatus === 'Analysis Complete'
                      }"
                      disabled
                      title="You don't have permission to analyze connections"
                    >
                      {{
                        item.analysisStatus === "Analysis Complete"
                          ? "Reanalyze"
                          : "Analyze"
                      }}
                    </button>

                    <!-- Action Menu Button -->
                    <!-- <div class="relative">
                      <button
                        class="btn btn-xs btn-ghost"
                        (click)="toggleActionMenu(item.database_id, $event)"
                        [attr.data-id]="item.database_id"
                      >
                        <i class="ti ti-dots-vertical"></i>
                      </button>
                    </div> -->
                  </div>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination - Only show when there's data -->
      <div
        *ngIf="paginatedData.length > 0"
        class="flex justify-between items-center bg-backgroundPrimary p-4 rounded-lg"
      >
        <!-- Left: Pagination Info & Page Size Selection -->
        <div class="flex flex-row flex-1 items-center">
          <h2 class="text-base text-gray-500 text-nowrap">
            Showing {{ currentPageStart }} to {{ currentPageEnd }} of
            {{ totalEntries }} DB's
          </h2>

          <select
            class="select select-sm rounded-md text-gray-500 mx-3 text-nowrap max-w-16"
            [(ngModel)]="pageSize"
            (change)="changePageSize($event)"
          >
            <option *ngFor="let size of pageSizeOptions" [value]="size">
              {{ size }}
            </option>
          </select>

          <p class="text-base text-gray-500 text-nowrap">per page</p>
        </div>

        <!-- Right: Pagination Controls -->
        <div class="pagination text-gray-500 flex gap-2">
          <button
            class="btn btn-xs btn-ghost"
            (click)="prevPage()"
            [disabled]="currentPage === 1"
          >
            <svg
              width="18"
              height="18"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M12.2574 5.59165C11.9324 5.26665 11.4074 5.26665 11.0824 5.59165L7.25742 9.41665C6.93242 9.74165 6.93242 10.2667 7.25742 10.5917L11.0824 14.4167C11.4074 14.7417 11.9324 14.7417 12.2574 14.4167C12.5824 14.0917 12.5824 13.5667 12.2574 13.2417L9.02409 9.99998L12.2574 6.76665C12.5824 6.44165 12.5741 5.90832 12.2574 5.59165Z"
                fill="#969696"
              />
            </svg>
          </button>

          <!-- Dynamic Pagination Buttons -->
          <button
            *ngFor="let page of pageNumbers"
            class="btn btn-xs"
            [ngClass]="{
              'btn-primary': currentPage === page,
              'btn-ghost ': currentPage !== page
            }"
            (click)="goToPage(page)"
          >
            {{ page }}
          </button>

          <button
            class="btn btn-xs btn-ghost"
            (click)="nextPage()"
            [disabled]="currentPage === totalPages"
          >
            <svg
              width="18"
              height="18"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M7.74375 5.2448C7.41875 5.5698 7.41875 6.0948 7.74375 6.4198L10.9771 9.65314L7.74375 12.8865C7.41875 13.2115 7.41875 13.7365 7.74375 14.0615C8.06875 14.3865 8.59375 14.3865 8.91875 14.0615L12.7437 10.2365C13.0687 9.91147 13.0687 9.38647 12.7437 9.06147L8.91875 5.23647C8.60208 4.9198 8.06875 4.9198 7.74375 5.2448Z"
                fill="#969696"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Action Menu Portal - Rendered outside of table container -->
<div
  *ngIf="activeActionMenu"
  class="fixed z-[9999] action-menu-portal"
  [style.top.px]="actionMenuPosition.top"
  [style.left.px]="actionMenuPosition.left"
  (click)="$event.stopPropagation()"
>
  <div
    class="bg-backgroundPrimary shadow-lg border border-[var(--border-color)] rounded-md p-2 w-[150px]"
  >
    <!-- View Option - Always available -->
    <a
      class="flex items-center gap-2 text-content2 hover:text-primary p-2 cursor-pointer rounded-md hover:bg-backgroundSecondary"
      (click)="viewConnection(activeActionMenuItem)"
    >
      <i class="ti ti-eye"></i>
      <span>View Details</span>
    </a>

    <!-- Edit Option - Only if user has permission -->
    <a
      *ngIf="canEditConnection"
      class="flex items-center gap-2 text-content2 hover:text-primary p-2 cursor-pointer rounded-md hover:bg-backgroundSecondary"
      (click)="editConnection(activeActionMenuItem)"
    >
      <i class="ti ti-edit"></i>
      <span>Edit</span>
    </a>

    <!-- Disabled Edit Option -->
    <span
      *ngIf="!canEditConnection && permissionsLoaded"
      class="flex items-center gap-2 text-content3 opacity-60 cursor-not-allowed p-2 rounded-md"
      title="You don't have permission to edit connections"
    >
      <i class="ti ti-edit"></i>
      <span>Edit</span>
    </span>

    <!-- Delete Option - Only if user has permission -->
    <a
      *ngIf="canDeleteConnection"
      class="flex items-center gap-2 text-content2 hover:text-error p-2 cursor-pointer rounded-md hover:bg-backgroundSecondary"
      (click)="deleteConnection(activeActionMenuItem)"
    >
      <i class="ti ti-trash"></i>
      <span>Delete</span>
    </a>

    <!-- Disabled Delete Option -->
    <span
      *ngIf="!canDeleteConnection && permissionsLoaded"
      class="flex items-center gap-2 text-content3 opacity-60 cursor-not-allowed p-2 rounded-md"
      title="You don't have permission to delete connections"
    >
      <i class="ti ti-trash"></i>
      <span>Delete</span>
    </span>
  </div>
</div>

<!-- Database Modal -->
<input class="modal-state" id="database-modal" type="checkbox" />
<div class="modal w-screen">
  <label class="modal-overlay" for="database-modal"></label>
  <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
    <!-- Loading overlay -->
    <div
      *ngIf="loading"
      class="absolute inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 rounded-lg"
    >
      <div class="flex flex-col items-center">
        <div
          class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"
        ></div>
        <p class="text-white mt-4">
          {{
            modalMode === "edit"
              ? "Updating"
              : modalMode === "delete"
              ? "Deleting"
              : "Loading"
          }}
          database...
        </p>
      </div>
    </div>

    <div class="modal-header">
      <h2 class="text-xl">
        {{
          modalMode === "view"
            ? "Database Details"
            : modalMode === "edit"
            ? "Edit Database Connection"
            : "Delete Database Connection"
        }}
      </h2>
      <label for="database-modal" class="modal-close-btn">
        <i class="ti ti-x"></i>
      </label>
    </div>

    <div class="p-4 overflow-auto">
      <!-- View Mode -->
      <div *ngIf="modalMode === 'view' && selectedDatabase">
        <!-- Error Message -->
        <div
          *ngIf="selectedDatabase.error"
          class="bg-error bg-opacity-10 p-4 rounded-lg border border-error mb-4"
        >
          <div class="flex items-start">
            <i class="ti ti-alert-triangle text-error text-2xl mr-3 mt-1"></i>
            <div>
              <h3 class="text-lg font-semibold text-error mb-2">Error</h3>
              <p class="text-content1">
                {{
                  selectedDatabase.errorMessage ||
                    "An error occurred while fetching database details."
                }}
              </p>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div class="form-field">
            <label class="form-label">Connection Name</label>
            <div class="input input-lg bg-backgroundSecondary">
              {{ selectedDatabase.DB_MainConnection }}
            </div>
          </div>
          <div class="form-field">
            <label class="form-label">Database Name</label>
            <div class="input input-lg bg-backgroundSecondary">
              {{ selectedDatabase.connection_name }}
            </div>
          </div>
          <div class="form-field">
            <label class="form-label">Host</label>
            <div class="input input-lg bg-backgroundSecondary">
              {{ selectedDatabase.database_host_name }}
            </div>
          </div>
          <div class="form-field">
            <label class="form-label">Port</label>
            <div class="input input-lg bg-backgroundSecondary">
              {{ selectedDatabase.database_port }}
            </div>
          </div>
          <div class="form-field">
            <label class="form-label">Username</label>
            <div class="input input-lg bg-backgroundSecondary">
              {{ selectedDatabase.database_username }}
            </div>
          </div>
          <div class="form-field">
            <label class="form-label">Database Type</label>
            <div class="input input-lg bg-backgroundSecondary">
              {{ selectedDatabase.database_type }}
            </div>
          </div>
          <div class="form-field col-span-2">
            <label class="form-label">Connection Status</label>
            <div
              class="input input-lg bg-backgroundSecondary flex items-center"
            >
              <span
                class="dot flex-shrink-0 mr-2"
                [ngClass]="
                  selectedDatabase.connection_status === 'Connected'
                    ? 'dot-success'
                    : 'dot-error'
                "
              ></span>
              {{ selectedDatabase.connection_status }}
            </div>
          </div>
        </div>

        <div class="flex justify-between mt-4">
          <button
            *ngIf="canEditConnection"
            class="btn btn-primary"
            (click)="modalMode = 'edit'; prepareEditForm(selectedDatabase)"
          >
            Edit Connection
          </button>
          <label for="database-modal" class="btn btn-outline-default">
            Close
          </label>
        </div>
      </div>

      <!-- Edit Mode -->
      <div *ngIf="modalMode === 'edit' && selectedDatabase">
        <div class="grid grid-cols-2 gap-4">
          <div class="form-field">
            <label class="form-label">Connection Name*</label>
            <input
              type="text"
              class="input input-lg max-w-full"
              [(ngModel)]="editConnectionDetails.DB_MainConnection"
              placeholder="Enter Connection Name"
            />
          </div>
          <div class="form-field">
            <label class="form-label">Database Name*</label>
            <input
              type="text"
              class="input input-lg max-w-full"
              [(ngModel)]="editConnectionDetails.connection_name"
              placeholder="Enter Database Name"
            />
          </div>
          <div class="form-field">
            <label class="form-label">Host*</label>
            <input
              type="text"
              class="input input-lg max-w-full"
              [(ngModel)]="editConnectionDetails.database_host_name"
              placeholder="Enter Host"
            />
          </div>
          <div class="form-field">
            <label class="form-label">Port*</label>
            <input
              type="text"
              class="input input-lg max-w-full"
              [(ngModel)]="editConnectionDetails.database_port"
              placeholder="Enter Port"
            />
          </div>
          <div class="form-field">
            <label class="form-label">Username*</label>
            <input
              type="text"
              class="input input-lg max-w-full"
              [(ngModel)]="editConnectionDetails.database_username"
              placeholder="Enter Username"
            />
          </div>
          <div class="form-field">
            <label class="form-label">Password</label>
            <input
              type="password"
              class="input input-lg max-w-full"
              [(ngModel)]="editConnectionDetails.database_password"
              placeholder="Leave blank to keep current password"
            />
          </div>
          <div class="form-field col-span-2">
            <label class="form-label">Database Type*</label>
            <select
              class="select select-lg w-full"
              [(ngModel)]="editConnectionDetails.database_type"
            >
              <option *ngFor="let db of DBList" [value]="db.key">
                {{ db.name }}
              </option>
            </select>
          </div>
        </div>

        <div class="flex justify-between mt-4">
          <button class="btn btn-primary" (click)="saveEditedConnection()">
            Save Changes
          </button>
          <label for="database-modal" class="btn btn-outline-default">
            Cancel
          </label>
        </div>
      </div>

      <!-- Delete Mode -->
      <div *ngIf="modalMode === 'delete' && selectedDatabase">
        <div
          class="bg-error bg-opacity-10 p-4 rounded-lg border border-error mb-4"
        >
          <div class="flex items-start">
            <i class="ti ti-alert-triangle text-error text-2xl mr-3 mt-1"></i>
            <div>
              <h3 class="text-lg font-semibold text-error mb-2">
                Warning: This action cannot be undone
              </h3>
              <p class="text-content1">
                Are you sure you want to delete the connection
                <strong>{{ selectedDatabase.DB_MainConnection }}</strong
                >? This will permanently remove all associated data, including
                analytics, documents, and transformations.
              </p>
            </div>
          </div>
        </div>

        <div class="flex justify-between mt-4">
          <button class="btn btn-error" (click)="confirmDeleteConnection()">
            Delete Connection
          </button>
          <label for="database-modal" class="btn btn-outline-default">
            Cancel
          </label>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Analysis Progress Modal -->
<input class="modal-state" id="analysis-progress-modal" type="checkbox" />
<div class="modal w-screen">
  <label class="modal-overlay" for="analysis-progress-modal"></label>
  <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
    <div class="modal-header">
      <h2 class="text-xl">
        {{
          analysisModalMode === "loading"
            ? "Analyzing Database"
            : analysisModalMode === "success"
            ? "Analysis Complete"
            : analysisModalMode === "error"
            ? "Analysis Failed"
            : "Database Analysis"
        }}
      </h2>
      <label
        for="analysis-progress-modal"
        class="modal-close-btn"
        *ngIf="analysisModalMode !== 'loading' || runInBackground"
      >
        <i class="ti ti-x"></i>
      </label>
    </div>

    <!-- Loading State (inside modal body) -->
    <div
      class="p-3 py-2 max-h-[80%] overflow-auto"
      *ngIf="analysisModalMode === 'loading'"
    >
      <div
        class="flex flex-col justify-center items-center p-8 gap-4 animate-fade-in"
      >
        <!-- Colorful loader with gradient ring -->
        <div class="relative">
          <!-- Outer gradient ring with animation -->
          <div
            class="w-20 h-20 rounded-full bg-gradient-to-r from-primary via-secondary to-primary p-1 animate-spin-slow"
          >
            <!-- Inner background -->
            <div
              class="w-full h-full rounded-full bg-backgroundPrimary flex items-center justify-center"
            >
              <!-- Spinner -->
              <div
                class="loading loading-spinner loading-md text-primary"
              ></div>
            </div>
          </div>

          <!-- Decorative elements -->
          <div class="absolute -top-2 -right-2 animate-bounce-slow">
            <div
              class="w-6 h-6 rounded-full bg-secondary flex items-center justify-center text-content1"
            >
              <i class="ti ti-sparkles text-xs"></i>
            </div>
          </div>

          <div class="absolute -bottom-1 -left-1 animate-pulse">
            <div
              class="w-5 h-5 rounded-full bg-primary flex items-center justify-center text-content1"
            >
              <i class="ti ti-brain text-xs"></i>
            </div>
          </div>

          <div class="absolute top-1 -left-2 animate-ping-slow">
            <div
              class="w-4 h-4 rounded-full bg-info flex items-center justify-center text-content1"
            >
              <i class="ti ti-bulb text-xs"></i>
            </div>
          </div>
        </div>

        <!-- Colorful progress indicator -->
        <div
          class="w-48 bg-backgroundSecondary rounded-full h-2.5 mt-2 overflow-hidden"
        >
          <div class="h-full w-full relative">
            <!-- Gradient progress bar -->
            <div
              class="absolute inset-0 bg-gradient-to-r from-primary via-secondary to-info animate-[progress_2s_ease-in-out_infinite]"
            ></div>
            <!-- Shimmer effect -->
            <div
              class="absolute inset-0 bg-gradient-to-r from-transparent via-content1 to-transparent opacity-20 animate-[shimmer_1.5s_ease-in-out_infinite]"
            ></div>
          </div>
        </div>

        <!-- Enhanced status text with gradient -->
        <div class="text-center">
          <div
            class="inline-block bg-gradient-to-r from-primary to-secondary text-transparent bg-clip-text"
          >
            <h3 class="text-xl font-bold">Analyzing Database</h3>
          </div>
          <div class="flex items-center justify-center gap-1 mt-1">
            <p class="text-content2 text-sm">
              Processing {{ selectedDatabaseName }}
            </p>
            <div class="flex">
              <span class="text-primary animate-bounce-slow delay-100">.</span>
              <span class="text-secondary animate-bounce-slow delay-200"
                >.</span
              >
              <span class="text-info animate-bounce-slow delay-300">.</span>
            </div>
          </div>
        </div>

        <!-- Current operation status -->
        <div class="mt-4 text-center">
          <p class="text-content1 text-sm">
            <span class="font-medium">Current operation:</span>
            {{ currentAnalysisOperation }}
          </p>
          <p class="text-content2 text-xs mt-1">
            {{ analysisTimeElapsed }} elapsed
          </p>
        </div>

        <!-- Show additional message if loading takes too long -->
        <div
          *ngIf="longLoadingMessage"
          class="mt-2 text-center max-w-md p-3 bg-backgroundSecondary rounded-lg border border-borderColor animate-fade-in"
        >
          <div class="flex items-center gap-2 mb-2">
            <i class="ti ti-info-circle text-primary"></i>
            <p class="text-content1 text-sm font-medium">
              Analysis in Progress
            </p>
          </div>
          <p class="text-content2 text-sm">
            Complex database analysis requires deeper processing to generate
            accurate results.
          </p>
          <p class="text-content2 text-xs mt-2 opacity-75">
            This may take a few moments to complete.
          </p>
        </div>

        <!-- Run in background option -->
        <div class="mt-4">
          <button
            class="btn btn-sm btn-outline-default"
            (click)="runAnalysisInBackground()"
          >
            <i class="ti ti-minimize mr-1"></i> Continue in Background
          </button>
        </div>
      </div>
    </div>

    <!-- Success State -->
    <div
      class="p-3 py-2 max-h-[80%] overflow-auto"
      *ngIf="analysisModalMode === 'success'"
    >
      <div
        class="flex flex-col justify-center items-center p-8 gap-4 animate-fade-in"
      >
        <!-- Success icon -->
        <div class="relative">
          <div
            class="w-20 h-20 rounded-full bg-success/10 flex items-center justify-center"
          >
            <i class="ti ti-check text-4xl text-success"></i>
          </div>
          <!-- Decorative elements -->
          <div class="absolute -top-2 -right-2 animate-bounce-slow">
            <div
              class="w-6 h-6 rounded-full bg-success flex items-center justify-center text-white"
            >
              <i class="ti ti-sparkles text-xs"></i>
            </div>
          </div>
        </div>

        <!-- Success message -->
        <div class="text-center mt-2">
          <h3 class="text-xl font-bold text-success">Analysis Complete</h3>
          <p class="text-content2 text-sm mt-2">
            Successfully analyzed {{ selectedDatabaseName }}
          </p>
          <div class="mt-3 p-3 bg-backgroundSecondary rounded-lg">
            <div class="flex justify-between items-center">
              <span class="text-content1 text-sm">Objects Found:</span>
              <span class="text-content1 font-medium">{{
                analysisObjectCount
              }}</span>
            </div>
            <div class="flex justify-between items-center mt-2">
              <span class="text-content1 text-sm">Time Taken:</span>
              <span class="text-content1 font-medium">{{
                analysisTimeElapsed
              }}</span>
            </div>
            <div class="flex justify-between items-center mt-2">
              <span class="text-content1 text-sm">Dependency Chart:</span>
              <span
                class="text-content1 font-medium"
                *ngIf="currentAnalysisIndex >= 0"
              >
                {{ tableData[currentAnalysisIndex].dependencyStatus }}
              </span>
            </div>
          </div>
        </div>

        <!-- Action buttons -->
        <div class="flex gap-3 mt-4">
          <button
            class="btn btn-sm btn-primary"
            (click)="viewDatabaseDetails()"
          >
            <i class="ti ti-eye mr-1"></i> View Details
          </button>
          <label
            for="analysis-progress-modal"
            class="btn btn-sm btn-outline-default"
          >
            Close
          </label>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div
      class="p-3 py-2 max-h-[80%] overflow-auto"
      *ngIf="analysisModalMode === 'error'"
    >
      <div
        class="flex flex-col justify-center items-center p-8 gap-4 animate-fade-in"
      >
        <!-- Error icon -->
        <div class="relative">
          <div
            class="w-20 h-20 rounded-full bg-error/10 flex items-center justify-center"
          >
            <i class="ti ti-alert-triangle text-4xl text-error"></i>
          </div>
        </div>

        <!-- Error message -->
        <div class="text-center mt-2">
          <h3 class="text-xl font-bold text-error">Analysis Failed</h3>
          <p class="text-content2 text-sm mt-2">
            There was an error analyzing {{ selectedDatabaseName }}
          </p>
          <div
            class="mt-3 p-3 bg-backgroundSecondary rounded-lg border border-error/20"
          >
            <p class="text-content1 text-sm">{{ analysisErrorMessage }}</p>
          </div>
        </div>

        <!-- Action buttons -->
        <div class="flex gap-3 mt-4">
          <button class="btn btn-sm btn-primary" (click)="retryAnalysis()">
            <i class="ti ti-refresh mr-1"></i> Retry Analysis
          </button>
          <label
            for="analysis-progress-modal"
            class="btn btn-sm btn-outline-default"
          >
            Close
          </label>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Table View Modal -->
<input class="modal-state" id="table-view-modal" type="checkbox" />
<div class="modal w-screen">
  <label class="modal-overlay" for="table-view-modal"></label>
  <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
    <!-- Loading overlay -->
    <div
      *ngIf="loading"
      class="absolute inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 rounded-lg"
    >
      <div class="flex flex-col items-center">
        <div
          class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"
        ></div>
        <p class="text-white mt-4">Loading table information...</p>
      </div>
    </div>

    <div class="modal-header">
      <h2 class="text-xl">Table Information</h2>
      <label for="table-view-modal" class="modal-close-btn">
        <i class="ti ti-x"></i>
      </label>
    </div>

    <div class="p-4 overflow-auto max-h-[70vh]">
      <div *ngIf="selectedTableInfo">
        <!-- Object Details Section -->
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="form-field">
            <label class="form-label">Object Name</label>
            <div class="input input-lg bg-backgroundSecondary">
              {{ selectedTableInfo.objectName }}
            </div>
          </div>
          <div class="form-field">
            <label class="form-label">Object Type</label>
            <div class="input input-lg bg-backgroundSecondary">
              {{ selectedTableInfo.objectType }}
            </div>
          </div>
          <div class="form-field">
            <label class="form-label">Transformation Status</label>
            <div
              class="input input-lg bg-backgroundSecondary flex items-center"
            >
              <span
                class="badge mr-2"
                [ngClass]="{
                  'badge-flat-success':
                    selectedTableInfo.transformed_into !== 'not transformed',
                  'badge-flat-error':
                    selectedTableInfo.transformed_into === 'not transformed'
                }"
              >
                {{
                  selectedTableInfo.transformed_into === "not transformed"
                    ? "Not Transformed"
                    : "Transformed"
                }}
              </span>
            </div>
          </div>
          <div class="form-field">
            <label class="form-label">Documentation Status</label>
            <div
              class="input input-lg bg-backgroundSecondary flex items-center"
            >
              <span
                class="badge mr-2"
                [ngClass]="{
                  'badge-flat-success': selectedTableInfo.doc === 'generated',
                  'badge-flat-warning': selectedTableInfo.doc !== 'generated'
                }"
              >
                {{
                  selectedTableInfo.doc === "generated"
                    ? "Generated"
                    : "Not Generated"
                }}
              </span>
            </div>
          </div>
          <div class="form-field" *ngIf="selectedTableInfo.description">
            <label class="form-label">Description</label>
            <div class="input input-lg bg-backgroundSecondary">
              {{ selectedTableInfo.description || "No description available" }}
            </div>
          </div>
          <div class="form-field" *ngIf="selectedTableInfo.created_at">
            <label class="form-label">Created At</label>
            <div class="input input-lg bg-backgroundSecondary">
              {{ selectedTableInfo.created_at | date : "medium" }}
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div
          *ngIf="selectedTableInfo.error"
          class="bg-error bg-opacity-10 p-4 rounded-lg border border-error mb-4"
        >
          <div class="flex items-start">
            <i class="ti ti-alert-triangle text-error text-2xl mr-3 mt-1"></i>
            <div>
              <h3 class="text-lg font-semibold text-error mb-2">Error</h3>
              <p class="text-content1">
                {{
                  selectedTableInfo.errorMessage ||
                    "An error occurred while fetching table details."
                }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer Buttons -->
      <div class="flex justify-end mt-4">
        <label for="table-view-modal" class="btn btn-outline-default">
          Close
        </label>
      </div>
    </div>
  </div>
</div>

<input class="modal-state" id="connect-DB" type="checkbox" />
<div class="modal w-screen">
  <label class="modal-overlay"></label>
  <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
    <!-- Loading overlay -->
    <div
      *ngIf="loading"
      class="absolute inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 rounded-lg"
    >
      <div class="flex flex-col items-center">
        <div
          class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"
        ></div>
        <p class="text-white mt-4">Connecting to database...</p>
      </div>
    </div>

    <div class="modal-header">
      <h2 class="text-xl">Create New Connection</h2>
      <label for="connect-DB" class="modal-close-btn"
        ><i class="ti ti-x"></i
      ></label>
    </div>
    <div class="py-3 flex items-center justify-center border-b">
      <app-stepper
        [steps]="[
          { title: 'choose your data source' },
          { title: 'specify connection details' }
        ]"
        [activeStep]="stepIndex"
        [completedSteps]="stepIndex === 1 ? [0] : []"
      ></app-stepper>
    </div>
    <div class="p-3 py-2 max-h-[80%] overflow-auto">
      <!-- Step 1: Choose Your Data Source -->
      <div id="Step1" *ngIf="stepIndex === 0">
        <div class="form-control relative w-full border-1 mb-3">
          <input
            type="text"
            class="input input-lg max-w-full pl-10"
            placeholder="Search database types..."
            [(ngModel)]="dbListSearchTerm"
          />
          <span class="absolute inset-y-0 left-3 inline-flex items-center">
            <i class="ti ti-search"></i>
          </span>
        </div>
        <div class="grid grid-cols-3 gap-3">
          <app-select-card
            *ngFor="let db of filteredDBList"
            [isSelected]="selectedCard === db.id"
            (selected)="setSelectedCard(db.id, db.key)"
          >
            <div class="flex flex-col items-center">
              <img
                src="{{ db.icon }}"
                class="max-h-[45px] h-auto w-auto object-contain mb-2"
                alt="{{ db.name }}"
              />
              <p class="text-center font-semibold">
                {{ db.name }}
              </p>
            </div>
          </app-select-card>
        </div>
      </div>

      <!-- Step 2: Specify Connection Details -->
      <div id="step2" *ngIf="stepIndex === 1">
        <section class="py-3">
          <div class="form-group">
            <div class="form-field">
              <div class="form-field">
                <label class="form-label">Set New Connection Name*</label>
                <input
                  placeholder="Enter Name"
                  type="text"
                  class="input input-lg max-w-full"
                  [ngClass]="{
                    'input-error':
                      formErrors.DB_MainConnection &&
                      touchedFields.DB_MainConnection
                  }"
                  [(ngModel)]="connectionDetails.DB_MainConnection"
                  (input)="
                    onInputChange(
                      'DB_MainConnection',
                      connectionDetails.DB_MainConnection
                    )
                  "
                  (blur)="
                    validateField(
                      'DB_MainConnection',
                      connectionDetails.DB_MainConnection
                    )
                  "
                />
                <div
                  *ngIf="
                    formErrors.DB_MainConnection &&
                    touchedFields.DB_MainConnection
                  "
                  class="form-text text-sm text-error mt-1"
                >
                  {{ formErrors.DB_MainConnection }}
                </div>
                <div
                  *ngIf="
                    !formErrors.DB_MainConnection ||
                    !touchedFields.DB_MainConnection
                  "
                  class="form-text text-sm text-content3 mt-1"
                >
                  This name must be unique and will be used to identify this
                  connection.
                </div>
              </div>
            </div>
            <div class="form-field">
              <label class="form-label">Existing Database Name*</label>
              <input
                placeholder="Enter Existing Database Name"
                type="text"
                class="input input-lg max-w-full"
                [ngClass]="{
                  'input-error':
                    formErrors.connection_name && touchedFields.connection_name
                }"
                [(ngModel)]="connectionDetails.connection_name"
                (input)="
                  onInputChange(
                    'connection_name',
                    connectionDetails.connection_name
                  )
                "
                (blur)="
                  validateField(
                    'connection_name',
                    connectionDetails.connection_name
                  )
                "
              />
              <div
                *ngIf="
                  formErrors.connection_name && touchedFields.connection_name
                "
                class="form-text text-sm text-error mt-1"
              >
                {{ formErrors.connection_name }}
              </div>
            </div>
            <div class="form-field">
              <label class="form-label">Host*</label>
              <input
                placeholder="Enter host (e.g., localhost or ***********)"
                type="text"
                class="input input-lg max-w-full"
                [ngClass]="{
                  'input-error':
                    formErrors.database_host_name &&
                    touchedFields.database_host_name
                }"
                [(ngModel)]="connectionDetails.database_host_name"
                (input)="
                  onInputChange(
                    'database_host_name',
                    connectionDetails.database_host_name
                  )
                "
                (blur)="
                  validateField(
                    'database_host_name',
                    connectionDetails.database_host_name
                  )
                "
              />
              <div
                *ngIf="
                  formErrors.database_host_name &&
                  touchedFields.database_host_name
                "
                class="form-text text-sm text-error mt-1"
              >
                {{ formErrors.database_host_name }}
              </div>
            </div>

            <div class="form-field">
              <label class="form-label">Port*</label>
              <input
                placeholder="Enter Port (e.g., 3306 for MySQL)"
                type="text"
                class="input input-lg max-w-full"
                [ngClass]="{
                  'input-error':
                    formErrors.database_port && touchedFields.database_port
                }"
                [(ngModel)]="connectionDetails.database_port"
                (input)="
                  onInputChange(
                    'database_port',
                    connectionDetails.database_port
                  )
                "
                (blur)="
                  validateField(
                    'database_port',
                    connectionDetails.database_port
                  )
                "
              />
              <div
                *ngIf="formErrors.database_port && touchedFields.database_port"
                class="form-text text-sm text-error mt-1"
              >
                {{ formErrors.database_port }}
              </div>
            </div>
            <div class="form-field">
              <label class="form-label">Database Username*</label>
              <div class="form-control relative w-full">
                <input
                  type="text"
                  class="input input-lg max-w-full"
                  placeholder="Enter Username"
                  [ngClass]="{
                    'input-error':
                      formErrors.database_username &&
                      touchedFields.database_username
                  }"
                  [(ngModel)]="connectionDetails.database_username"
                  (input)="
                    onInputChange(
                      'database_username',
                      connectionDetails.database_username
                    )
                  "
                  (blur)="
                    validateField(
                      'database_username',
                      connectionDetails.database_username
                    )
                  "
                />

                <span
                  class="absolute inset-y-0 right-4 inline-flex items-center"
                >
                  <i class="ti ti-user text-content3"></i>
                </span>
              </div>
              <div
                *ngIf="
                  formErrors.database_username &&
                  touchedFields.database_username
                "
                class="form-text text-sm text-error mt-1"
              >
                {{ formErrors.database_username }}
              </div>
            </div>
            <div class="form-field">
              <label class="form-label">Database Password*</label>
              <div class="form-control relative w-full">
                <input
                  [type]="showPassword ? 'text' : 'password'"
                  class="input input-lg max-w-full"
                  placeholder="Password"
                  [ngClass]="{
                    'input-error':
                      formErrors.database_password &&
                      touchedFields.database_password
                  }"
                  [(ngModel)]="connectionDetails.database_password"
                  (input)="
                    onInputChange(
                      'database_password',
                      connectionDetails.database_password
                    )
                  "
                  (blur)="
                    validateField(
                      'database_password',
                      connectionDetails.database_password
                    )
                  "
                />

                <span
                  class="absolute inset-y-0 right-4 inline-flex items-center text-content2 cursor-pointer"
                  (click)="showPassword = !showPassword"
                >
                  <i
                    class="ti ti-eye-off text-xl text-content3"
                    *ngIf="!showPassword"
                  ></i>
                  <i
                    class="ti ti-eye text-xl text-content3"
                    *ngIf="showPassword"
                  ></i>
                </span>
              </div>
              <div
                *ngIf="
                  formErrors.database_password &&
                  touchedFields.database_password
                "
                class="form-text text-sm text-error mt-1"
              >
                {{ formErrors.database_password }}
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
    <div class="flex gap-3 p-3 border-t justify-end">
      <div class="flex gap-3" id="step1-btns">
        <button
          *ngIf="stepIndex > 0"
          (click)="prevStep()"
          class="btn btn-sm"
          [disabled]="loading"
        >
          Back
        </button>
        <button
          *ngIf="stepIndex === 0"
          (click)="nextStep()"
          class="btn btn-primary btn-sm"
          [disabled]="loading"
        >
          Next
        </button>
        <button
          *ngIf="stepIndex === 1 && canCreateConnection"
          (click)="connectDB()"
          class="btn btn-primary btn-sm"
          [disabled]="loading"
        >
          <i class="ti ti-loader animate-spin mr-2" *ngIf="loading"></i>
          {{ loading ? "Connecting..." : "Connect and Save" }}
        </button>
        <button
          *ngIf="stepIndex === 1 && !canCreateConnection"
          class="btn btn-primary btn-sm cursor-not-allowed opacity-60"
          disabled
          title="You don't have permission to create connections"
        >
          Connect and Save
        </button>
      </div>
    </div>
  </div>
</div>

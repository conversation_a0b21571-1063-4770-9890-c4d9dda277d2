import {
  Component,
  OnInit,
  AfterViewChecked,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DatabaseService } from 'src/app/core/services/database/database.service';
import hljs from 'highlight.js/lib/core'; // Import highlight.js core
import javascript from 'highlight.js/lib/languages/javascript'; // Import JavaScript language
import python from 'highlight.js/lib/languages/python'; // Import Python language (example)
import { PromptsService } from 'src/app/core/services/prompts/prompts.service';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { AuthService } from 'src/app/core/auth/auth.service';

interface TransformationResult {
  object_name: string;
  has_api_transformation: boolean;
  has_rdbms_transformation: boolean;
  original_code: string;
  api_code: string | null;
  rdbms_code: string;
  summary: string;
  target_db_type: string;
  transformation_timestamp: string;
}

@Component({
  selector: 'app-code-review',
  templateUrl: './code-review.component.html',
  styleUrls: ['./code-review.component.css'],
})
export class CodeReviewComponent implements OnInit, AfterViewChecked {
  databaseId!: string;
  objectName!: string;
  objectType!: string;
  sourceDB!: string;
  resultCode: string | null = null;
  transformationData!: TransformationResult;
  selectedOptions: string[] = [];
  data: any;
  selectedCard: number = 1;

  searchTerm: string = '';
  searchDB: string = '';
  selectedDbType: string = '';

  // Database connection status
  isConnected: boolean = false;

  // Copy success states
  copySuccess = false;
  codeAreaCopySuccess = false;
  originalCodeCopySuccess = false;

  // Copying in progress states
  isCopying = false;
  isCodeAreaCopying = false;
  isOriginalCodeCopying = false;
  loading: boolean = false;
  loadingData: boolean = false;

  getFilteredDBList(): any[] {
    // Return the filtered list based on the search query
    return this.DBList.filter((db) =>
      db.name.toLowerCase().includes(this.searchDB.toLowerCase())
    );
  }

  constructor(
    private route: ActivatedRoute,
    private dbService: DatabaseService,
    private router: Router,
    private promptsService: PromptsService,
    private sanitizer: DomSanitizer,
    private authService: AuthService
  ) {}

  ngOnInit() {
    // Load user permissions first
    this.loadUserPermissions();

    this.route.queryParamMap.subscribe((params) => {
      this.databaseId = params.get('id') || '';
      this.objectName = params.get('objectName') || '';
      this.objectType = params.get('objectType') || '';

      const storedData = localStorage.getItem('activeDatabaseInfo');
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        const { type } = parsedData;
        this.sourceDB = type;

        // Check if connection status is available in localStorage
        if (parsedData.connection_status) {
          this.isConnected = parsedData.connection_status === 'Connected';
        } else {
          // If not available, check the database connection status
          this.checkDatabaseConnectionStatus();
        }

        if (this.databaseId && this.objectName && this.objectType) {
          this.getCodeReviewData();
        }
      } else {
        console.warn('No activeDatabaseInfo found in localStorage.');
      }
    });
    this.loadPrompts();
  }

  /**
   * Check the database connection status
   */
  checkDatabaseConnectionStatus() {
    if (!this.databaseId) {
      console.error('Missing database ID for checking connection status');
      return;
    }

    // Use getDatabaseAnalytics instead of getDatabaseDetails
    this.dbService.getDatabaseAnalytics(this.databaseId, '').subscribe({
      next: (response: any) => {
        if (response && response.connection_status) {
          this.isConnected = response.connection_status === 'Connected';
          console.log(
            'Database connection status:',
            this.isConnected ? 'Connected' : 'Disconnected'
          );

          // Update the localStorage with the connection status
          const storedData = localStorage.getItem('activeDatabaseInfo');
          if (storedData) {
            const parsedData = JSON.parse(storedData);
            parsedData.connection_status = response.connection_status;
            localStorage.setItem(
              'activeDatabaseInfo',
              JSON.stringify(parsedData)
            );
          }
        } else {
          // Default to disconnected if status is not available
          this.isConnected = false;
        }
      },
      error: (error: any) => {
        console.error('Error checking database connection status:', error);
        this.isConnected = false; // Default to disconnected on error
      },
    });
  }

  ngAfterViewChecked() {
    if (this.transformationData) {
      this.highlightCode();
    }
  }

  getSanitizedOriginalCode(): SafeHtml {
    if (!this.transformationData || !this.transformationData.original_code) {
      return '';
    }

    const code = this.transformationData.original_code;
    const language = this.getLanguageFromCode(code);
    const escapedCode = this.escapeHtml(code);

    // Add a wrapper with the language class to help highlight.js
    const wrappedCode = `<span class="${language}">${escapedCode}</span>`;
    return this.sanitizer.bypassSecurityTrustHtml(wrappedCode);
  }

  getSanitizedRdbmsCode(): SafeHtml {
    if (!this.transformationData || !this.transformationData.rdbms_code) {
      return '';
    }

    const code = this.transformationData.rdbms_code;
    const language = this.getLanguageFromCode(code);
    const escapedCode = this.escapeHtml(code);

    // Add a wrapper with the language class to help highlight.js
    const wrappedCode = `<span class="${language}">${escapedCode}</span>`;
    return this.sanitizer.bypassSecurityTrustHtml(wrappedCode);
  }

  getSanitizedApiCode(): SafeHtml {
    if (!this.transformationData || !this.transformationData.api_code) {
      return '';
    }

    const code = this.transformationData.api_code || '';
    const language = this.getLanguageFromCode(code);
    const escapedCode = this.escapeHtml(code);

    // Add a wrapper with the language class to help highlight.js
    const wrappedCode = `<span class="${language}">${escapedCode}</span>`;
    return this.sanitizer.bypassSecurityTrustHtml(wrappedCode);
  }

  // Helper method to escape HTML special characters
  private escapeHtml(unsafe: string): string {
    return unsafe
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  getCodeReviewData() {
    this.loadingData = true;
    if (!this.databaseId || !this.objectType || !this.objectName) {
      console.error(
        'Missing required parameters for getting transformation result'
      );

      return;
    }

    this.dbService
      .getTransformationResult(
        this.databaseId,
        this.objectType,
        this.objectName
      )
      .subscribe({
        next: (res: TransformationResult) => {
          this.transformationData = res;
          console.log('Full Transformation Result:', this.transformationData);
          this.loadingData = false;
          // 👇 Call this only after transformationData is set
          this.setAvailableTargets();

          // Trigger highlighting after a short delay to ensure the view is updated
          setTimeout(() => {
            this.highlightCode();
          }, 100);
        },
        error: (err) => {
          this.loadingData = false;
          console.error('Failed to fetch transformation result:', err);
        },
      });
  }

  highlightCode() {
    if (!this.transformationData) {
      return;
    }

    // Register JavaScript and Python languages
    if (!hljs.getLanguage('javascript')) {
      hljs.registerLanguage('javascript', javascript);
    }

    if (!hljs.getLanguage('python')) {
      hljs.registerLanguage('python', python);
    }

    // Apply syntax highlighting after the view is updated
    const codeBlocks = document.querySelectorAll('pre code');
    codeBlocks.forEach((block) => {
      // Check if the block has already been highlighted
      if (block.getAttribute('data-highlighted') === 'yes') {
        // Remove the data-highlighted attribute to allow re-highlighting
        block.removeAttribute('data-highlighted');
      }

      // Get the content to determine language
      const content = block.textContent || '';
      const language = this.getLanguageFromCode(content);

      // Add language class if not present
      if (!block.classList.contains(language)) {
        block.classList.add(language);
      }

      // Apply highlighting
      hljs.highlightElement(block as HTMLElement);
    });
  }

  // Function to determine the language dynamically based on content (or other criteria)
  getLanguageFromCode(code: string): string {
    if (!code) return 'javascript';

    // Simple example: check for keywords or file extension to determine language
    if (code.includes('function') || code.includes('const')) {
      return 'javascript';
    } else if (code.includes('def') || code.includes('import')) {
      return 'python';
    } else {
      return 'javascript'; // Default fallback
    }
  }

  selectedTarget = 'code'; // default
  availableTargets: string[] = [];

  ngOnChanges() {
    this.setAvailableTargets();
  }

  // Method to handle target change and trigger highlighting
  onTargetChange() {
    // Use setTimeout to ensure the view is updated before highlighting
    setTimeout(() => {
      this.highlightCode();
    }, 0);
  }

  setAvailableTargets() {
    this.availableTargets = [];

    if (this.transformationData?.has_rdbms_transformation) {
      this.availableTargets.push('code');
    }

    if (this.transformationData?.has_api_transformation) {
      this.availableTargets.push('api');
    }

    // Set default
    if (this.availableTargets.length === 1) {
      this.selectedTarget = this.availableTargets[0];
    } else if (!this.availableTargets.includes(this.selectedTarget)) {
      this.selectedTarget = this.availableTargets[0] || 'code';
    }
  }

  @ViewChild('container') containerRef!: ElementRef;

  leftWidth = 50;
  resizing = false;

  startResizing(_event: MouseEvent) {
    this.resizing = true;
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';

    const container = this.containerRef.nativeElement;
    const containerRect = container.getBoundingClientRect();

    const onMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - containerRect.left;
      const containerWidth = containerRect.width;
      const newLeftWidth = (deltaX / containerWidth) * 100;

      if (newLeftWidth > 10 && newLeftWidth < 90) {
        this.leftWidth = newLeftWidth;
      }
    };

    const onMouseUp = () => {
      this.resizing = false;
      document.body.style.cursor = 'default';
      document.body.style.userSelect = 'auto';

      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mouseup', onMouseUp);
    };

    window.addEventListener('mousemove', onMouseMove);
    window.addEventListener('mouseup', onMouseUp);
  }

  // To track if the API call was successful
  success: boolean = false;
  // Store the success message
  successMessage: string = 'Transformation successful!';
  errorMessage: string = '';

  // User permissions
  isSuperUser: boolean = false;
  isAdmin: boolean = false;
  userPermissions: string[] = [];
  permissionsLoaded: boolean = false;

  // Permission flags for specific actions
  canTransformCode: boolean = false;
  canRegenerateCode: boolean = false;
  canPushToGithub: boolean = false;

  fineTuneCode() {
    // Check if the database is connected
    if (!this.isConnected) {
      console.error('Cannot fine-tune code when database is disconnected');
      this.errorMessage = 'Database must be connected to fine-tune code.';
      return;
    }

    if (!this.transformationData || !this.transformationData.object_name) {
      console.error('Missing transformation data or object name');
      return;
    }

    const storedData = localStorage.getItem('activeDatabaseInfo');
    this.success = false;

    if (storedData) {
      const { connection, id } = JSON.parse(storedData);

      const objectName = this.transformationData.object_name;
      const objectType = this.objectType.toLowerCase();

      if (connection && id && id.length === 36) {
        this.loading = true;

        // Determine if we're transforming to API or RDBMS based on selectedTarget
        const regenerateApi = this.selectedTarget === 'api';
        const regenerateRdbms = this.selectedTarget === 'code';
        const targetDbType =
          this.selectedTarget === 'code'
            ? this.transformationData.target_db_type
            : '';

        // Use regenerateTransformedCode instead of transformCode
        this.dbService
          .regenerateTransformedCode(
            id,
            objectName,
            objectType,
            regenerateApi,
            regenerateRdbms,
            targetDbType,
            this.selectedOptions
          )
          .subscribe(
            (response) => {
              console.log('Regeneration Success:', response);
              this.loading = false;
              localStorage.setItem(
                'transformationResponse',
                JSON.stringify(response)
              );

              // Display success screen or message
              this.success = true;
              this.successMessage = 'Code regeneration was successful!';

              // Refresh the code review data after successful transformation
              this.getCodeReviewData();
            },
            (error) => {
              console.error('Error in code regeneration:', error);
              this.loading = false;
              this.success = false;
              this.errorMessage = 'An error occurred during code regeneration.';
            }
          );
      } else {
        console.warn('Invalid or missing database ID.');
        this.router.navigate(['/database', connection], {
          queryParams: { id },
        });
      }
    } else {
      console.warn('No active database found.');
      this.router.navigate(['/database']);
    }
  }
  promptData: any[] = [];
  totalEntries: number = 0;
  loadPrompts() {
    const skip = 0;
    const limit = 1000;

    // Get organization ID from localStorage
    const organization_id =
      localStorage.getItem('organization_id') || undefined;

    // 1. Load paginated data
    this.promptsService
      .getPrompts(undefined, undefined, skip, limit, organization_id)
      .subscribe({
        next: (res) => {
          const promptList = res ?? [];
          this.promptData = promptList.map((item: any) => ({
            ...item,
            promptName: item.title,
            type: item.prompt_type,
            description: item.description,
            source: item.source_db_type,
            target: item.target_db_type,
            status: 'Active',
          }));
          console.log(this.promptData);
        },
        error: (err) => console.error('Paginated data error:', err),
      });

    // 2. Load all data to calculate total entries (only count)
    this.promptsService
      .getPrompts(undefined, undefined, 0, 10000, organization_id)
      .subscribe({
        next: (res) => {
          this.totalEntries = res?.length ?? 0;
        },
        error: (err) => console.error('Total count error:', err),
      });
  }

  // Database List
  DBList = [
    { id: 1, key: 'sybase', name: 'Sybase', icon: 'assets/sybase.png' },
    {
      id: 2,
      key: 'postgresql',
      name: 'PostgreSQL',
      icon: 'assets/postgrace.png',
    },
    {
      id: 3,
      key: 'sql_server',
      name: 'SQL Server',
      icon: 'assets/sql server.png',
    },
    { id: 4, key: 'mysql', name: 'MySQL', icon: 'assets/mysql.png' },
    {
      id: 5,
      key: 'amazon_rds_pg',
      name: 'Amazon RDS PostgrSQL',
      icon: 'assets/rds.png',
    },
    {
      id: 6,
      key: 'amazon_athena',
      name: 'Amazon Athena',
      icon: 'assets/amzathena.png',
    },
    { id: 7, key: 'oracle', name: 'Oracle', icon: 'assets/oracle.png' },
  ];
  setSelectedCard(cardNumber: number, key: string) {
    this.selectedCard = cardNumber;
    this.selectedDbType = key;
  }
  get filteredOptions(): any[] {
    return this.promptData.filter((prompt) => {
      // Text search condition (title and description)
      const textSearchCondition = this.searchTerm
        ? prompt.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
          prompt.description
            .toLowerCase()
            .includes(this.searchTerm.toLowerCase())
        : true;

      // Filter by database types matching the current transformation
      const sourceDbMatch =
        this.transformationData && prompt.source_db_type === this.sourceDB;

      const targetDbMatch =
        this.transformationData &&
        prompt.target_db_type === this.transformationData.target_db_type;

      // Return prompts that match both the text search and the DB type filters
      return textSearchCondition && sourceDbMatch && targetDbMatch;
    });
  }

  toggleSelection(item: { title: string; description: string }): void {
    const itemString = item.title + ' ' + item.description;

    if (this.selectedOptions.includes(itemString)) {
      this.selectedOptions = this.selectedOptions.filter(
        (opt) => opt !== itemString
      );
    } else {
      this.selectedOptions.push(itemString);
    }
  }

  copySummary(): void {
    if (!this.transformationData || !this.transformationData.summary) {
      console.error('No summary available to copy');
      return;
    }

    // Set copying state
    this.isCopying = true;

    // Get the raw text without HTML formatting
    const textToCopy = this.transformationData.summary;

    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        // Show success feedback using our method
        this.showCopySuccess();
      })
      .catch((err) => {
        console.error('Could not copy text: ', err);
        // Fallback for older browsers
        this.fallbackCopyTextToClipboard(textToCopy, false, false);
      });
  }

  // Fallback function for browsers that don't support navigator.clipboard
  // This is now handled by the enhanced fallbackCopyTextToClipboard method below

  // Optional: Add a visual feedback method
  showCopySuccess(): void {
    // Reset copying state and show success
    this.isCopying = false;
    this.copySuccess = true;

    // Use zone.js to ensure Angular detects the change
    setTimeout(() => {
      this.copySuccess = false;
    }, 2000);
  }

  /**
   * Copy the AI-generated code to clipboard
   */
  copyGeneratedCode(): void {
    let codeToCopy = '';

    // Set copying state
    this.isCodeAreaCopying = true;

    // Get the appropriate code based on the selected target
    if (this.selectedTarget === 'code' && this.transformationData.rdbms_code) {
      codeToCopy = this.transformationData.rdbms_code;
    } else if (
      this.selectedTarget === 'api' &&
      this.transformationData.api_code
    ) {
      codeToCopy = this.transformationData.api_code || '';
    }

    if (!codeToCopy) {
      console.error('No code available to copy');
      this.isCodeAreaCopying = false;
      return;
    }

    // Try to use the clipboard API
    navigator.clipboard
      .writeText(codeToCopy)
      .then(() => {
        this.showCodeAreaCopySuccess();
      })
      .catch((err) => {
        console.error('Could not copy code: ', err);
        // Fallback for older browsers
        this.fallbackCopyTextToClipboard(codeToCopy, true, false);
      });
  }

  /**
   * Show success feedback for code area copy
   */
  showCodeAreaCopySuccess(): void {
    // Reset copying state and show success
    this.isCodeAreaCopying = false;
    this.codeAreaCopySuccess = true;

    // Use zone.js to ensure Angular detects the change
    setTimeout(() => {
      this.codeAreaCopySuccess = false;
    }, 2000);
  }

  /**
   * Copy the original code to clipboard
   */
  copyOriginalCode(): void {
    // Set copying state
    this.isOriginalCodeCopying = true;

    if (!this.transformationData || !this.transformationData.original_code) {
      console.error('No original code available to copy');
      this.isOriginalCodeCopying = false;
      return;
    }

    const codeToCopy = this.transformationData.original_code;

    // Try to use the clipboard API
    navigator.clipboard
      .writeText(codeToCopy)
      .then(() => {
        this.showOriginalCodeCopySuccess();
      })
      .catch((err) => {
        console.error('Could not copy original code: ', err);
        // Fallback for older browsers
        this.fallbackCopyTextToClipboard(codeToCopy, false, true);
      });
  }

  /**
   * Show success feedback for original code area copy
   */
  showOriginalCodeCopySuccess(): void {
    // Reset copying state and show success
    this.isOriginalCodeCopying = false;
    this.originalCodeCopySuccess = true;

    // Use zone.js to ensure Angular detects the change
    setTimeout(() => {
      this.originalCodeCopySuccess = false;
    }, 2000);
  }

  /**
   * Fallback copy method with option to specify which success indicator to show
   * @param text The text to copy
   * @param isCodeArea Whether this is for the AI-generated code area
   * @param isOriginalCode Whether this is for the original code area
   */
  fallbackCopyTextToClipboard(
    text: string,
    isCodeArea: boolean = false,
    isOriginalCode: boolean = false
  ): void {
    // Note: Copying states are already set in the calling methods

    const textArea = document.createElement('textarea');
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const successful = document.execCommand('copy');
      if (successful) {
        if (isCodeArea) {
          this.showCodeAreaCopySuccess();
        } else if (isOriginalCode) {
          this.showOriginalCodeCopySuccess();
        } else {
          this.showCopySuccess();
        }
      } else {
        // Reset copying states if copy failed
        if (isCodeArea) {
          this.isCodeAreaCopying = false;
        } else if (isOriginalCode) {
          this.isOriginalCodeCopying = false;
        } else {
          this.isCopying = false;
        }
      }
    } catch (err) {
      console.error('Fallback: Oops, unable to copy', err);
      // Reset copying states on error
      if (isCodeArea) {
        this.isCodeAreaCopying = false;
      } else if (isOriginalCode) {
        this.isOriginalCodeCopying = false;
      } else {
        this.isCopying = false;
      }
    }

    document.body.removeChild(textArea);
  }

  /**
   * Load user permissions from the auth service
   */
  loadUserPermissions(): void {
    this.authService.getUserRoleDetails().subscribe({
      next: (roleDetails) => {
        // Set basic user information
        this.isSuperUser = roleDetails.is_superuser || false;

        // Case-insensitive check for Admin role
        const roleName = roleDetails.role?.name || '';
        this.isAdmin = roleName.toLowerCase() === 'admin';

        // Get permissions
        if (roleDetails.role && roleDetails.role.permissions) {
          // Handle permissions in array format (from user-role endpoint)
          if (Array.isArray(roleDetails.role.permissions)) {
            this.userPermissions = roleDetails.role.permissions;
          }
          // Handle permissions in object format (for backward compatibility)
          else {
            this.userPermissions = Object.entries(roleDetails.role.permissions)
              .filter(([_, value]) => value === true)
              .map(([key, _]) => key);
          }
        }

        // Check specific permissions
        this.canTransformCode =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('transform_code');

        this.canRegenerateCode =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('regenerate_code');

        this.canPushToGithub =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('push_to_github');

        // Mark permissions as loaded
        this.permissionsLoaded = true;

        console.log('User permissions loaded:', {
          isSuperUser: this.isSuperUser,
          isAdmin: this.isAdmin,
          permissions: this.userPermissions,
          canTransformCode: this.canTransformCode,
          canRegenerateCode: this.canRegenerateCode,
          canPushToGithub: this.canPushToGithub,
        });
      },
      error: (err) => {
        console.error('Error loading user permissions:', err);
        // Fallback to getUserDetails if getUserRoleDetails fails
        this.fallbackToUserDetails();
      },
    });
  }

  /**
   * Fallback to getUserDetails if getUserRoleDetails fails
   */
  fallbackToUserDetails(): void {
    this.authService.getUserDetails().subscribe({
      next: (user) => {
        // Set basic user information
        this.isSuperUser = user.is_superuser || false;

        // Case-insensitive check for Admin role
        const roleName = user.role?.name || '';
        this.isAdmin = roleName.toLowerCase() === 'admin';

        // Get permissions
        if (user.role && user.role.permissions) {
          // Handle permissions in array format
          if (Array.isArray(user.role.permissions)) {
            this.userPermissions = user.role.permissions;
          }
          // Handle permissions in object format
          else {
            this.userPermissions = Object.entries(user.role.permissions)
              .filter(([_, value]) => value === true)
              .map(([key, _]) => key);
          }
        }

        // Check specific permissions
        this.canTransformCode =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('transform_code');

        this.canRegenerateCode =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('regenerate_code');

        this.canPushToGithub =
          this.isSuperUser ||
          this.isAdmin ||
          this.userPermissions.includes('push_to_github');

        // Mark permissions as loaded
        this.permissionsLoaded = true;

        console.log('User permissions loaded (fallback):', {
          isSuperUser: this.isSuperUser,
          isAdmin: this.isAdmin,
          permissions: this.userPermissions,
          canTransformCode: this.canTransformCode,
          canRegenerateCode: this.canRegenerateCode,
          canPushToGithub: this.canPushToGithub,
        });
      },
      error: (err) => {
        console.error('Error loading user details:', err);
        // Even if permissions fail to load, set defaults to allow basic viewing
        this.permissionsLoaded = true;
      },
    });
  }

  /**
   * Check if the user has a specific permission
   * @param permission The permission to check
   * @returns True if the user has the permission, false otherwise
   */
  hasPermission(permission: string): boolean {
    return (
      this.isSuperUser ||
      this.isAdmin ||
      this.userPermissions.includes(permission)
    );
  }

  /**
   * Push the code to Github
   * This method checks if the database is connected before allowing the user to push to Github
   */
  pushToGithub(): void {
    // Check if the database is connected
    if (!this.isConnected) {
      console.error('Cannot push to Github when database is disconnected');
      this.errorMessage = 'Database must be connected to push to Github.';
      return;
    }

    // TODO: Implement the actual push to Github functionality
    console.log('Pushing to Github...');

    // For now, just show a success message
    this.success = true;
    this.successMessage = 'Code pushed to Github successfully!';
  }
}

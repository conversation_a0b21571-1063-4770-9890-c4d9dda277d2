<!-- Peeking Chatbot Icon -->
<div
  *ngIf="!isChatbotOpen"
  class="fixed bottom-24 right-0 bg-secondary p-2 rounded-l-lg shadow-lg z-50 cursor-pointer transition-all duration-300 peeking-icon"
  (click)="toggleChatbot()"
  title="Open Chat Assistant"
>
  <div class="flex items-center">
    <img src="assets/BOT.png" class="h-12 w-12" />
  </div>
</div>

<!-- Chatbot Window -->
<div
  *ngIf="isChatbotOpen"
  class="fixed bottom-6 right-6 max-w-[440px] w-[90%] max-h-[773px] h-[80vh] bg-white shadow-lg border border-gray-200 rounded-lg z-50 flex flex-col transition-all duration-300 chatbot-window"
>
  <div
    class="flex justify-between items-center bg-secondary rounded-lg rounded-b-none p-2"
  >
    <div class="flex items-center">
      <img src="assets/BOT.png" class="h-8 w-8 mr-2" />
      <h2 class="text-xl text-white">AI Agent Assistant</h2>
    </div>
    <!-- Close/Minimize button -->
    <button
      (click)="toggleChatbot()"
      class="btn btn-sm btn-circle btn-ghost text-white hover:text-black transition-all hover:bg-white hover:bg-opacity-20"
      title="Minimize"
    >
      <i class="ti ti-x"></i>
    </button>
  </div>
  <!-- Loading State -->
  <div class="p-4 flex flex-col flex-1 w-full justify-center items-center">
    <div
      *ngIf="chatbotState === 'loading'"
      class="flex flex-col items-center justify-center flex-1"
    >
      <img
        src="assets/BOT.png"
        alt="Chatbot"
        class="w-16 h-16 rounded-full mb-2 animate-pulse"
      />
      <p class="text-sm text-gray-600 animate-pulse">Loading...</p>
    </div>

    <!-- Greeting with Grid Message Boxes -->
    <div
      *ngIf="chatbotState === 'greeting'"
      class="flex flex-col flex-1 items-center text-center"
      [@fadeIn]
    >
      <img
        src="assets/BOT.png"
        alt="Chatbot"
        class="w-16 h-16 rounded-full mb-2"
      />
      <h3 class="text-lg font-semibold text-primary">Hello user</h3>
      <p class="text-sm text-gray-600 mb-4">How can I help you today?</p>
      <div class="grid grid-cols-2 gap-3 w-full" [@fadeIn]>
        <button
          class="bg-gray-100 p-3 rounded-lg text-sm flex flex-col items-center"
          (click)="openChat()"
        >
          <span>Generate Migration Plan</span>
        </button>
        <button
          class="bg-gray-100 p-3 rounded-lg text-sm flex flex-col items-center"
          (click)="openChat()"
        >
          <span>Database complexity analysis.</span>
        </button>
        <button
          class="bg-gray-100 p-3 rounded-lg text-sm flex flex-col items-center"
          (click)="openChat()"
        >
          <span>Generate Migration Assesment report.</span>
        </button>
        <button
          class="bg-gray-100 p-3 rounded-lg text-sm flex flex-col items-center"
          (click)="openChat()"
        >
          <span>AWS migration best practices</span>
        </button>
      </div>
    </div>

    <!-- Chat Interface (should be outside greeting section) -->
    <div
      *ngIf="chatbotState === 'chat'"
      class="flex flex-col flex-1 overflow-y-auto"
    >
      <!-- Chat messages go here -->
    </div>
    <input
      type="text"
      placeholder="Type a message..."
      class="w-full border p-2 mt-2 rounded"
      (keydown.enter)="openChat()"
    />
  </div>
</div>

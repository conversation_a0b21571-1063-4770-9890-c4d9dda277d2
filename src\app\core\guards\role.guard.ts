import { Injectable } from '@angular/core';
import {
  CanActivate,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
} from '@angular/router';
import { AuthService } from '../auth/auth.service';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class RoleGuard implements CanActivate {
  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot
  ): Observable<boolean> {
    console.log('RoleGuard - canActivate called');

    // Get required roles from route data
    const requiredRoles = route.data['roles'] as string[];
    const requiredPermissions = route.data['permissions'] as string[];

    // First check if user is logged in
    if (!this.authService.isLoggedIn()) {
      console.log('RoleGuard - User not logged in, redirecting to login');
      this.router.navigate(['/']);
      return of(false);
    }

    // First try to get detailed role information
    return this.authService.getUserRoleDetails().pipe(
      map((roleDetails) => {
        console.log('RoleGuard - User role details:', roleDetails);
        console.log('RoleGuard - Required roles:', requiredRoles);
        console.log('RoleGuard - Required permissions:', requiredPermissions);

        if (!roleDetails) {
          console.log('RoleGuard - No role details, redirecting to login');
          this.router.navigate(['/']);
          return false;
        }

        // Super users can access everything
        if (roleDetails.is_superuser) {
          console.log('RoleGuard - User is a superuser, access granted');
          return true;
        }

        // Check if user has required role
        if (requiredRoles && requiredRoles.length > 0) {
          const userRole = roleDetails.role?.name;
          const userRoleId = roleDetails.role_id;

          console.log('RoleGuard - User role:', userRole);
          console.log('RoleGuard - User role_id:', userRoleId);

          // Check if user has a role_id (even if role name is not properly loaded)
          const hasRoleId = userRoleId !== undefined && userRoleId !== null;

          // Case-insensitive role check
          const hasRequiredRole = requiredRoles.some(
            (role) => userRole && userRole.toLowerCase() === role.toLowerCase()
          );

          console.log('RoleGuard - Role checks:', {
            hasRequiredRole: hasRequiredRole,
            hasRoleId: hasRoleId,
          });

          // Allow access if user has either the required role name or a role_id
          // This handles cases where the role name might not be properly loaded
          if ((!userRole && !hasRoleId) || (!hasRequiredRole && !hasRoleId)) {
            console.log(
              'RoleGuard - User does not have required role or role_id, access denied'
            );
            this.router.navigate(['/dashboard']);
            return false;
          } else {
            console.log(
              'RoleGuard - User has required role or role_id, checking permissions'
            );
          }
        }

        // Check if user has required permissions
        if (requiredPermissions && requiredPermissions.length > 0) {
          // If user has Admin role, grant access regardless of specific permissions
          const roleName = roleDetails.role?.name || '';
          const userRoleId = roleDetails.role_id;
          const hasRoleId = userRoleId !== undefined && userRoleId !== null;

          // Check if user has Admin role by name (case-insensitive)
          const isAdminByName = roleName.toLowerCase() === 'admin';

          console.log('RoleGuard - Admin role check:', {
            roleName: roleName,
            roleNameLower: roleName.toLowerCase(),
            isAdminByName: isAdminByName,
            userRoleId: userRoleId,
            hasRoleId: hasRoleId,
          });

          // If user has Admin role by name or has a role_id, grant access
          if (isAdminByName || hasRoleId) {
            console.log(
              'RoleGuard - User has Admin role or role_id, granting access regardless of specific permissions'
            );
            return true;
          }

          // If user doesn't have role or permissions, deny access
          if (!roleDetails.role || !roleDetails.role.permissions) {
            console.log(
              'RoleGuard - User has no role or permissions, access denied'
            );
            this.router.navigate(['/dashboard']);
            return false;
          }

          console.log(
            'RoleGuard - User permissions:',
            roleDetails.role.permissions
          );

          // Check if user has any of the required permissions
          let hasRequiredPermission = false;

          // Handle permissions (always in array format from user-role endpoint)
          if (Array.isArray(roleDetails.role.permissions)) {
            console.log('RoleGuard - Permissions are in array format');
            hasRequiredPermission = requiredPermissions.some((permission) =>
              roleDetails.role.permissions.includes(permission)
            );
          } else {
            console.log('RoleGuard - Permissions are in object format');
            hasRequiredPermission = requiredPermissions.some(
              (permission) => roleDetails.role.permissions[permission] === true
            );
          }

          console.log(
            'RoleGuard - User has required permissions:',
            hasRequiredPermission
          );

          if (!hasRequiredPermission) {
            console.log(
              'RoleGuard - User does not have required permissions, access denied'
            );
            this.router.navigate(['/dashboard']);
            return false;
          }
        }

        console.log('RoleGuard - Access granted');
        return true;
      }),
      catchError((error) => {
        console.error('Error in role guard:', error);
        this.router.navigate(['/dashboard']);
        return of(false);
      })
    );
  }
}

<!-- Cards container -->
<div class="grid grid-cols-2 w-full gap-6 pb-6 profile-container">
  <!-- Left Card: Profile Info -->
  <div>
    <div class="bg-backgroundPrimary rounded shadow">
      <!-- Header area -->
      <div
        class="bg-secondary text-white rounded-t px-6 py-4 flex items-center justify-between"
      >
        <div class="flex items-center gap-4 p-4 rounded-lg max-w-sm">
          <!-- Avatar Image -->
          <div class="avatar">
            <img
              class="w-56 h-56 rounded-full"
              style="border: 2px solid white"
              src="../../../assets/icons/9d119d757ff7d7b36b9d71b86d973fbe.png"
              alt="User Avatar"
            />
          </div>
          <!-- User Details -->
          <div>
            <h3 class="text-xl">{{ userDetails.full_name }}</h3>
            <div class="flex flex-row gap-1">
              <!-- <p class="text-sm">
                {{
                  userRoleDetails?.role?.name ||
                    userDetails?.role?.name ||
                    "User"
                }}
              </p> -->
              <p class="text-xs" *ngIf="isSuperUser">
                <span class="bg-success text-white px-2 py-0.5 rounded-full"
                  >Super User</span
                >
              </p>
            </div>
          </div>
        </div>
        <!-- Edit button -->
        <button
          class="flex items-center gap-1 bg-white text-purple-700 text-sm px-3 py-1 rounded hover:bg-purple-50 transition"
          (click)="enableInput()"
        >
          <!-- Pencil Icon (Tabler) -->
          <img
            src="../../../assets/icons/pencil.png"
            alt="Edit Icon"
            class="w-4 h-4"
          />
          Edit
        </button>
      </div>

      <!-- Card body -->
      <div class="p-6 space-y-4">
        <!-- Organization -->
        <div>
          <!-- Label with Aligned Icon -->
          <label
            class="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1"
          >
            Organization
            <!-- Tooltip on hover for edit icon -->
            <div class="relative inline-block group">
              <img
                src="../../../assets/icons/pencil.png"
                alt="Edit Icon"
                class="w-4 h-4 cursor-pointer"
                (click)="enableInput()"
                data-ripple
              />
              <div
                class="absolute top-full left-0 mt-2 px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                Edit organization
              </div>
            </div>
          </label>
          <!-- Input Field -->
          <input
            type="text"
            [value]="
              userRoleDetails?.organization?.name ||
              userDetails?.organization?.name ||
              'Not assigned'
            "
            class="block w-full rounded border-gray-300 focus:ring-purple-600 focus:border-purple-600 p-2"
            [disabled]="isDisabled"
          />
        </div>

        <!-- Phone Number -->
        <div>
          <label
            class="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1"
          >
            Phone Number
            <div class="relative inline-block group">
              <img
                src="../../../assets/icons/pencil.png"
                alt="Edit Icon"
                class="w-4 h-4 cursor-pointer"
                (click)="enableInput()"
                data-ripple
              />
              <div
                class="absolute top-full left-0 mt-2 px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                Edit phone number
              </div>
            </div>
          </label>
          <input
            type="text"
            value="*************"
            class="block w-full rounded border-gray-300 focus:ring-purple-600 focus:border-purple-600"
            [disabled]="isDisabled"
          />
        </div>

        <!-- Email -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1"
            >Email</label
          >
          <input
            type="text"
            [value]="userDetails.email"
            mailto:value="<EMAIL>"
            class="block w-full rounded border-gray-300 focus:ring-purple-600 focus:border-purple-600"
            [disabled]="isDisabled"
          />
        </div>

        <!-- Managed By -->
        <!-- <div>
          <label class="block text-sm font-medium text-gray-700 mb-1"
            >Managed by</label
          >
          <input
            type="text"
            value="Microsoft"
            class="block w-full rounded border-gray-300 focus:ring-purple-600 focus:border-purple-600"
            [disabled]="isDisabled"
          />
        </div> -->
        <span class="text-sm" style="color: #71717a">Managed by Microsoft</span>

        <!-- Location -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1"
            >Location</label
          >
          <input
            type="text"
            value=""
            placeholder="None"
            class="block w-full rounded border-gray-300 focus:ring-purple-600 focus:border-purple-600"
            [disabled]="isDisabled"
          />
        </div>

        <!-- Change Password -->
        <div class="mt-2">
          <a
            href="#"
            class="text-600 hover:underline text-lg"
            style="color: #b2204f; font: 20px"
            >Change Password</a
          >
        </div>
      </div>
    </div>
    <!-- Role Card -->
    <div class="bg-backgroundPrimary rounded-lg shadow p-6 max-w-lg mt-6">
      <!-- Role Label -->
      <div class="flex justify-between items-center">
        <label class="block text-lg font-bold text-content1"
          >Role & Permissions</label
        >
        <button
          class="flex items-center gap-2 px-3 py-2 border border-[var(--border-color)] rounded-lg text-content1 hover:bg-backgroundSecondary"
          (click)="enableInput()"
          *ngIf="isSuperUser"
        >
          <img
            src="../../../assets/icons/edit.png"
            alt="Edit Icon"
            class="w-4 h-4"
          />
          EDIT
        </button>
      </div>
      <!-- Line Separator -->
      <hr class="my-4 border-[var(--border-color)]" />

      <!-- Role Name -->
      <div class="mb-4">
        <h3 class="text-md font-semibold text-content1 mb-2">Current Role</h3>
        <span class="px-3 py-1 text-sm rounded-full bg-primary text-white">
          {{ userRoleDetails?.role?.name || userDetails?.role?.name || "User" }}
        </span>
        <span
          class="px-3 py-1 text-sm rounded-full bg-success text-white ml-2"
          *ngIf="isSuperUser"
        >
          Super User
        </span>
      </div>

      <!-- Permissions Section -->
      <div *ngIf="permissions && permissions.length > 0">
        <h3 class="text-md font-semibold text-content1 mb-2">Permissions</h3>

        <!-- Simple list view of all permissions -->
        <div class="flex flex-wrap gap-2 mb-4">
          <span
            *ngFor="let permission of permissions"
            class="px-3 py-1 text-sm rounded-full bg-backgroundSecondary text-content2"
            [title]="permission"
          >
            {{ permission.replace("_", " ") }}
          </span>
        </div>

        <!-- Grouped permissions by category (collapsible) -->
        <div class="mt-4">
          <details class="mb-2">
            <summary
              class="cursor-pointer text-sm font-medium text-content1 hover:text-primary"
            >
              View permissions by category
            </summary>
            <div class="mt-2 pl-4 border-l-2 border-[var(--border-color)]">
              <!-- Connection permissions -->
              <div class="mb-3" *ngIf="hasPermissionType('connection')">
                <h4 class="text-sm font-semibold text-content1 mb-1">
                  Connection
                </h4>
                <div class="flex flex-wrap gap-2">
                  <span
                    *ngFor="
                      let permission of getPermissionsByType('connection')
                    "
                    class="px-2 py-0.5 text-xs rounded-full bg-backgroundSecondary text-content2"
                  >
                    {{ permission.replace("_", " ") }}
                  </span>
                </div>
              </div>

              <!-- Document permissions -->
              <div class="mb-3" *ngIf="hasPermissionType('document')">
                <h4 class="text-sm font-semibold text-content1 mb-1">
                  Document
                </h4>
                <div class="flex flex-wrap gap-2">
                  <span
                    *ngFor="let permission of getPermissionsByType('document')"
                    class="px-2 py-0.5 text-xs rounded-full bg-backgroundSecondary text-content2"
                  >
                    {{ permission.replace("_", " ") }}
                  </span>
                </div>
              </div>

              <!-- Code permissions -->
              <div class="mb-3" *ngIf="hasPermissionType('code')">
                <h4 class="text-sm font-semibold text-content1 mb-1">Code</h4>
                <div class="flex flex-wrap gap-2">
                  <span
                    *ngFor="let permission of getPermissionsByType('code')"
                    class="px-2 py-0.5 text-xs rounded-full bg-backgroundSecondary text-content2"
                  >
                    {{ permission.replace("_", " ") }}
                  </span>
                </div>
              </div>

              <!-- User permissions -->
              <div class="mb-3" *ngIf="hasPermissionType('user')">
                <h4 class="text-sm font-semibold text-content1 mb-1">
                  User Management
                </h4>
                <div class="flex flex-wrap gap-2">
                  <span
                    *ngFor="let permission of getPermissionsByType('user')"
                    class="px-2 py-0.5 text-xs rounded-full bg-backgroundSecondary text-content2"
                  >
                    {{ permission.replace("_", " ") }}
                  </span>
                </div>
              </div>

              <!-- Other permissions -->
              <div class="mb-3" *ngIf="hasOtherPermissions()">
                <h4 class="text-sm font-semibold text-content1 mb-1">Other</h4>
                <div class="flex flex-wrap gap-2">
                  <span
                    *ngFor="let permission of getOtherPermissions()"
                    class="px-2 py-0.5 text-xs rounded-full bg-backgroundSecondary text-content2"
                  >
                    {{ permission.replace("_", " ") }}
                  </span>
                </div>
              </div>
            </div>
          </details>
        </div>
      </div>

      <!-- No Permissions Message -->
      <div
        *ngIf="!permissions || permissions.length === 0"
        class="text-content2 text-sm"
      >
        No specific permissions assigned.
      </div>
    </div>
  </div>
  <!-- Right Card: Your Plan & Preferences -->
  <div class="space-y-6">
    <!-- Card 1: Your Plan -->
    <div class="bg-backgroundPrimary rounded-lg shadow p-6">
      <div class="flex items-center justify-between pb-4 border-b">
        <div class="flex items-center justify-between w-full">
          <h3 class="text-lg font-semibold text-content1">Your Plan</h3>
          <button
            class="flex items-center gap-1 text-sm border border-[var(--border-color)] px-3 py-1 rounded hover:bg-backgroundSecondary transition"
            (click)="enableInput()"
          >
            <img
              src="../../../assets/icons/edit.png"
              alt="Edit Icon"
              class="w-4 h-4"
            />
            Edit
          </button>
        </div>
      </div>
      <!-- Free Plan Badge -->
      <div class="mt-4 flex items-center justify-between">
        <span
          class="inline-block bg-backgroundSecondary text-content1 text-sm px-3 py-1 rounded"
        >
          Free Plan
        </span>
        <span
          class="inline-block px-3 py-1 text-sm rounded-full bg-success text-white"
        >
          Active
        </span>
      </div>
    </div>

    <!-- Card 2: Preferences -->
    <div class="bg-backgroundPrimary rounded-lg shadow p-6">
      <h4 class="text-sm font-bold text-content2 mb-3 border-b pb-4">
        Preferences
      </h4>
      <div class="space-y-3 text-sm text-content1">
        <!-- Theme -->
        <div class="flex items-center justify-between">
          <span>Theme</span>
          <input
            type="text"
            [value]="theme"
            style="width: 100%; max-width: 70%"
            class="text-content1 bg-backgroundSecondary border border-[var(--border-color)] rounded-md px-3 py-1"
            [disabled]="isDisabled"
          />
        </div>
        <!-- System Language -->
        <div class="flex items-center justify-between">
          <span>System Language</span>
          <input
            type="text"
            value="English"
            style="width: 100%; max-width: 70%"
            class="text-content1 bg-backgroundSecondary border border-[var(--border-color)] rounded-md px-3 py-1"
            [disabled]="isDisabled"
          />
        </div>
        <!-- Email for receiving notifications -->
        <div class="flex items-center justify-between">
          <span class="flex items-center gap-2 text-sm text-content1 mb-1">
            Email for receiving notification
            <!-- Tooltip on hover for edit icon -->
            <div class="relative inline-block group">
              <img
                src="assets/icons/pencil.png"
                class="w-5 cursor-pointer"
                alt="Edit Icon"
                (click)="enableInput()"
                data-ripple
              />
              <div
                class="absolute top-full left-0 mt-2 px-2 py-1 bg-backgroundSecondary text-content1 text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                Edit notification email
              </div>
            </div>
          </span>
        </div>
        <div class="relative" style="width: 100%; max-width: 100%">
          <input
            type="text"
            value="RevUp AI"
            class="text-content1 bg-backgroundSecondary border border-[var(--border-color)] rounded-md px-3 py-1 w-full pr-8"
            [disabled]="isDisabled"
          />
        </div>
      </div>
    </div>

    <!-- Card 3: MFA & Verification Methods -->
    <div class="bg-backgroundPrimary rounded-lg shadow p-6">
      <!-- Enable MFA -->
      <div class="flex items-center justify-between pb-4 border-b">
        <div class="flex items-center gap-1">
          <span class="text-sm font-semibold text-content1">Enable MFA</span>
          <!-- Info Icon with tooltip on hover -->
          <div class="popover popover-hover">
            <div class="popover-trigger my-2">
              <img
                src="../../../assets/icons/info-circle.png"
                alt="Info"
                class="w-4 h-4 mr-2 cursor-pointer"
              />
            </div>
            <div
              class="popover-content popover-top-center min-w-[359px] bg-backgroundPrimary"
            >
              <div class="popover-arrow bg-backgroundPrimary"></div>
              <div class="">
                <div class="font-bold mb-1 text-content1">
                  Multi-factor Authentication (MFA)
                </div>
                <p class="text-sm text-content2">
                  Multi-factor Authentication (MFA) adds an additional layer of
                  security to your account.
                </p>
              </div>
            </div>
          </div>
        </div>
        <label class="flex items-center cursor-pointer">
          <input
            type="checkbox"
            class="sr-only peer"
            checked
            [disabled]="isDisabled"
          />
          <div
            class="w-11 h-6 bg-backgroundSecondary peer-focus:ring-2 peer-focus:ring-primary rounded-full peer-checked:bg-primary relative after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-backgroundPrimary after:border-[var(--border-color)] after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:after:translate-x-full peer-checked:after:border-white"
          ></div>
        </label>
      </div>

      <!-- Verification Methods -->
      <div class="pt-4">
        <h4 class="text-sm font-medium text-content2 mb-2">
          VERIFICATION METHODS
        </h4>
        <div class="space-y-3 flex flex-col gap-y-3">
          <!-- Email -->
          <label class="flex items-center cursor-pointer justify-between">
            <span class="mr-3 text-sm text-content1 font-bold">Email</span>
            <input
              type="checkbox"
              class="sr-only peer"
              checked
              [disabled]="isDisabled"
            />
            <div
              class="w-11 h-6 bg-backgroundSecondary peer-focus:ring-2 peer-focus:ring-primary rounded-full peer-checked:bg-primary relative after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-backgroundPrimary after:border-[var(--border-color)] after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:after:translate-x-full peer-checked:after:border-white"
            ></div>
          </label>
          <!-- SMS -->
          <label class="flex items-center cursor-pointer justify-between">
            <span class="mr-3 text-sm text-content1 font-bold">SMS</span>
            <input
              type="checkbox"
              class="sr-only peer"
              checked
              [disabled]="isDisabled"
            />
            <div
              class="w-11 h-6 bg-backgroundSecondary peer-focus:ring-2 peer-focus:ring-primary rounded-full peer-checked:bg-primary relative after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-backgroundPrimary after:border-[var(--border-color)] after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:after:translate-x-full peer-checked:after:border-white"
            ></div>
          </label>
        </div>
      </div>
    </div>

    <!-- Card 4: Connected Apps -->
    <div
      class="bg-backgroundPrimary rounded-lg shadow p-6 connected-apps-section"
    >
      <div class="flex items-center justify-between pb-4 border-b">
        <h3 class="text-lg font-semibold text-content1">Connected Apps</h3>
        <button
          class="flex items-center gap-1 text-sm border border-[var(--border-color)] px-3 py-1 rounded hover:bg-backgroundSecondary transition"
          (click)="enableInput()"
        >
          <img
            src="../../../assets/icons/edit.png"
            alt="Edit Icon"
            class="w-4 h-4"
          />
          Edit
        </button>
      </div>

      <!-- GitHub Integration -->
      <div class="mt-4 space-y-4">
        <div
          class="flex items-center justify-between pb-3 border-b border-[var(--border-color)]"
        >
          <div class="flex items-center gap-2">
            <!-- GitHub Icon -->
            <div
              class="w-8 h-8 flex items-center justify-center bg-backgroundSecondary rounded-md"
            >
              <i class="ti ti-brand-github text-xl"></i>
            </div>
            <div>
              <h4 class="text-sm font-semibold text-content1">GitHub</h4>
              <p class="text-xs text-content2">
                {{
                  githubConnected
                    ? "Connected to GitHub"
                    : "Connect your GitHub account"
                }}
              </p>
            </div>
          </div>
          <!-- Connect/Disconnect Button -->
          <div class="flex items-center gap-2">
            <button
              *ngIf="githubConnected"
              class="px-3 py-1 text-xs bg-error text-white rounded hover:bg-error/90 transition"
              (click)="disconnectGitHub()"
            >
              Disconnect
            </button>
            <button
              *ngIf="!githubConnected"
              class="px-3 py-1 text-xs bg-primary text-white rounded hover:bg-primary/90 transition cursor-pointer"
              (click)="connectToGitHub()"
            >
              Connect with GitHub
            </button>
            <button
              *ngIf="githubConnected"
              class="px-3 py-1 text-xs bg-backgroundSecondary text-content1 rounded hover:bg-backgroundSecondary/90 transition cursor-pointer"
              (click)="viewGitHubRepositories()"
            >
              View Repositories
            </button>
          </div>
        </div>

        <!-- GitHub Connection Info (only visible when connected) -->
        <div *ngIf="githubConnected" class="pl-10 space-y-3">
          <div class="flex items-center justify-between text-sm">
            <span class="text-content2">Status:</span>
            <span class="px-2 py-0.5 text-xs rounded-full bg-success text-white"
              >Connected via OAuth</span
            >
          </div>

          <!-- GitHub Repositories -->
          <div class="space-y-2">
            <div class="flex items-center justify-between text-sm">
              <span class="text-content2">Repositories:</span>
              <button
                class="text-xs text-primary hover:underline"
                (click)="viewGitHubRepositories()"
              >
                Refresh
              </button>
            </div>

            <!-- Loading indicator -->
            <div *ngIf="loading" class="flex justify-center py-4">
              <div
                class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"
              ></div>
            </div>

            <!-- Repository list -->
            <div
              *ngIf="!loading"
              class="bg-backgroundSecondary p-2 rounded-md border border-[var(--border-color)]"
            >
              <!-- Selected repository -->
              <div
                *ngIf="selectedRepository"
                class="mb-2 p-2 bg-backgroundPrimary rounded-md border border-[var(--border-color)]"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <i class="ti ti-star-filled text-warning mr-1"></i>
                    <span class="text-content1 font-medium">{{
                      selectedRepository
                    }}</span>
                    <span class="text-xs text-content2 ml-2">(Selected)</span>
                  </div>
                </div>
              </div>

              <!-- No repositories message -->
              <div
                *ngIf="githubRepositories.length === 0"
                class="text-xs text-content2 italic p-2"
              >
                No repositories found or not yet loaded.
              </div>

              <!-- Repository list -->
              <ul
                *ngIf="githubRepositories.length > 0"
                class="text-xs space-y-1"
              >
                <li
                  *ngFor="let repo of githubRepositories"
                  class="flex items-center justify-between p-1 hover:bg-backgroundPrimary rounded"
                >
                  <div class="flex items-center">
                    <i class="ti ti-brand-github mr-1"></i>
                    <span class="text-content1">{{ repo }}</span>
                  </div>
                  <div class="flex items-center">
                    <button
                      *ngIf="repo !== selectedRepository"
                      class="text-xs text-primary hover:underline mr-2"
                      (click)="selectRepository(repo)"
                      title="Select this repository"
                    >
                      <i class="ti ti-check"></i>
                    </button>
                    <button
                      class="text-xs text-error hover:underline"
                      (click)="deleteRepository(repo)"
                      title="Unlink this repository"
                    >
                      <i class="ti ti-trash"></i>
                    </button>
                  </div>
                </li>
              </ul>

              <!-- Repository selection button -->
              <div class="mt-2 flex justify-end">
                <label
                  for="repo-select-modal"
                  class="btn btn-xs btn-ghost text-content2"
                >
                  <i class="ti ti-search mr-1"></i> Search Repositories
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Repository Selection Modal -->
      <input class="modal-state" id="repo-select-modal" type="checkbox" />
      <div class="modal w-screen">
        <label class="modal-overlay"></label>
        <div
          class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%] bg-backgroundPrimary"
        >
          <!-- Modal Header -->
          <div class="modal-header">
            <h2 class="text-xl font-medium">Select GitHub Repository</h2>
            <label for="repo-select-modal" class="modal-close-btn"
              ><i class="ti ti-x"></i
            ></label>
          </div>

          <!-- Modal Body -->
          <div class="p-6 space-y-4">
            <!-- Loading indicator -->
            <div *ngIf="loading" class="flex justify-center py-4">
              <div
                class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"
              ></div>
            </div>

            <!-- Repository search -->
            <div class="form-control w-full" *ngIf="!loading">
              <label class="label">
                <span class="label-text text-content2"
                  >Search Repositories</span
                >
              </label>
              <input
                type="text"
                placeholder="Search repositories..."
                class="input input-bordered w-full bg-backgroundSecondary text-content1 border-[var(--border-color)]"
                [(ngModel)]="searchTerm"
                (input)="filterRepositories()"
              />
            </div>

            <!-- Repository list -->
            <div class="form-control w-full" *ngIf="!loading">
              <label class="label">
                <span class="label-text text-content2"
                  >Available Repositories</span
                >
              </label>
              <div
                class="bg-backgroundSecondary p-2 rounded-md border border-[var(--border-color)] max-h-60 overflow-y-auto"
              >
                <div
                  *ngIf="filteredRepositories.length === 0"
                  class="text-xs text-content2 italic p-2"
                >
                  No repositories found matching your search.
                </div>
                <ul
                  *ngIf="filteredRepositories.length > 0"
                  class="text-xs space-y-1"
                >
                  <li
                    *ngFor="let repo of filteredRepositories"
                    class="flex items-center justify-between p-2 hover:bg-backgroundPrimary rounded cursor-pointer"
                    (click)="selectRepository(repo)"
                  >
                    <div class="flex items-center">
                      <i class="ti ti-brand-github mr-1"></i>
                      <span class="text-content1">{{ repo }}</span>
                    </div>
                    <i
                      class="ti ti-check text-success"
                      *ngIf="selectedRepository === repo"
                    ></i>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Modal Footer -->
          <div class="modal-footer">
            <label
              for="repo-select-modal"
              class="btn btn-sm btn-ghost text-content2"
            >
              Cancel
            </label>
          </div>
        </div>
      </div>

      <!-- GitHub Configuration Modal -->
      <input class="modal-state" id="github-modal" type="checkbox" />
      <div class="modal w-screen">
        <label class="modal-overlay"></label>
        <div
          class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%] bg-backgroundPrimary"
        >
          <!-- Modal Header -->
          <div class="modal-header">
            <h2 class="text-xl font-medium">
              {{
                githubConnected
                  ? "Edit GitHub Configuration"
                  : "Connect to GitHub"
              }}
            </h2>
            <label for="github-modal" class="modal-close-btn"
              ><i class="ti ti-x"></i
            ></label>
          </div>

          <!-- Modal Body -->
          <div class="p-6 space-y-4">
            <!-- Repository URL -->
            <div class="form-control w-full">
              <label class="label">
                <span class="label-text text-content2">Repository URL</span>
              </label>
              <input
                type="text"
                placeholder="https://github.com/username/repository"
                class="input input-bordered w-full bg-backgroundSecondary text-content1 border-[var(--border-color)]"
                [(ngModel)]="githubConfig.repositoryUrl"
              />
            </div>

            <!-- Branch -->
            <div class="form-control w-full">
              <label class="label">
                <span class="label-text text-content2">Default Branch</span>
              </label>
              <input
                type="text"
                placeholder="main"
                class="input input-bordered w-full bg-backgroundSecondary text-content1 border-[var(--border-color)]"
                [(ngModel)]="githubConfig.branch"
              />
            </div>

            <!-- OAuth Notice -->
            <div class="form-control w-full">
              <div
                class="bg-backgroundSecondary p-3 rounded-md border border-[var(--border-color)]"
              >
                <p class="text-sm text-content1">
                  <i class="ti ti-info-circle mr-2 text-primary"></i>
                  You'll be redirected to GitHub to authorize access to your
                  repositories.
                </p>
                <p class="text-xs text-content2 mt-2">
                  This secure OAuth flow allows RevUp.AI to access your GitHub
                  repositories without storing your credentials.
                </p>
              </div>
            </div>
          </div>

          <!-- Modal Footer -->
          <div class="modal-footer">
            <label
              for="github-modal"
              class="btn btn-sm btn-ghost text-content2"
            >
              Cancel
            </label>
            <button
              class="btn btn-sm btn-primary text-white"
              (click)="saveGitHubConfig()"
            >
              {{ githubConnected ? "Update" : "Connect" }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

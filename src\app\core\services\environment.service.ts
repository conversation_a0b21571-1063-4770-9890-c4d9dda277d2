import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class EnvironmentService {
  private env = environment;

  get apiBaseUrl(): string {
    // In production, ensure we're using HTTPS
    if (this.env.production && this.env.apiBaseUrl.startsWith('http:')) {
      return this.env.apiBaseUrl.replace('http:', 'https:');
    }
    return this.env.apiBaseUrl;
  }

  get wsUrl(): string {
    // Return the WebSocket URL from environment
    return this.env.wsUrl;
  }

  get isProduction(): boolean {
    return this.env.production;
  }
}

import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DocHubComponent } from './doc-hub.component';
import { RoleGuard } from 'src/app/core/guards/role.guard';

const routes: Routes = [
  {
    path: '',
    component: DocHubComponent,
    canActivate: [RoleGuard],
    data: {
      permissions: ['access_document_vault', 'download_document'],
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DocHubRoutingModule {}

import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { EnvironmentService } from '../services/environment.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, tap, of, map, catchError } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  constructor(
    private router: Router,
    private http: HttpClient,
    private envService: EnvironmentService
  ) {
    // Get the API URL from the environment service
    // The environment service already handles HTTPS in production
    this.apiUrl = this.envService.apiBaseUrl;

    // Log the URL in production for debugging
    if (this.envService.isProduction) {
      console.log('Auth Service API URL:', this.apiUrl);
    }

    // Check token validity on startup
    this.validateTokenOnStartup();
  }
  private apiUrl: string;
  private timeoutWarning: any;
  private sessionTimeout: any;
  private warningDuration = 15 * 1000; // 15 seconds warning duration
  private idleDuration = 600 * 1000; // 10 minutes of idle time
  private lastActivity: number = Date.now(); // Track last activity timestamp

  signup(fullName: string, email: string, password: string): Observable<any> {
    const payload = { full_name: fullName, email, password };
    return this.http.post(`${this.apiUrl}/auth/signup`, payload);
  }

  login(email: string, password: string): Observable<any> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded',
      Accept: 'application/json',
    });

    const payload = new URLSearchParams();
    payload.set('username', email);
    payload.set('password', password);
    payload.set('scope', '');
    payload.set('client_id', '');
    payload.set('client_secret', '');

    return this.http
      .post<any>(`${this.apiUrl}/auth/login`, payload.toString(), { headers })

      .pipe(
        tap((response: any) => {
          if (response.access_token) {
            localStorage.setItem('token', response.access_token);
            this.router.navigate(['/dashboard']);
            // Fetch user details and role information
            this.getUserDetails().subscribe({
              next: (user) => {
                console.log('User Details Fetched:', user);

                // After getting basic user details, fetch detailed role information
                this.getUserRoleDetails().subscribe({
                  next: (roleDetails) =>
                    console.log('User Role Details Fetched:', roleDetails),
                  error: (err) =>
                    console.error('Error fetching user role details:', err),
                });
              },
              error: (err) =>
                console.error('Error fetching user details:', err),
            });
          }
        })
      );
  }

  getUserDetails(field?: keyof any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) return new Observable<any>((observer) => observer.next(null));

    const headers = new HttpHeaders({
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    });

    return this.http.get<any>(`${this.apiUrl}/auth/me`, { headers }).pipe(
      map((response) => {
        if (response) {
          // Handle permissions format - convert array to object if needed
          if (response.role && Array.isArray(response.role.permissions)) {
            console.log('Converting permissions array to object format');
            const permissionsObj: Record<string, boolean> = {};
            response.role.permissions.forEach((permission: string) => {
              permissionsObj[permission] = true;
            });
            response.role.permissions = permissionsObj;
          }

          localStorage.setItem('userDetails', JSON.stringify(response)); // Store full user details
          if (response.id) {
            localStorage.setItem('user_id', response.id); // Store user ID separately
          }
          if (response.organization_id) {
            localStorage.setItem('organization_id', response.organization_id); // Store organization ID separately
          }
        }
        return response;
      }),
      // Transform the response: return full object or only requested field
      field ? map((response) => response[field]) : map((response) => response)
    );
  }

  // login(email: string, password: string): boolean {
  //   // Simulating authentication (Replace with API call in the future)
  //   if (email === '<EMAIL>' && password === 'password') {
  //     localStorage.setItem('token', 'secure-user-token'); // Store a token
  //     this.router.navigate(['/dashboard']); // Redirect to dashboard
  //     return true;
  //   }
  //   return false;
  // }

  logout() {
    localStorage.clear(); // Clears all local storage
    this.router.navigate(['/']); // Redirect to login or home
  }

  isLoggedIn(): boolean {
    return localStorage.getItem('token') !== null; // Check if user is logged in
  }

  resetTimeout() {
    // Update last activity timestamp
    this.lastActivity = Date.now();

    // Clear existing timeouts
    clearTimeout(this.timeoutWarning);
    clearTimeout(this.sessionTimeout);

    // Set new timeout for session warning
    this.timeoutWarning = setTimeout(() => {
      this.showSessionWarning();
    }, this.idleDuration);
  }

  showSessionWarning() {
    // Notify the app that the warning should be displayed
    window.dispatchEvent(new Event('session-warning'));

    this.sessionTimeout = setTimeout(() => {
      this.logout(); // Log out user if they don’t interact
    }, this.warningDuration);
  }

  extendSession() {
    // Update the session expiry time when the user extends their session
    const newExpiry = Date.now() + 24 * 60 * 60 * 1000;
    localStorage.setItem('sessionExpiry', newExpiry.toString());

    // Reset the timeout
    this.resetTimeout();
  }

  /**
   * Validates the token when the application starts
   * This prevents automatic logout if the token is still valid
   * Also validates stored user_id and organization_id
   */
  validateTokenOnStartup() {
    const token = localStorage.getItem('token');
    if (!token) {
      // No token, no need to validate
      return;
    }

    // Check if we have a stored session expiry time
    const sessionExpiry = localStorage.getItem('sessionExpiry');
    if (sessionExpiry) {
      const expiryTime = parseInt(sessionExpiry, 10);
      // If the session is still valid, start the timeout
      if (expiryTime > Date.now()) {
        console.log('Valid session found, starting timeout');
        this.resetTimeout();

        // Even if session is valid, validate the stored IDs
        this.validateStoredIds();
      } else {
        console.log('Session expired, clearing local storage');
        // Session expired, clear storage
        localStorage.clear();
      }
    } else {
      // No expiry time stored, validate token with server
      this.validateToken(token).subscribe({
        next: (isValid) => {
          if (isValid) {
            console.log('Token validated with server, starting timeout');
            // Set a new expiry time (e.g., 24 hours from now)
            const newExpiry = Date.now() + 24 * 60 * 60 * 1000;
            localStorage.setItem('sessionExpiry', newExpiry.toString());
            this.resetTimeout();

            // Also validate the stored IDs
            this.validateStoredIds();
          } else {
            console.log('Invalid token, logging out');
            this.logout();
          }
        },
        error: () => {
          // Error validating token, assume it's invalid
          console.error('Error validating token, logging out');
          this.logout();
        },
      });
    }
  }

  /**
   * Validates the token with the server
   * @param token The token to validate
   * @returns Observable<boolean> indicating if the token is valid
   */
  validateToken(token: string): Observable<boolean> {
    const headers = new HttpHeaders({
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    });

    return this.http.get<any>(`${this.apiUrl}/auth/me`, { headers }).pipe(
      map((response) => {
        // If we get a valid response, the token is valid
        return !!response;
      }),
      catchError(() => {
        // If there's an error, the token is invalid
        return of(false);
      })
    );
  }

  /**
   * Validates the stored user_id and organization_id
   * Refreshes them from the server if they're invalid or missing
   */
  validateStoredIds(): void {
    const token = localStorage.getItem('token');
    if (!token) {
      return;
    }

    const headers = new HttpHeaders({
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    });

    // Get user details to validate and refresh IDs
    this.http
      .get<any>(`${this.apiUrl}/auth/me`, { headers })
      .pipe(
        catchError((error) => {
          console.error('Error validating stored IDs:', error);
          return of(null);
        })
      )
      .subscribe((response) => {
        if (response) {
          const storedUserId = localStorage.getItem('user_id');
          const storedOrgId = localStorage.getItem('organization_id');

          // Validate user_id
          if (response.id && (!storedUserId || storedUserId !== response.id)) {
            console.log(
              'Updating stored user_id with correct value from server'
            );
            localStorage.setItem('user_id', response.id);
          }

          // Validate organization_id
          if (
            response.organization_id &&
            (!storedOrgId || storedOrgId !== response.organization_id)
          ) {
            console.log(
              'Updating stored organization_id with correct value from server'
            );
            localStorage.setItem('organization_id', response.organization_id);
          }
        }
      });
  }

  /**
   * Get the user's role information
   * @returns Observable with the user's role information
   */
  getUserRole(): Observable<any> {
    console.log('getUserRole called');

    return this.getUserDetails().pipe(
      map((userDetails) => {
        if (!userDetails) {
          console.log('No user details available');
          return null;
        }

        console.log(
          'Processing user details for role information:',
          userDetails
        );

        // Extract role information from user details
        const roleInfo = {
          roleId:
            userDetails.role_id ||
            (userDetails.role ? userDetails.role.id : null),
          roleName: userDetails.role
            ? userDetails.role.name || userDetails.role.role
            : null,
          isSuperUser: userDetails.is_superuser || false,
          isAdmin: userDetails.role
            ? (userDetails.role.name || '').toLowerCase() === 'admin' ||
              (userDetails.role.role || '').toLowerCase() === 'admin'
            : false,
          isOrgAdmin: userDetails.role
            ? userDetails.role.name === 'Admin' ||
              userDetails.role.role === 'Admin'
            : false,
          organizationId: userDetails.organization_id || null,
        };

        console.log('Extracted role information:', roleInfo);

        return roleInfo;
      }),
      catchError((error) => {
        console.error('Error getting user role:', error);
        return of(null);
      })
    );
  }

  /**
   * Get permissions for the current user
   * This method uses the user details and role information already returned by the API
   * @returns Observable with the user's permissions
   */
  getUserPermissions(): Observable<string[]> {
    return this.getUserDetails().pipe(
      map((userDetails) => {
        if (!userDetails) return [];

        // If user is a superuser, they have all permissions
        if (userDetails.is_superuser) {
          return ['all_permissions']; // Special marker for superuser
        }

        // Extract permissions from role
        if (userDetails.role && userDetails.role.permissions) {
          // Handle both array and object formats
          if (Array.isArray(userDetails.role.permissions)) {
            return userDetails.role.permissions;
          } else {
            return Object.entries(userDetails.role.permissions)
              .filter(([_, value]) => value === true)
              .map(([key, _]) => key);
          }
        }

        return [];
      }),
      catchError((error) => {
        console.error('Error getting user permissions:', error);
        return of([]);
      })
    );
  }

  /**
   * Check if the current user has a specific permission
   * @param permission Permission to check
   * @returns Observable<boolean> indicating if the user has the permission
   */
  hasPermission(permission: string): Observable<boolean> {
    return this.getUserDetails().pipe(
      map((userDetails) => {
        if (!userDetails) return false;

        // Superusers have all permissions
        if (userDetails.is_superuser) {
          return true;
        }

        // Check if the user's role has the specific permission
        if (userDetails.role && userDetails.role.permissions) {
          // Handle both array and object formats
          if (Array.isArray(userDetails.role.permissions)) {
            return userDetails.role.permissions.includes(permission);
          } else {
            return userDetails.role.permissions[permission] === true;
          }
        }

        return false;
      }),
      catchError((error) => {
        console.error(`Error checking permission ${permission}:`, error);
        return of(false);
      })
    );
  }

  /**
   * Get detailed user role information from the dedicated endpoint
   * This method fetches user role details including permissions and organization info
   * @returns Observable with the user's detailed role information
   */
  getUserRoleDetails(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) return of(null);

    const headers = new HttpHeaders({
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    });

    return this.http
      .get<any>(`${this.apiUrl}/auth/user-role`, { headers })
      .pipe(
        tap((response) => {
          console.log('User role details fetched:', response);

          // Store the role details in localStorage for easy access
          if (response) {
            localStorage.setItem('userRoleDetails', JSON.stringify(response));
          }
        }),
        catchError((error) => {
          console.error('Error fetching user role details:', error);
          return of(null);
        })
      );
  }
}

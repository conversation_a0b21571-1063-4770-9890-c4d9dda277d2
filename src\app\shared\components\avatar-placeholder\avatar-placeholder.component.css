.avatar-placeholder {
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Add compatibility with the avatar-ring-primary class */
:host ::ng-deep .avatar-ring-primary {
  border: 2px solid #9400ff;
  padding: 2px;
}

.avatar-sm {
  width: 32px;
  height: 32px;
}

.avatar-md {
  width: 48px;
  height: 48px;
}

.avatar-lg {
  width: 64px;
  height: 64px;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-initials {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.avatar-sm .avatar-initials {
  font-size: 14px;
}

.avatar-md .avatar-initials {
  font-size: 18px;
}

.avatar-lg .avatar-initials {
  font-size: 24px;
}

import {
  Component,
  OnInit,
  AfterViewInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { DatabaseService } from 'src/app/core/services/database/database.service';
import { AuthService } from 'src/app/core/auth/auth.service';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { Location } from '@angular/common';

interface Document {
  procedure_name: string;
  document_base64: string;
  database_name: string;
  object_type: string;
}

@Component({
  selector: 'app-doc-hub',
  templateUrl: './doc-hub.component.html',
  styleUrls: ['./doc-hub.component.css'],
})
export class DocHubComponent implements OnInit {
  @ViewChild('statusTemplate') statusTemplate!: TemplateRef<any>;
  @ViewChild('actionTemplate') actionTemplate!: TemplateRef<any>;

  // Filter and search properties
  searchQuery: string = '';
  selectedType: string = '';
  types: string[] = [];

  // Documents data
  documents: Document[] = [];
  loading: boolean = false;
  error: string | null = null;

  // Pagination
  currentPage: number = 1;
  pageSize: number = 5;
  pageSizeOptions: number[] = [5, 10, 15];

  customTemplates: {
    status: TemplateRef<any> | null;
    action: TemplateRef<any> | null;
  } = {
    status: null,
    action: null,
  };

  // User permissions
  isSuperUser: boolean = false;
  isAdmin: boolean = false;
  userPermissions: string[] = [];
  hasDocumentAccess: boolean = false;
  canDownloadDocuments: boolean = false;
  permissionsLoaded: boolean = false;

  constructor(
    private databaseService: DatabaseService,
    private authService: AuthService,
    private location: Location
  ) {}

  ngOnInit(): void {
    // Load permissions first, then documents will be loaded after permissions are checked
    this.loadUserPermissions();
  }

  /**
   * Load user permissions and role information
   */
  loadUserPermissions(): void {
    this.authService.getUserRoleDetails().subscribe({
      next: (roleDetails) => {
        if (roleDetails) {
          // Set superuser status
          this.isSuperUser = roleDetails.is_superuser || false;

          // Check for Admin role
          const roleName =
            roleDetails.role?.name || roleDetails.role?.role || '';
          this.isAdmin = roleName.toLowerCase() === 'admin';

          // Get permissions
          if (roleDetails.role && roleDetails.role.permissions) {
            // Handle permissions in array format (from user-role endpoint)
            if (Array.isArray(roleDetails.role.permissions)) {
              this.userPermissions = roleDetails.role.permissions;
            }
            // Handle permissions in object format (for backward compatibility)
            else {
              this.userPermissions = Object.entries(
                roleDetails.role.permissions
              )
                .filter(([_, value]) => value === true)
                .map(([key, _]) => key);
            }
          }

          // Check specific permissions
          this.hasDocumentAccess =
            this.isSuperUser ||
            this.isAdmin ||
            this.userPermissions.includes('access_document_vault');

          this.canDownloadDocuments =
            this.isSuperUser ||
            this.isAdmin ||
            this.userPermissions.includes('download_document');

          // Mark permissions as loaded
          this.permissionsLoaded = true;

          console.log('User permissions loaded:', {
            isSuperUser: this.isSuperUser,
            isAdmin: this.isAdmin,
            permissions: this.userPermissions,
            hasDocumentAccess: this.hasDocumentAccess,
            canDownloadDocuments: this.canDownloadDocuments,
            permissionsLoaded: this.permissionsLoaded,
          });

          // Now that permissions are loaded, load documents if user has access
          this.loadDocuments();
        }
      },
      error: (err) => {
        console.error('Error loading user permissions:', err);
        // Fallback to getUserDetails if getUserRoleDetails fails
        this.fallbackToUserDetails();
      },
    });
  }

  /**
   * Fallback to getUserDetails if getUserRoleDetails fails
   */
  fallbackToUserDetails(): void {
    this.authService.getUserDetails().subscribe({
      next: (user) => {
        if (user) {
          // Set superuser status
          this.isSuperUser = user.is_superuser || false;

          // Check for Admin role
          const roleName = user.role?.name || '';
          this.isAdmin = roleName.toLowerCase() === 'admin';

          // Get permissions
          if (user.role && user.role.permissions) {
            // Handle permissions in array format
            if (Array.isArray(user.role.permissions)) {
              this.userPermissions = user.role.permissions;
            }
            // Handle permissions in object format
            else {
              this.userPermissions = Object.entries(user.role.permissions)
                .filter(([_, value]) => value === true)
                .map(([key, _]) => key);
            }
          }

          // Check specific permissions
          this.hasDocumentAccess =
            this.isSuperUser ||
            this.isAdmin ||
            this.userPermissions.includes('access_document_vault');

          this.canDownloadDocuments =
            this.isSuperUser ||
            this.isAdmin ||
            this.userPermissions.includes('download_document');

          // Mark permissions as loaded
          this.permissionsLoaded = true;

          console.log('User permissions loaded (fallback):', {
            isSuperUser: this.isSuperUser,
            isAdmin: this.isAdmin,
            permissions: this.userPermissions,
            hasDocumentAccess: this.hasDocumentAccess,
            canDownloadDocuments: this.canDownloadDocuments,
            permissionsLoaded: this.permissionsLoaded,
          });

          // Now that permissions are loaded, load documents if user has access
          this.loadDocuments();
        }
      },
      error: (err) => {
        console.error('Error loading user details:', err);
      },
    });
  }

  ngAfterViewInit(): void {
    this.customTemplates = {
      status: this.statusTemplate,
      action: this.actionTemplate,
    };
  }

  loadDocuments(): void {
    // Only load documents if user has access
    if (!this.permissionsLoaded) {
      // If permissions aren't loaded yet, wait for them
      setTimeout(() => this.loadDocuments(), 500);
      return;
    }

    // Skip loading if user doesn't have access
    if (!this.hasDocumentAccess) {
      this.loading = false;
      return;
    }

    this.loading = true;
    this.error = null;

    this.databaseService.getDocumentsByUserAndDatabase().subscribe({
      next: (response: any) => {
        this.documents = response.documents || [];
        this.loading = false;

        // Extract unique object types for the filter dropdown
        const uniqueTypes = [
          ...new Set(this.documents.map((doc) => doc.object_type)),
        ];
        this.types = uniqueTypes;
      },
      error: (err: any) => {
        console.error('Error loading documents:', err);
        this.error = 'Failed to load documents. Please try again.';
        this.loading = false;
      },
    });
  }

  /**
   * Download a document if user has permission
   * @param document The document to download
   */
  downloadDocument(document: Document): void {
    // Check if user has permission to download documents
    if (!this.canDownloadDocuments) {
      console.warn('User does not have permission to download documents');
      return;
    }

    if (document.document_base64) {
      this.databaseService.downloadPdf(
        document.document_base64,
        document.procedure_name
      );
    } else {
      console.error('No document base64 data available for download');
    }
  }

  /**
   * Check if the user has a specific permission
   * @param permission The permission to check
   * @returns True if the user has the permission, false otherwise
   */
  hasPermission(permission: string): boolean {
    return (
      this.isSuperUser ||
      this.isAdmin ||
      this.userPermissions.includes(permission)
    );
  }

  get filteredTableData() {
    const searchLower = this.searchQuery.toLowerCase();
    return this.documents.filter((item) => {
      const matchesSearch =
        item.procedure_name.toLowerCase().includes(searchLower) ||
        item.database_name.toLowerCase().includes(searchLower);

      const matchesType = this.selectedType
        ? item.object_type === this.selectedType
        : true;

      return matchesSearch && matchesType;
    });
  }

  get paginatedTableData() {
    const data = this.filteredTableData;
    const start = (this.currentPage - 1) * this.pageSize;
    return data.slice(start, start + this.pageSize);
  }

  get totalEntries() {
    return this.filteredTableData.length;
  }

  get currentPageStart() {
    return this.totalEntries === 0
      ? 0
      : (this.currentPage - 1) * this.pageSize + 1;
  }

  get currentPageEnd() {
    return Math.min(this.currentPage * this.pageSize, this.totalEntries);
  }

  get totalPages() {
    return Math.ceil(this.totalEntries / this.pageSize);
  }

  changePageSize(event: Event) {
    const target = event.target as HTMLSelectElement;
    this.pageSize = parseInt(target.value, 10);
    this.currentPage = 1;
  }

  prevPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  goToPage(page: number) {
    this.currentPage = page;
  }

  refreshDocuments() {
    this.loadDocuments();
  }

  /**
   * Navigate back to the previous page
   */
  goBack(): void {
    this.location.back();
  }
}

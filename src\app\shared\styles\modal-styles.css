/* Global Modal Styles based on prompt-vault-modal */

/* Modal content container */
.modal-content {
  max-height: 90vh;
  display: flex !important;
  flex-direction: column !important;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  background-color: var(--backgroundPrimary);
  border: 1px solid var(--border-color);
  /* Preserve original width settings from each modal */
  padding: 0 !important;
}

/* Modal scrollable content area */
.modal-content > div:nth-child(2) {
  overflow-y: auto;
  max-height: calc(90vh - 120px); /* Adjust based on header/footer height */
  flex: 1;
}

/* Modal header styling - slightly darker than table header */
.modal-header {
  background-color: rgba(148, 0, 255, 0.05) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.12),
    rgba(44, 206, 138, 0.05)
  ) !important;
  padding: 0.75rem 1rem !important;
  border-bottom: 1px solid var(--border-color) !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 10 !important;
}

/* Modal header text styling */
.modal-header h2,
.modal-header h3 {
  color: var(--content1) !important;
  font-weight: 600 !important;
  margin: 0 !important;
}

/* Modal close button */
.modal-close-btn {
  transition: all 0.3s ease !important;
  border-radius: 50% !important;
  width: 2rem !important;
  height: 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  background: transparent !important;
  border: none !important;
  color: var(--content1) !important;
}

.modal-close-btn:hover {
  background-color: var(--backgroundSecondary) !important;
  transform: rotate(90deg) !important;
}

/* Modal footer styling */
.modal-footer {
  display: flex !important;
  justify-content: flex-end !important;
  gap: 0.75rem !important;
  padding: 1rem !important;
  border-top: 1px solid var(--border-color) !important;
  position: sticky !important;
  bottom: 0 !important;
  z-index: 10 !important;
  background: linear-gradient(
    to bottom,
    transparent,
    var(--backgroundPrimary) 15%
  ) !important;
}

/* Modal buttons */
.modal-footer button,
.modal-footer label {
  height: 2.25rem !important;
  min-height: 0 !important;
  transition: all 0.3s ease !important;
}

.modal-footer button:hover,
.modal-footer label:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
}

/* Modal overlay */
.modal-overlay {
  background-color: rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(2px) !important;
}

/* Dark mode modal styling */
[data-theme="dark"] .modal-content {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4) !important;
  border: 1px solid rgba(75, 75, 75, 0.5) !important;
}

/* Dark mode modal header styling to match table header */
[data-theme="dark"] .modal-header {
  background-color: rgba(148, 0, 255, 0.05) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.12),
    rgba(44, 206, 138, 0.05)
  ) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Dark mode modal close button */
[data-theme="dark"] .modal-close-btn:hover {
  background-color: rgba(148, 0, 255, 0.15) !important;
}

/* Modal animation */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-content {
  animation: modalFadeIn 0.3s ease-out forwards;
}

/* Modal form controls */
.modal-content .form-field {
  margin-bottom: 1rem !important;
}

.modal-content .form-label {
  display: block !important;
  margin-bottom: 0.5rem !important;
  color: var(--content1) !important;
  font-weight: 500 !important;
}

.modal-content .input,
.modal-content .select,
.modal-content .textarea {
  width: 100% !important;
  background-color: var(--backgroundPrimary) !important;
  color: var(--content1) !important;
  border: 1px solid var(--border-color) !important;
  transition: all 0.3s ease !important;
}

.modal-content .input:focus,
.modal-content .select:focus,
.modal-content .textarea:focus {
  border-color: var(--primary) !important;
  box-shadow: 0 0 0 2px rgba(148, 0, 255, 0.1) !important;
  outline: none !important;
}

/* Confirmation modal specific styling */
.confirmation-modal {
  max-width: 24rem !important;
}

.confirmation-modal .modal-body {
  padding: 1rem !important;
}

.confirmation-modal p {
  color: var(--content1) !important;
  margin-bottom: 1rem !important;
}

/* Delete confirmation styling */
.delete-confirmation .btn-error {
  background-color: var(--error) !important;
  color: white !important;
}

.delete-confirmation .btn-error:hover {
  background-color: var(--error-focus) !important;
}

/* Table row animation */
@keyframes fadeInRow {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Selected row styling */
.selected-row {
  background-color: rgba(var(--primary-rgb), 0.1) !important;
  border-left: 3px solid var(--primary) !important;
}

/* Enhanced table styling */
.enhanced-table {
  border-radius: 0.5rem;
  overflow: hidden;
}

.enhanced-table table {
  border-collapse: separate;
  border-spacing: 0;
}

.enhanced-table th {
  background-color: rgba(148, 0, 255, 0.05) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.12),
    rgba(44, 206, 138, 0.05)
  ) !important;
  padding: 0.75rem 1rem !important;
  font-weight: 600 !important;
}

.enhanced-table th span {
  border: none !important;
}

.enhanced-table td {
  padding: 0.75rem 1rem !important;
}

.enhanced-table tbody tr:hover {
  background-color: rgba(var(--primary-rgb), 0.05) !important;
}

/* Dependency chart selected row styling - matches hover state */
tr.dependency-selected-row {
  background-color: rgba(var(--primary-rgb), 0.05) !important;
  border-left: 3px solid transparent !important;
}

/* Make sure the enhanced table doesn't override our dependency-selected-row */
.enhanced-table tbody tr.dependency-selected-row {
  background-color: rgba(var(--primary-rgb), 0.05) !important;
}

/* Use RippleUI theme tokens */
:root {
  --carousel-border: var(--border-color, rgba(226, 226, 226, 1));
  --carousel-border-hover: var(--border-color-hover, rgba(200, 200, 200, 1));
  --carousel-shadow: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] {
  --carousel-border: var(--border-color, rgba(52, 52, 52, 1));
  --carousel-border-hover: var(--border-color-hover, rgba(75, 75, 75, 1));
  --carousel-shadow: rgba(0, 0, 0, 0.15);
}

.carousel-container {
  position: relative;
  margin: auto;
  text-align: center;
  width: 100%;
  border-radius: 12px;
  padding: 15px;
  transition: all 0.3s ease-in-out;
  border: 1px solid var(--carousel-border);
  box-shadow: 0 2px 8px var(--carousel-shadow);
}

.carousel-container:hover {
  border-color: var(--carousel-border-hover, var(--carousel-border));
  box-shadow: 0 4px 12px var(--carousel-shadow);
}

.carousel-container img {
  display: none;
  transition: all 0.5s ease-in-out;
  transform: scale(0.95);
  opacity: 0;
}

.carousel-container img.image-active {
  display: block;
  animation: fadeInScale 0.5s forwards;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Carousel text animations */
.carousel-text {
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.5s ease-out;
}

.carousel-text.active {
  opacity: 1;
  transform: translateY(0);
  animation: fadeInUp 0.5s forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dot indicators */
.carousel-dot-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}

.dot {
  cursor: pointer;
  height: 8px;
  width: 8px;
  margin: 0 5px;
  border-radius: 50%;
  display: inline-block;
  transition: all 0.3s ease;
  background-color: var(--primary, #9400ff);
  opacity: 0.5;
}

.active,
.dot:hover {
  opacity: 1;
  transform: scale(1.2);
  box-shadow: 0 0 5px var(--primary, rgba(148, 0, 255, 0.5));
}

/* Dark mode text color adjustments */
[data-theme="dark"] .carousel-text {
  color: var(--content1, #ffffff);
}

/* Dark mode border adjustments */
[data-theme="dark"] .border-b {
  border-color: var(--border-color, rgba(52, 52, 52, 1)) !important;
}

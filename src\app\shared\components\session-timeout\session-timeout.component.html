<!-- Mo<PERSON> Trigger (Hidden, will be triggered via TypeScript) -->
<input
  class="modal-state"
  id="session-timeout-modal"
  type="checkbox"
  [checked]="showModal"
/>

<!-- Modal Structure -->
<div class="modal">
  <label class="modal-overlay"></label>
  <div
    class="modal-content flex flex-col gap-5 p-0 max-w-md min-w-[40%] bg-backgroundPrimary rounded-lg shadow-lg"
  >
    <!-- Modal Header -->
    <div class="modal-header">
      <h2 class="text-xl font-medium">Session Expiring Soon</h2>
      <button class="modal-close-btn" (click)="closeModal()">
        <i class="ti ti-x"></i>
      </button>
    </div>

    <!-- Modal Body -->
    <div class="p-5">
      <!-- Countdown Timer -->
      <div class="flex flex-col items-center mb-4">
        <div
          class="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center mb-3"
        >
          <span class="text-2xl font-bold text-primary">{{ countdown }}</span>
        </div>
        <p class="text-content1 text-center">
          Your session is about to expire due to inactivity.
          <br />You will be logged out in
          <span class="font-semibold text-primary">{{ countdown }}</span>
          seconds.
        </p>
      </div>

      <!-- Progress Bar -->
      <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
        <div
          class="bg-primary h-2.5 rounded-full"
          [style.width]="(countdown / 15) * 100 + '%'"
        ></div>
      </div>
    </div>

    <!-- Modal Footer -->
    <div class="modal-footer">
      <button class="btn btn-outline btn-sm" (click)="logout()">
        Logout Now
      </button>
      <button class="btn btn-primary btn-sm" (click)="stayLoggedIn()">
        Stay Logged In
      </button>
    </div>
  </div>
</div>

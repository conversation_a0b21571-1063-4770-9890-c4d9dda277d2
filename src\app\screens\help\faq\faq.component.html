<div class="container mx-auto py-8">
  <div class="text-center mb-8">
    <h1 class="text-3xl font-bold mb-4">Frequently Asked Questions</h1>
    <p class="mx-auto">
      Find answers to common questions about RevMigrate. Can't find what you're
      looking for?
      <a routerLink="/help/request" class="text-primary hover:underline"
        >Contact our support team</a
      >.
    </p>
  </div>

  <!-- Search Bar -->
  <div class="mb-8 max-w-4xl mx-auto">
    <div class="form-control relative w-full">
      <input
        class="input input-lg max-w-full"
        type="text"
        placeholder="Search for questions..."
        (input)="search($event)"
      />

      <span class="absolute inset-y-0 right-4 inline-flex items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-content3"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
          />
        </svg>
      </span>
    </div>
  </div>

  <!-- Category Filters -->
  <div class="flex flex-wrap justify-start gap-3 mb-8 max-w-4xl mx-auto">
    <button
      *ngFor="let category of categories"
      (click)="filterByCategory(category.name)"
      [class.bg-primary]="activeCategory === category.name"
      [class.text-white]="activeCategory === category.name"
      [class.bg-backgroundPrimary]="activeCategory !== category.name"
      class="py-2 px-4 rounded-full flex items-center gap-2 transition-colors"
    >
      <i [class]="category.icon"></i>
      <span class="capitalize">{{ category.name }}</span>
    </button>
  </div>

  <!-- FAQ Items -->
  <div class="max-w-4xl mx-auto">
    <div *ngIf="filteredFaqs.length === 0" class="text-center p-8">
      <i class="ti ti-search-off text-5xl mb-4"></i>
      <p class="text-xl">No results found for your search.</p>
      <p class="mt-2 text-content3">
        Try different keywords or browse all categories.
      </p>
    </div>

    <div *ngFor="let faq of filteredFaqs" class="mb-4">
      <div
        class="rounded-lg bg-backgroundSecondary shadow-md overflow-hidden"
        [class.border-l-4]="faq.isOpen"
        [class.border-primary]="faq.isOpen"
      >
        <div
          class="p-4 flex justify-between items-center cursor-pointer"
          (click)="toggleFaq(faq)"
        >
          <h3 class="text-lg font-semibold">{{ faq.question }}</h3>
          <i
            class="ti"
            [ngClass]="faq.isOpen ? 'ti-chevron-up' : 'ti-chevron-down'"
          ></i>
        </div>

        <div *ngIf="faq.isOpen" class="p-4 pt-0">
          <div class="divider divider-horizontal w-full mt-0"></div>
          <p class="text-content2">{{ faq.answer }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Help Section -->
  <div class="text-center mt-12">
    <h3 class="text-xl font-semibold mb-3">Still have questions?</h3>
    <p class="mb-4">
      Our support team is here to help you with any questions or concerns.
    </p>
    <a routerLink="/help/request-form" class="btn btn-primary px-6">
      Contact Support
    </a>
  </div>
</div>

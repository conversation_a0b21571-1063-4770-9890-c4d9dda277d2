import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../core/auth/auth.service';
import {
  trigger,
  transition,
  style,
  animate,
  state,
} from '@angular/animations';

@Component({
  selector: 'app-chat-bot',
  templateUrl: './chat-bot.component.html',
  styleUrls: ['./chat-bot.component.css'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms ease-in', style({ opacity: 1 })),
      ]),
    ]),
    trigger('slideInOut', [
      state(
        'in',
        style({
          transform: 'translateY(0)',
          opacity: 1,
        })
      ),
      state(
        'out',
        style({
          transform: 'translateY(20px)',
          opacity: 0,
        })
      ),
      transition('in => out', animate('300ms ease-out')),
      transition('out => in', animate('300ms ease-in')),
    ]),
  ],
})
export class ChatBotComponent implements OnInit {
  userDetails: any = {};

  // Simplified state: either fully open or peeking
  isChatbotOpen = false;

  chatbotState: 'loading' | 'greeting' | 'chat' = 'loading';

  constructor(private authService: AuthService) {}

  ngOnInit(): void {
    // Call the existing getUserDetails() method to fetch user details
    this.authService.getUserDetails().subscribe({
      next: (response) => {
        if (response) {
          this.userDetails = response;
        }
      },
      error: (err) => {
        console.error('Error fetching user details:', err);
      },
    });
  }

  // Toggle between open and closed states with animation
  toggleChatbot() {
    this.isChatbotOpen = !this.isChatbotOpen;

    if (this.isChatbotOpen) {
      this.chatbotState = 'loading';
      // Add a slight delay for the loading animation
      setTimeout(() => (this.chatbotState = 'greeting'), 1000);
    }
  }

  openChat() {
    this.chatbotState = 'chat';
  }
}

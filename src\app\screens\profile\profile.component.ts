import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../core/auth/auth.service';
import { ThemeService } from '../../core/theme/theme.service';
import {
  ConnectedAppsService,
  GitHubConfig,
} from '../../core/services/connected-apps/connected-apps.service';
import { forkJoin, catchError, of } from 'rxjs';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css'],
})
export class ProfileComponent implements OnInit {
  userDetails: any = {};
  userRoleDetails: any = {};
  isDisabled: boolean = true;
  theme: string = 'light';
  permissions: string[] = [];
  isSuperUser: boolean = false;

  // GitHub integration properties
  githubConnected: boolean = false;
  showToken: boolean = false;
  githubRepositories: string[] = [];
  githubConfig: GitHubConfig = {
    repositoryUrl: '',
    branch: 'main',
    token: '',
    isConnected: false,
  };

  constructor(
    private authService: AuthService,
    private themeService: ThemeService,
    private connectedAppsService: ConnectedAppsService,
    private router: Router
  ) {}

  // Loading state
  loading: boolean = false;

  // Repository search
  searchTerm: string = '';
  filteredRepositories: string[] = [];
  selectedRepository: string = '';

  ngOnInit(): void {
    // Get current theme
    this.theme = this.themeService.getTheme();

    // Check if there's a token in the URL (from GitHub callback)
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    if (token) {
      console.log('Detected token in URL from GitHub callback');

      // Store the token in localStorage
      localStorage.setItem('auth_token', token);

      // Remove the token from the URL to prevent sharing it accidentally
      this.router.navigate(['/profile'], { replaceUrl: true });

      // Set GitHub as connected
      this.githubConnected = true;

      // Show success message
      setTimeout(() => {
        alert('Successfully connected to GitHub!');
      }, 500);

      // Refresh GitHub status after a short delay
      setTimeout(() => {
        this.refreshGitHubStatus();
      }, 1000);

      return; // Skip the rest of initialization since we're redirecting
    }

    // Check if this is a GitHub callback by looking for 'code' parameter in URL
    const code = urlParams.get('code');
    if (code) {
      console.log('Detected GitHub callback with code:', code);
      // This is a GitHub callback - show loading state
      this.loading = true;

      // We need to manually check GitHub connection status after callback
      setTimeout(() => {
        this.refreshGitHubStatus();
      }, 1000); // Give backend a moment to process the callback
    }

    // Check if we need to redirect after GitHub login
    this.checkGitHubRedirect();

    // Fetch user details, role details, and GitHub config
    this.loadUserData();
  }

  /**
   * Refresh GitHub connection status
   */
  refreshGitHubStatus(): void {
    console.log('Refreshing GitHub connection status...');
    this.loading = true;

    // First try to get repositories directly - this is the most reliable way to check connection
    this.connectedAppsService.getGitHubRepositories().subscribe({
      next: (repos) => {
        console.log('GitHub repositories loaded:', repos);

        // If we can get repositories, GitHub is definitely connected
        this.githubConnected = true;

        // Update repositories
        if (Array.isArray(repos)) {
          this.githubRepositories = repos;
          this.filteredRepositories = [...repos];

          // If we have repositories, select the first one by default
          if (repos.length > 0 && !this.selectedRepository) {
            this.selectedRepository = repos[0];
            this.githubConfig.repositoryUrl = `https://github.com/${repos[0]}`;
          }
        }

        // Now get the full config
        this.connectedAppsService.getGitHubConfig().subscribe({
          next: (config) => {
            console.log('GitHub config refreshed:', config);
            this.githubConfig = config;
            this.githubConfig.isConnected = true; // Force connected status
            this.loading = false;
          },
          error: (err) => {
            console.error('Error refreshing GitHub config:', err);
            // We already know GitHub is connected from the repositories call
            this.loading = false;
          },
        });
      },
      error: (err) => {
        console.error('Error loading GitHub repositories:', err);

        // Fallback to config check
        this.connectedAppsService.getGitHubConfig().subscribe({
          next: (config) => {
            console.log('GitHub config refreshed:', config);
            this.githubConfig = config;
            this.githubConnected = config.isConnected;
            this.loading = false;
          },
          error: (err) => {
            console.error('Error refreshing GitHub status:', err);
            this.githubConnected = false;
            this.loading = false;
          },
        });
      },
    });
  }

  /**
   * Load all user data including GitHub status
   */
  loadUserData(): void {
    this.loading = true;

    forkJoin({
      userDetails: this.authService.getUserDetails(),
      userRoleDetails: this.authService.getUserRoleDetails(),
      githubConfig: this.connectedAppsService.getGitHubConfig().pipe(
        catchError((err) => {
          console.error('Error fetching GitHub config:', err);
          return of(null);
        })
      ),
    }).subscribe({
      next: (response) => {
        if (response.userDetails) {
          this.userDetails = response.userDetails;
        }

        if (response.userRoleDetails) {
          this.userRoleDetails = response.userRoleDetails;
          this.isSuperUser = response.userRoleDetails.is_superuser || false;

          // Extract permissions from role details
          if (
            this.userRoleDetails.role &&
            this.userRoleDetails.role.permissions
          ) {
            if (Array.isArray(this.userRoleDetails.role.permissions)) {
              this.permissions = this.userRoleDetails.role.permissions;
            } else {
              // Convert object format to array if needed
              this.permissions = Object.entries(
                this.userRoleDetails.role.permissions
              )
                .filter(([_, value]) => value === true)
                .map(([key, _]) => key);
            }
          }

          console.log('User role details loaded:', this.userRoleDetails);
          console.log('Permissions:', this.permissions);
        }

        // Set GitHub config if available
        if (response.githubConfig) {
          this.githubConfig = response.githubConfig;
          this.githubConnected = this.githubConfig.isConnected;
          console.log('GitHub config loaded:', this.githubConfig);

          // If connected, load repositories
          if (this.githubConnected) {
            this.viewGitHubRepositories();
          }
        } else {
          // Initialize with default values if no config is available
          this.githubConfig = {
            repositoryUrl: '',
            branch: 'main',
            token: '',
            isConnected: false,
          };
          this.githubConnected = false;
        }
      },
      error: (err) => {
        console.error('Error fetching user data:', err);
      },
    });
  }

  // Helper methods for permission filtering
  hasPermissionType(type: string): boolean {
    return this.permissions.some((p) => p.includes(type));
  }

  getPermissionsByType(type: string): string[] {
    return this.permissions.filter((p) => p.includes(type));
  }

  hasOtherPermissions(): boolean {
    return this.permissions.some(
      (p) =>
        !p.includes('connection') &&
        !p.includes('document') &&
        !p.includes('code') &&
        !p.includes('user')
    );
  }

  getOtherPermissions(): string[] {
    return this.permissions.filter(
      (p) =>
        !p.includes('connection') &&
        !p.includes('document') &&
        !p.includes('code') &&
        !p.includes('user')
    );
  }

  // Hover flags for tooltips
  showOrgTooltip = false;
  showPhoneTooltip = false;
  showNotificationTooltip = false;
  showMfaTooltip = false;

  // Enable all inputs when user clicks "Edit"
  enableInput(): void {
    this.isDisabled = false;
  }

  /**
   * Toggle visibility of GitHub token
   */
  toggleTokenVisibility(): void {
    this.showToken = !this.showToken;
  }

  /**
   * Connect to GitHub using OAuth
   */
  connectToGitHub(): void {
    this.connectedAppsService.initiateGitHubLogin();
  }

  /**
   * View GitHub repositories
   */
  viewGitHubRepositories(): void {
    this.loading = true;
    this.connectedAppsService.getGitHubRepositories().subscribe({
      next: (repos) => {
        console.log('GitHub repositories:', repos);
        this.loading = false;

        // Update the repositories array
        if (Array.isArray(repos)) {
          this.githubRepositories = repos;
          this.filteredRepositories = [...repos]; // Initialize filtered list
        } else {
          this.githubRepositories = [];
          this.filteredRepositories = [];
        }

        // If no repositories found, show a message
        if (this.githubRepositories.length === 0) {
          alert('No repositories found');
        }
      },
      error: (err) => {
        console.error('Error fetching GitHub repositories:', err);
        this.githubRepositories = [];
        this.filteredRepositories = [];
        this.loading = false;
        alert('Failed to fetch GitHub repositories. Please try again.');
      },
    });
  }

  /**
   * Filter repositories based on search term
   */
  filterRepositories(): void {
    if (!this.searchTerm) {
      this.filteredRepositories = [...this.githubRepositories];
      return;
    }

    const term = this.searchTerm.toLowerCase();
    this.filteredRepositories = this.githubRepositories.filter((repo) =>
      repo.toLowerCase().includes(term)
    );
  }

  /**
   * Select a repository for linking
   * @param repoName The repository name to select
   */
  selectRepository(repoName: string): void {
    this.selectedRepository = repoName;

    // Close the repository selection modal if it exists
    const repoModalCheckbox = document.getElementById(
      'repo-select-modal'
    ) as HTMLInputElement;
    if (repoModalCheckbox) {
      repoModalCheckbox.checked = false;
    }

    // Update the GitHub config with the selected repository
    this.githubConfig.repositoryUrl = `https://github.com/${repoName}`;

    // Show confirmation
    alert(
      `Repository "${repoName}" selected. You can now link it to a database connection.`
    );
  }

  /**
   * Disconnect from GitHub
   */
  disconnectGitHub(): void {
    if (confirm('Are you sure you want to disconnect from GitHub?')) {
      this.connectedAppsService.disconnectGitHub().subscribe({
        next: () => {
          this.githubConnected = false;
          this.githubConfig.isConnected = false;
          // Clear sensitive data
          this.githubConfig.token = '';
          alert('Successfully disconnected from GitHub');
        },
        error: (err) => {
          console.error('Error disconnecting from GitHub:', err);
          alert('Failed to disconnect from GitHub. Please try again.');
        },
      });
    }
  }

  /**
   * Check if we need to redirect after GitHub login
   */
  checkGitHubRedirect(): void {
    const redirectUrl = localStorage.getItem('github_redirect_url');
    if (redirectUrl) {
      console.log('Found GitHub redirect URL:', redirectUrl);

      // Clear the stored URL to prevent future redirects
      localStorage.removeItem('github_redirect_url');

      // Only redirect if we're on the profile page and the URL is different
      if (
        window.location.href !== redirectUrl &&
        window.location.pathname.includes('/profile')
      ) {
        console.log('Redirecting to:', redirectUrl);
        window.location.href = redirectUrl;
      }
    }
  }

  /**
   * Delete/unlink a GitHub repository
   * @param repoName The name of the repository to delete/unlink
   */
  deleteRepository(repoName: string): void {
    if (
      confirm(
        `Are you sure you want to remove access to repository "${repoName}"?`
      )
    ) {
      this.loading = true;

      // Call the API to unlink the repository
      this.connectedAppsService.unlinkGitHubRepository(repoName).subscribe({
        next: (response) => {
          console.log('Repository unlink response:', response);
          this.loading = false;

          // Remove the repository from the local arrays
          this.githubRepositories = this.githubRepositories.filter(
            (repo) => repo !== repoName
          );
          this.filteredRepositories = this.filteredRepositories.filter(
            (repo) => repo !== repoName
          );

          // If this was the selected repository, clear the selection
          if (this.selectedRepository === repoName) {
            this.selectedRepository = '';
          }

          alert(`Repository "${repoName}" has been unlinked successfully.`);
        },
        error: (err) => {
          console.error('Error unlinking repository:', err);
          this.loading = false;

          // If we get a specific error message from the API, show it
          if (err.error && err.error.message) {
            alert(`Failed to unlink repository: ${err.error.message}`);
          } else {
            alert('Failed to unlink repository. Please try again.');
          }
        },
      });
    }
  }

  /**
   * Save GitHub configuration and close modal
   */
  saveGitHubConfig(): void {
    // Validate inputs
    if (!this.githubConfig.repositoryUrl) {
      alert('Please enter a repository URL');
      return;
    }

    // With OAuth flow, we don't need a token anymore
    // Just validate the repository URL
    this.connectedAppsService
      .testGitHubConnection(this.githubConfig)
      .subscribe({
        next: () => {
          // Store the repository URL and branch in localStorage for later use
          localStorage.setItem(
            'github_config_temp',
            JSON.stringify(this.githubConfig)
          );

          // Close modal by unchecking the checkbox
          const modalCheckbox = document.getElementById(
            'github-modal'
          ) as HTMLInputElement;
          if (modalCheckbox) {
            modalCheckbox.checked = false;
          }

          // Redirect to GitHub OAuth directly
          this.connectToGitHub();
        },
        error: (err) => {
          console.error('Error validating GitHub repository:', err);
          alert(
            'Failed to validate GitHub repository. Please check the URL and try again.'
          );
        },
      });
  }

  /**
   * Link a GitHub repository to a database connection
   * @param databaseId The database ID to link the repository to
   */
  linkRepositoryToDatabase(databaseId: string): void {
    if (!this.selectedRepository) {
      alert('Please select a repository first');
      return;
    }

    this.loading = true;
    this.connectedAppsService
      .linkGitHubRepository(databaseId, this.selectedRepository)
      .subscribe({
        next: (response) => {
          console.log('Repository linked successfully:', response);
          this.loading = false;
          alert(
            `Repository "${this.selectedRepository}" linked to database successfully!`
          );
        },
        error: (err) => {
          console.error('Error linking repository to database:', err);
          this.loading = false;
          alert('Failed to link repository to database. Please try again.');
        },
      });
  }
}

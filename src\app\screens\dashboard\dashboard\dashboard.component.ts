import { Component } from '@angular/core';
import { DatabaseService } from 'src/app/core/services/database/database.service';
import {
  trigger,
  transition,
  style,
  animate,
  query,
  stagger,
} from '@angular/animations';
import { ThemeService } from 'src/app/core/theme/theme.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('400ms ease-out', style({ opacity: 1 })),
      ]),
    ]),
    trigger('staggerFadeIn', [
      transition('* => *', [
        query(
          ':enter',
          [
            style({ opacity: 0, transform: 'translateY(10px)' }),
            stagger('100ms', [
              animate(
                '400ms ease-out',
                style({ opacity: 1, transform: 'translateY(0)' })
              ),
            ]),
          ],
          { optional: true }
        ),
      ]),
    ]),
  ],
})
export class DashboardComponent {
  constructor(private dbService: DatabaseService) {}
  stats: any;
  disconnectedCount = 0;
  images = [
    {
      text: 'Seamless Database Connection & AI Analysis',
      desc: 'Easily connect to your existing database — our AI instantly scans and analyzes your data landscape.\nUnderstand the structure, relationships, and metadata of your data objects in minutes.',
      imageSrc: 'assets/icons/db-ai.svg',
      imageAlt: 'nature1',
    },
    {
      text: 'Auto-generate Business Docs & Code Objects',
      desc: 'Our platform auto-generates:\n- Detailed business documentation\n- API endpoints\n- Database code objects for your target system\nEverything is tailored and transformation-ready.',
      imageSrc: 'assets/icons/code-ai.svg',
      imageAlt: 'nature2',
    },
    {
      text: 'Intelligent Prompts & Dependency Visualization',
      desc: 'Get AI-powered transformation instructions, optimized for your use case.\nPlus, explore an interactive dependency chart that makes navigating complex data relationships a breeze.',
      imageSrc: 'assets/icons/graph.svg',
      imageAlt: 'person1',
    },
  ];

  calculatePercentage(part: number = 0, total: number = 0): any {
    if (!total || total === 0) return 0;
    const percent = (part / total) * 100;
    // Ensure a minimum visible width for the progress bar (at least 1%)
    return Math.max(1, parseFloat(percent.toFixed(1)));
  }

  ngOnInit(): void {
    this.dbService.getDatabaseStats().subscribe({
      next: (stats) => {
        console.log('Dashboard Stats:', stats);
        this.stats = stats;

        // Example: Mark disconnected as 0 for now, or add real logic
        this.disconnectedCount = 0; // Or calculate based on actual db status
      },
      error: (err) => console.error('Failed to load stats', err),
    });

    this.calculatePercentage();
  }

  // Get current theme from localStorage
  getTheme(): string {
    return localStorage.getItem('theme') || 'light';
  }
}

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UserManagementRoutingModule } from './user-management-routing.module';
import { OrganizationDetailsComponent } from './organization-details/organization-details.component';
import { UserManagementComponent } from './user-management.component';

@NgModule({
  declarations: [OrganizationDetailsComponent, UserManagementComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    UserManagementRoutingModule,
  ],
})
export class UserManagementModule {}

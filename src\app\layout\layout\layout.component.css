/* Sidebar Links */
nav ul li a {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  color: #fff;
  text-decoration: none;
  border-left: 4px solid transparent;
  @apply transition-all duration-300 ease-in-out;
  margin: 2px 0;
}

/* Active Link Styling */
nav ul li a.active {
  @apply bg-primary border-white;
  transition: all 0.3s ease-in-out;
  box-shadow: none;
  border-radius: 0.25rem;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

/* Hover Effect */
nav ul li a:hover {
  @apply bg-primary border-white;
  transition: all 0.3s ease-in-out;
  box-shadow: none;
  border-radius: 0.25rem;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

/* Dark Mode Enhancements */
[data-theme="dark"] nav ul li a {
  color: var(--content1, #ffffff);
}

[data-theme="dark"] nav ul li a.active {
  background-color: rgba(148, 0, 255, 0.15);
  border-color: var(--primary, #9400ff);
  box-shadow: none;
  border-radius: 0.25rem;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

[data-theme="dark"] nav ul li a:hover {
  background-color: rgba(148, 0, 255, 0.08);
  border-color: var(--primary, #9400ff);
  box-shadow: none;
  border-radius: 0.25rem;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

/* Main Content */
.main-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

/* Top Bar */
.topbar {
  background: #444;
  color: white;
  padding: 15px;
}

/* Content Section */
.content {
  padding: 20px;
}
.border-b {
  border-color: rgb(var(--border) / var(--tw-bg-opacity));
}

/* Help Card Styling */
[data-theme="dark"] .shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2),
    0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] a[routerLink="/help"] .border {
  border-width: 1px;
  border-style: solid;
}

/* Theme Switch Styling */
.theme-switch-wrapper {
  position: relative;
  display: inline-block;
  height: 30px;
  width: 60px;
}

.theme-switch {
  display: inline-block;
  height: 100%;
  width: 100%;
  position: relative;
}

.theme-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #d0d0d0; /* Darker for better contrast with icons */
  transition: 0.4s;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1); /* Add subtle border */
}

[data-theme="dark"] .slider {
  background-color: #222;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  z-index: 2;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .slider:before {
  background-color: #555;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.icons-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 6px;
  box-sizing: border-box;
  z-index: 1;
  transition: 0.4s;
}

.sun-icon {
  margin-left: 2px;
  font-size: 18px;
  color: #f39c12;
  transition: color 0.3s ease;
}

.moon-icon {
  margin-right: 2px;
  font-size: 18px;
  color: #4a0093; /* Brighter purple for better visibility in light mode */
  transition: color 0.3s ease;
}

[data-theme="dark"] .sun-icon {
  color: #f1c40f;
}

[data-theme="dark"] .moon-icon {
  color: #74b9ff;
}

input:checked + .slider {
  background-color: #1a1a2e; /* Darker blue/black for better contrast */
}

input:checked + .slider:before {
  transform: translateX(30px);
  background-color: #f1c40f;
}

/* Make the inactive icon slightly transparent */
input:not(:checked) + .slider .moon-icon {
  opacity: 0.9;
}

input:checked + .slider .sun-icon {
  opacity: 0.9;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196f3;
}

.theme-switch:hover .slider {
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .theme-switch:hover .slider {
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.1);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

/* Animation for the icons */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideIn {
  0% {
    transform: translateX(-10px) scale(0.8);
    opacity: 0;
  }
  100% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes glow {
  0% {
    text-shadow: 0 0 0px currentColor;
  }
  50% {
    text-shadow: 0 0 5px currentColor;
  }
  100% {
    text-shadow: 0 0 0px currentColor;
  }
}

[data-theme="light"] .sun-icon {
  animation: fadeIn 0.5s ease-in-out, glow 2s ease-in-out infinite;
}

[data-theme="dark"] .moon-icon {
  animation: slideIn 0.5s ease-in-out, glow 2s ease-in-out infinite;
}

/* Improve the switch appearance */
.theme-switch-wrapper {
  margin-right: 4px;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.theme-switch-wrapper:hover {
  transform: scale(1);
}

import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { FormsModule, ReactiveFormsModule } from '@angular/forms'; // Import directly
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { AuthModule } from './screens/auth/auth.module';
import { LayoutComponent } from './layout/layout/layout.component';
import { SessionTimeoutComponent } from './shared/components/session-timeout/session-timeout.component';
import { SharedModule } from './shared/shared/shared.module';
import { NotFoundComponent } from './screens/not-found/not-found.component';
import { ChatBotComponent } from './screens/chat-bot/chat-bot.component';
import { MarkdownModule } from 'ngx-markdown';
import { HelpComponent } from './screens/help/help.component';
@NgModule({
  declarations: [
    AppComponent,
    LayoutComponent,
    SessionTimeoutComponent,
    NotFoundComponent,
    ChatBotComponent,
    HelpComponent,
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    AuthModule,
    FormsModule, // Now available globally
    ReactiveFormsModule,
    SharedModule,
    MarkdownModule.forRoot(),
  ],
  providers: [
    // Include RoleGuard in providers
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}

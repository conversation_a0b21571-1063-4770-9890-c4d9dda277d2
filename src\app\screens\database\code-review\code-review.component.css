.hljs {
  @apply bg-backgroundPrimary text-content1;
}

.resizable-panel {
  min-width: 200px;
  overflow: hidden; /* Prevent overflow from the panel itself */
}

.resizer {
  width: 6px;
  cursor: col-resize;
  background-color: #e5e7eb; /* light gray */
  position: relative;
  z-index: 10;
  transition: background-color 0.2s ease;
}

.resizer:hover,
.resizer:active {
  background-color: #cbd5e1; /* slightly darker on hover */
}

/* Ensure code blocks take full height */
pre {
  margin: 0;
  height: 100%;
}

/* Ensure proper scrolling for code content */
pre code {
  display: block;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #fff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

<div class="justify-center flex flex-col" [attr.data-theme]="getTheme()">
  <div
    *ngIf="images && images.length > 0"
    class="carousel-container max-md:h-[250px] min-h-[230px] items-center justify-center flex"
    [attr.data-theme]="getTheme()"
  >
    <div *ngFor="let image of images; let i = index">
      <div
        class="flex flex-row items-center justify-between gap-10 max-md:gap-4 py-4"
      >
        <div class="flex flex-col text-left">
          <h1
            *ngIf="selectedIndex === i"
            class="w-[65%] text-primary text-2xl font-semibold text-left max-md:text-[22px] max-w-[500px] min-w-[500px] carousel-text"
            [ngClass]="{ active: selectedIndex === i }"
          >
            {{ image.text }}
          </h1>
          <p
            *ngIf="selectedIndex === i"
            class="whitespace-pre-line max-w-[500px] min-w-[500px] text-content2 carousel-text"
            [ngClass]="{ active: selectedIndex === i }"
            [style.animation-delay]="'0.1s'"
          >
            {{ image.desc }}
          </p>
        </div>
        <img
          [src]="image.imageSrc"
          [alt]="image.imageAlt"
          [ngClass]="{ 'image-active': selectedIndex === i }"
          class="h-[100px] max-md:h-[60px]"
        />
      </div>
    </div>
  </div>
  <div class="bottom-4 mt-3 justify-center flex">
    <div *ngIf="indicators" class="carousel-dot-container">
      <span
        *ngFor="let dot of images; let i = index"
        [ngClass]="{ active: selectedIndex === i }"
        class="dot"
        (click)="selectImage(i)"
      >
      </span>
    </div>
  </div>
</div>

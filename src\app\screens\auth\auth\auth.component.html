<div
  class="auth-container flex flex-col md:grid md:grid-cols-2 h-full min-h-screen w-full p-10 0 items-center"
>
  <!-- Right Section: Image -->
  <div
    class="h-60 md:h-full flex flex-col gap-4 items-start justify-center bg-cover bg-center text-white w-full max-md:text-center"
  >
    <div><img src="assets/logo-full.png" class="h-12" /></div>
    <h1 class="text-7xl font-semibold">Welcome to RevMigrate</h1>
    <h3 class="text-xl">Accelerate your Data migration</h3>
    <!-- 
    <app-button type="primary" (click)="toggleTheme()">
      Toggle Theme</app-button
    > -->
  </div>

  <!-- LOGIN VIEW -->
  <div
    class="flex flex-col max-md:w-4/5 p-8 auth-card"
    [attr.data-theme]="theme"
    *ngIf="currentView === 'login'"
  >
    <h2 class="text-2xl font-bold text-white text-center">Welcome Back!</h2>
    <p class="font-semibold text-lg text-white text-center">
      Login to your account
    </p>
    <div class="w-full mt-6 flex flex-row justify-center items-center gap-4">
      <button class="btn rounded-md btn-block">
        <img
          src="/assets/icons/windows.svg"
          class="mr-2"
          type="default"
        />Mircrosoft
      </button>
      <button class="btn rounded-md btn-block">
        <img src="/assets/icons/google.svg" class="mr-2" type="default" />Google
      </button>
    </div>
    <div class="divider divider-horizontal my-5">OR</div>
    <div class="w-full grid grid-rows-2 gap-4 text-center">
      <div class="form-control relative w-full">
        <input
          type="email"
          class="input input-lg max-w-full"
          placeholder="Email"
          [(ngModel)]="email"
        />

        <span class="absolute inset-y-0 right-4 inline-flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-content3"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
            />
          </svg>
        </span>
      </div>
      <div class="form-control relative w-full">
        <input
          [type]="showPassword ? 'text' : 'password'"
          class="input input-lg max-w-full"
          placeholder="Password"
          [(ngModel)]="password"
        />

        <span
          class="absolute inset-y-0 right-4 inline-flex items-center text-content2 cursor-pointer"
          (click)="showPassword = !showPassword"
        >
          <i
            class="ti ti-eye-off text-xl text-content3"
            *ngIf="!showPassword"
          ></i>
          <i class="ti ti-eye text-xl text-content3" *ngIf="showPassword"></i>
        </span>
      </div>

      <button class="btn btn-primary w-full" (click)="login()">Login</button>

      <p *ngIf="errorMessage" class="error">{{ errorMessage }}</p>
      <p class="text-secondary cursor-pointer hover:underline">
        <a (click)="switchView('forgot-password')">Forgot Password?</a>
      </p>
    </div>

    <p class="mt-0 text-center">
      Don't have an account?
      <a (click)="switchView('register')" class="cursor-pointer hover:underline"
        >Sign up</a
      >
    </p>
  </div>
  <!-- REGISTER VIEW -->
  <div
    *ngIf="currentView === 'register'"
    class="flex flex-col max-md:w-4/5 justify-center items-center p-8 auth-card text-white"
    [attr.data-theme]="theme"
  >
    <h2 class="text-2xl font-bold text-white text-center">Sign Up</h2>

    <div class="w-full grid grid-rows-2 gap-4 text-center mt-6">
      <input
        class="input input-block input-solid bg-backgroundPrimary /20"
        type="text"
        placeholder="Name"
        [(ngModel)]="full_name"
      />

      <input
        class="input input-block input-solid bg-backgroundPrimary /20"
        type="email"
        placeholder="Email"
        [(ngModel)]="email"
      />
      <input
        class="input input-block input-solid bg-backgroundPrimary /20"
        type="password"
        placeholder="Password"
        [(ngModel)]="password"
      />

      <button class="btn btn-primary w-full" (click)="signUp()">
        Register
      </button>

      <p *ngIf="errorMessage" class="error">{{ errorMessage }}</p>
    </div>

    <p class="mt-0 text-center">
      Already have an account?
      <a (click)="switchView('login')" class="cursor-pointer hover:underline"
        >Login</a
      >
    </p>
  </div>
  <!-- FORGOT PASSWORD VIEW -->
  <div
    *ngIf="currentView === 'forgot-password'"
    class="flex flex-col max-md:w-4/5 justify-center items-center p-8 auth-card text-white"
    [attr.data-theme]="theme"
  >
    <h2 class="text-2xl font-bold text-white text-center">Forgot Password!</h2>
    <div class="w-full grid grid-rows-2 gap-4 text-center mt-6">
      <input
        class="input input-block input-solid bg-backgroundPrimary /20"
        type="email"
        placeholder="Enter your email"
      />

      <button class="btn btn-primary w-full">Reset Password</button>
      <p class="mt-0 text-center">
        Remember your password?
        <a (click)="switchView('login')" class="cursor-pointer hover:underline"
          >Back to Login</a
        >
      </p>
    </div>
  </div>
</div>

<!-- LOGIN VIEW -->
<!-- <div *ngIf="currentView === 'login'">
        <h2>Login</h2>
        <input type="text" [(ngModel)]="email" placeholder="Email" />
        <input type="password" [(ngModel)]="password" placeholder="Password" />
        <button (click)="login()">Login</button>
        <p *ngIf="errorMessage" class="error">{{ errorMessage }}</p>
        <p>
          <a (click)="switchView('forgot-password')">Forgot Password?</a>
        </p>
        <p>
          Don't have an account?
          <a (click)="switchView('register')">Sign up</a>
        </p>
      </div> -->

<!-- REGISTER VIEW -->
<!-- <div *ngIf="currentView === 'register'">
        <h2>Sign Up</h2>
        <input type="text" placeholder="Name" />
        <input type="email" placeholder="Email" />
        <input type="password" placeholder="Password" />
        <button>Register</button>
        <p>
          Already have an account?
          <a (click)="switchView('login')">Login</a>
        </p>
      </div> -->

<!-- FORGOT PASSWORD VIEW -->
<!-- <div *ngIf="currentView === 'forgot-password'">
        <h2>Forgot Password</h2>
        <input type="email" placeholder="Enter your email" />
        <button>Reset Password</button>
        <p>
          Remember your password?
          <a (click)="switchView('login')">Back to Login</a>
        </p>
      </div> -->

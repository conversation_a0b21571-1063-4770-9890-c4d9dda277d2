import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
interface SupportCategory {
  id: string;
  name: string;
}
@Component({
  selector: 'app-request-form',
  templateUrl: './request-form.component.html',
  styleUrls: ['./request-form.component.css'],
})
export class RequestFormComponent {
  helpForm: FormGroup;
  isSubmitting: boolean = false;
  submitSuccess: boolean = false;
  submitError: boolean = false;

  constructor(private fb: FormBuilder) {
    this.helpForm = this.fb.group({
      name: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      title: ['', Validators.required],
      message: ['', [Validators.required, Validators.minLength(10)]],
    });
  }

  ngOnInit(): void {
    // Prefill the user's name if available
    const userName = localStorage.getItem('userName');
    if (userName) {
      this.helpForm.patchValue({
        name: userName,
      });
    }
  }

  onSubmit(): void {
    if (this.helpForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.helpForm.controls).forEach((key) => {
        const control = this.helpForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;

    // For demo purposes, simulate a service call
    setTimeout(() => {
      this.isSubmitting = false;
      this.submitSuccess = true;
      console.log('Form submitted:', this.helpForm.value);
    }, 1500);
  }

  resetForm(): void {
    const userName = this.helpForm.get('name')?.value;
    this.helpForm.reset({ name: userName });
    this.submitSuccess = false;
    this.submitError = false;
  }
}

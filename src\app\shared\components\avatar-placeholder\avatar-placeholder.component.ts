import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-avatar-placeholder',
  templateUrl: './avatar-placeholder.component.html',
  styleUrls: ['./avatar-placeholder.component.css']
})
export class AvatarPlaceholderComponent implements OnInit {
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() name: string = '';
  @Input() className: string = '';
  
  avatarSrc: string = '';
  initials: string = '';
  
  ngOnInit(): void {
    // Generate initials from name
    if (this.name) {
      this.initials = this.generateInitials(this.name);
    }
    
    // Randomly select an avatar placeholder
    const avatarNumber = Math.floor(Math.random() * 4) + 1;
    this.avatarSrc = `assets/avatar-placeholder${avatarNumber > 1 ? '-' + avatarNumber : ''}.svg`;
  }
  
  private generateInitials(name: string): string {
    if (!name) return '';
    
    const nameParts = name.split(' ');
    if (nameParts.length === 1) {
      return nameParts[0].charAt(0).toUpperCase();
    } else {
      return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
    }
  }
}

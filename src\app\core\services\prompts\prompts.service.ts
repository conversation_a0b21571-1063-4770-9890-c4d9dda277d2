import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { EnvironmentService } from '../environment.service';

export interface PromptPayload {
  title: string;
  prompt_type: string;
  source_db_type: string;
  target_db_type: string;
  description: string;
  organization_id?: string;
}

@Injectable({
  providedIn: 'root',
})
export class PromptsService {
  private baseUrl: string;

  constructor(
    private http: HttpClient,
    private envService: EnvironmentService
  ) {
    // Set the base URL using the environment service
    // The environment service already handles HTTPS in production
    this.baseUrl = `${this.envService.apiBaseUrl}/prompt-vault`;

    // Log the URL in production for debugging
    if (this.envService.isProduction) {
      console.log('Prompts Service URL:', this.baseUrl);
    }
  }

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders({ Authorization: `Bearer ${token}` });
  }

  createPrompt(payload: PromptPayload): Observable<any> {
    // Ensure organization_id is included in the payload
    if (!payload.organization_id) {
      payload.organization_id = localStorage.getItem('organization_id') || '';
    }

    return this.http.post(this.baseUrl, payload, {
      headers: this.getAuthHeaders(),
    });
  }

  getPrompts(
    source_db_type?: string,
    target_db_type?: string,
    skip: number = 0,
    limit: number = 10,
    organization_id?: string
  ): Observable<any> {
    let params = new HttpParams().set('skip', skip).set('limit', limit);
    if (source_db_type) params = params.set('source_db_type', source_db_type);
    if (target_db_type) params = params.set('target_db_type', target_db_type);
    if (organization_id)
      params = params.set('organization_id', organization_id);

    return this.http.get(this.baseUrl, {
      params,
      headers: this.getAuthHeaders(),
    });
  }

  getPromptById(prompt_id: string): Observable<any> {
    return this.http.get(`${this.baseUrl}/${prompt_id}`);
  }

  updatePrompt(prompt_id: string, payload: PromptPayload): Observable<any> {
    return this.http.put(`${this.baseUrl}/${prompt_id}`, payload, {
      headers: this.getAuthHeaders(),
    });
  }

  deletePrompt(prompt_id: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/${prompt_id}`, {
      headers: this.getAuthHeaders(),
    });
  }
}

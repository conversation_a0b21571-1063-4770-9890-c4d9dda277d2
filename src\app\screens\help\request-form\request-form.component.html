<div class="container mx-auto py-8">
  <div class="text-center mb-8">
    <h1 class="text-3xl font-bold mb-4">Request Support</h1>
    <p class="text-gray-600 max-w-2xl mx-auto">
      Need help with RevMigrate? Fill out this form and our support team will
      get back to you as soon as possible.
    </p>
  </div>

  <div class="max-w-3xl mx-auto">
    <!-- Success Message -->
    <div
      *ngIf="submitSuccess"
      class="mb-8 bg-green-100 border-l-4 border-green-500 text-green-700 p-4"
    >
      <div class="flex items-center">
        <i class="ti ti-check-circle text-2xl mr-3"></i>
        <div>
          <p class="font-semibold">Support request submitted successfully!</p>
          <p>
            Our team will review your request and respond as soon as possible.
          </p>
        </div>
      </div>
      <div class="mt-4">
        <button class="btn btn-sm btn-outline" (click)="resetForm()">
          Submit another request
        </button>
      </div>
    </div>

    <!-- Error Message -->
    <div
      *ngIf="submitError"
      class="mb-8 bg-red-100 border-l-4 border-red-500 text-red-700 p-4"
    >
      <div class="flex items-center">
        <i class="ti ti-alert-circle text-2xl mr-3"></i>
        <div>
          <p class="font-semibold">
            There was a problem submitting your request
          </p>
          <p>
            Please try again or contact us directly at
            support&#64;revmigrate.com
          </p>
        </div>
      </div>
      <div class="mt-4">
        <button class="btn btn-sm btn-outline" (click)="resetForm()">
          Try again
        </button>
      </div>
    </div>

    <div *ngIf="!submitSuccess" class="w-full">
      <div class="card w-full">
        <div class="card-body">
          <form [formGroup]="helpForm" (ngSubmit)="onSubmit()" class="w-full">
            <div class="form-group mb-4">
              <label for="name" class="block text-sm font-medium mb-1"
                >Name</label
              >
              <input
                type="text"
                class="form-control input input-bordered w-full"
                id="name"
                placeholder="Your Name"
                formControlName="name"
                readonly
              />
              <div
                class="text-red-500 text-sm mt-1"
                *ngIf="
                  helpForm.get('name')?.invalid && helpForm.get('name')?.touched
                "
              >
                Name is required.
              </div>
            </div>

            <div class="form-group mb-4">
              <label for="email" class="block text-sm font-medium mb-1"
                >Email</label
              >
              <input
                type="email"
                class="form-control input input-block input-bordered w-full"
                id="email"
                placeholder="Email"
                formControlName="email"
                [disabled]="isSubmitting"
                [ngClass]="{
                  'input-error':
                    helpForm.get('email')?.invalid &&
                    helpForm.get('email')?.touched
                }"
              />
              <div
                class="text-red-500 text-sm mt-1"
                *ngIf="
                  helpForm.get('email')?.invalid &&
                  helpForm.get('email')?.touched
                "
              >
                <span *ngIf="helpForm.get('email')?.errors?.['required']"
                  >Email is required.</span
                >
                <span *ngIf="helpForm.get('email')?.errors?.['email']"
                  >Please enter a valid email address.</span
                >
              </div>
            </div>

            <div class="form-group mb-4">
              <label for="title" class="block text-sm font-medium mb-1"
                >Feedback Title</label
              >
              <input
                type="text"
                class="form-control input input-block input-bordered w-full"
                id="title"
                placeholder="Feedback Title"
                formControlName="title"
                [disabled]="isSubmitting"
                [ngClass]="{
                  'input-error':
                    helpForm.get('title')?.invalid &&
                    helpForm.get('title')?.touched
                }"
              />
              <div
                class="text-red-500 text-sm mt-1"
                *ngIf="
                  helpForm.get('title')?.invalid &&
                  helpForm.get('title')?.touched
                "
              >
                Feedback title is required.
              </div>
            </div>

            <div class="form-group mb-4">
              <label for="message" class="block text-sm font-medium mb-1"
                >Feedback Message</label
              >
              <textarea
                class="form-control textarea textarea-block textarea-bordered w-full"
                id="message"
                rows="3"
                placeholder="Feedback Message"
                formControlName="message"
                [disabled]="isSubmitting"
                [ngClass]="{
                  'textarea-error':
                    helpForm.get('message')?.invalid &&
                    helpForm.get('message')?.touched
                }"
              ></textarea>
              <div
                class="text-red-500 text-sm mt-1"
                *ngIf="
                  helpForm.get('message')?.invalid &&
                  helpForm.get('message')?.touched
                "
              >
                <span *ngIf="helpForm.get('message')?.errors?.['required']"
                  >Feedback message is required.</span
                >
                <span *ngIf="helpForm.get('message')?.errors?.['minlength']"
                  >Message must be at least 10 characters.</span
                >
              </div>
            </div>

            <div class="mt-6">
              <button
                class="btn btn-primary px-8"
                type="submit"
                [disabled]="isSubmitting"
              >
                {{ isSubmitting ? "Sending..." : "Send" }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

name: Deploy Angular App to EC2

on:
  push:
    branches: [vishal/dev]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20.12.2"

      - name: Install dependencies
        run: yarn install

      - name: Build Angular app
        run: yarn build

      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.EC2_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.EC2_HOST }} >> ~/.ssh/known_hosts

      # Create directory and set permissions before copying files
      - name: Prepare deployment directory
        run: |
          ssh ${{ secrets.EC2_USERNAME }}@${{ secrets.EC2_HOST }} "
            sudo rm -rf /var/www/html/angular/*
            sudo mkdir -p /var/www/html/angular
            sudo chown -R ${{ secrets.EC2_USERNAME }}:${{ secrets.EC2_USERNAME }} /var/www/html/angular
            sudo chmod -R 755 /var/www/html/angular
          "

      - name: Copy build files to server
        run: |
          scp -r dist/revmigrate/* ${{ secrets.EC2_USERNAME }}@${{ secrets.EC2_HOST }}:/var/www/html/angular/

      # Reset permissions after copying files
      - name: Set final permissions
        run: |
          ssh ${{ secrets.EC2_USERNAME }}@${{ secrets.EC2_HOST }} "
            sudo chown -R www-data:www-data /var/www/html/angular
            sudo chmod -R 755 /var/www/html/angular
          "

      - name: Configure Nginx
        run: |
          echo '
          server {
              listen 80;
              server_name revmigrateai.revt2d.com;
              
              # Redirect all HTTP traffic to HTTPS
              return 301 https://$host$request_uri;
          }

          server {
              listen 443 ssl;
              server_name revmigrateai.revt2d.com;
              
              ssl_certificate /etc/letsencrypt/live/revmigrateai.revt2d.com/fullchain.pem;
              ssl_certificate_key /etc/letsencrypt/live/revmigrateai.revt2d.com/privkey.pem;
              
              root /var/www/html/angular;
              index index.html;
              
              location / {
                  try_files $uri $uri/ /index.html;
                  
                  # Add headers to prevent mixed content
                  add_header Content-Security-Policy "upgrade-insecure-requests";
              }

              location /revmigrate_ai_agent {
                  proxy_pass https://revmigrate.revt2d.com;
                  proxy_http_version 1.1;
                  proxy_set_header Upgrade $http_upgrade;
                  proxy_set_header Connection "upgrade";
                  proxy_set_header Host $host;
                  proxy_cache_bypass $http_upgrade;
                  proxy_ssl_server_name on;
              }
          }
          ' | ssh ${{ secrets.EC2_USERNAME }}@${{ secrets.EC2_HOST }} "sudo tee /etc/nginx/sites-available/revmigrate"

      - name: Enable Nginx Configuration
        run: |
          ssh ${{ secrets.EC2_USERNAME }}@${{ secrets.EC2_HOST }} "
            sudo ln -sf /etc/nginx/sites-available/revmigrate /etc/nginx/sites-enabled/
            sudo rm -f /etc/nginx/sites-enabled/default
            sudo nginx -t && sudo systemctl restart nginx
          "

      # Now install SSL after Nginx is configured
      - name: Install and Configure SSL
        run: |
          ssh ${{ secrets.EC2_USERNAME }}@${{ secrets.EC2_HOST }} "
            sudo apt-get update
            sudo apt-get install -y certbot python3-certbot-nginx
            
            # Get and install SSL certificate non-interactively
            sudo certbot --nginx \
              -d revmigrateai.revt2d.com \
              --non-interactive \
              --agree-tos \
              --register-unsafely-without-email \
              --redirect || {
                echo 'Certbot failed. Checking nginx status...'
                sudo nginx -t
                echo 'Checking if port 80 is open...'
                nc -zv revmigrateai.revt2d.com 80
                echo 'Checking if port 443 is open...'
                nc -zv revmigrateai.revt2d.com 443
                exit 1
              }
          "

      # Setup auto-renewal without email
      - name: Setup SSL Auto-renewal
        run: |
          ssh ${{ secrets.EC2_USERNAME }}@${{ secrets.EC2_HOST }} "
            (crontab -l 2>/dev/null; echo '0 12 * * * /usr/bin/certbot renew --quiet') | crontab -
          "

      - name: Set permissions
        run: |
          ssh ${{ secrets.EC2_USERNAME }}@${{ secrets.EC2_HOST }} "
            sudo chown -R www-data:www-data /var/www/html/angular
            sudo chmod -R 755 /var/www/html/angular
          "

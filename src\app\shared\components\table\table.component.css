.table > thead > tr th:first-child {
  border-bottom-left-radius: 0rem;
  border-left: 0;
  border-top-left-radius: 0rem;
  @apply max-w-7;
}
.table > thead > tr th:last-child {
  border-bottom-right-radius: 0rem;
  border-right: 0;
  border-top-right-radius: 0rem;
}

.table > tbody > tr > th,
.table > thead > tr > th {
  @apply font-medium text-[#6E6E6E];
  transition: background-color 0.2s ease;
}

/* Status indicators */
.dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.dot-success {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--success) / var(--tw-bg-opacity));
  box-shadow: 0 0 6px rgba(44, 206, 138, 0.6);
}
.dot-error {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--error) / var(--tw-bg-opacity));
  box-shadow: 0 0 6px rgba(245, 54, 91, 0.6);
}
.dot-warning {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--warning) / var(--tw-bg-opacity));
  box-shadow: 0 0 6px rgba(251, 99, 64, 0.6);
}

/* Row hover effect */
.table-hover tbody tr {
  transition: all 0.2s ease-in-out;
}

.table-hover tbody tr:hover {
  background-color: rgba(148, 0, 255, 0.05) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Selected row styling */
.table tbody tr.selected {
  background-color: rgba(148, 0, 255, 0.1) !important;
  border-left: 3px solid rgb(var(--primary));
}

/* Checkbox animation */
.checkbox {
  transition: all 0.2s ease;
}

.checkbox:checked {
  animation: pulse 0.3s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Sort indicator animation */
.sort-indicator {
  transition: transform 0.3s ease;
}

.sort-asc {
  transform: rotate(0deg);
}

.sort-desc {
  transform: rotate(180deg);
}

/* Pagination button hover effect */
.pagination button:not([disabled]):hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Table header sticky effect */
thead th {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--backgroundPrimary);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

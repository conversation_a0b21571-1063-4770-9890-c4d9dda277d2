import { Component } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs';
import { AuthService } from 'src/app/core/auth/auth.service';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.css'],
})
export class LayoutComponent {
  userDetails: any = {};
  isSuperUser: boolean = false;
  isAdmin: boolean = false;
  isDebugMode: boolean = true; // Set to true to show debug info in the UI
  hasAdminAccess: boolean = false; // Cached result of admin access check

  constructor(private authService: AuthService, private router: Router) {}

  toggleTheme() {
    // Toggle between 'light' and 'dark' themes
    this.theme = this.theme === 'light' ? 'dark' : 'light';

    // Save the theme in localStorage
    localStorage.setItem('theme', this.theme);

    // Update the document data-theme attribute
    document.documentElement.setAttribute('data-theme', this.theme);

    // Update the body class for additional styling
    document.body.classList.remove('light', 'dark');
    document.body.classList.add(this.theme);

    console.log('Theme toggled to:', this.theme);
  }

  collapsed = false;

  pageTitle: string = 'Dashboard'; // Default title
  breadcrumb: string = ''; // Breadcrumb text
  breadcrumb1: string = '';
  breadcrumb2: string = '';
  breadcrumb1Link: string = '/';
  breadcrumb1QueryParams: any = {};
  theme: string = 'dark';
  // Define mapping of routes to page titles
  pageTitles: { [key: string]: string } = {
    '/dashboard': 'Dashboard',
    '/database': 'My Databases',
    '/database/connection-crm': 'Connection-CRM',
    '/doc-analysis': 'Document Analysis Hub',
    '/chat-assistant': 'Legal Chat Assistant',
    '/admin': 'Admin',
    '/user-management': 'Admin', // For backward compatibility
  };

  ngOnInit() {
    // Load sidebar state from localStorage
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState !== null) {
      this.collapsed = JSON.parse(savedState);
    }

    // Set the page title initially
    this.updateBreadcrumb();
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        setTimeout(() => this.updateBreadcrumb(), 100);
      });

    // Listen for custom breadcrumb update events
    window.addEventListener('updateBreadcrumb', ((event: CustomEvent) => {
      console.log('Received updateBreadcrumb event:', event.detail);
      if (event.detail && event.detail.path && event.detail.title) {
        // Update pageTitles with the new title
        this.pageTitles[event.detail.path] = event.detail.title;
        // Update the breadcrumb
        this.updateBreadcrumb();
      }
    }) as EventListener);

    // Load the theme from localStorage
    const savedTheme = localStorage.getItem('theme') || 'light';
    this.theme = savedTheme;

    // Apply theme to both document and body
    document.documentElement.setAttribute('data-theme', this.theme);
    document.body.classList.add(this.theme);

    console.log('Theme loaded:', this.theme);

    // First try to get user details from the standard endpoint
    this.authService.getUserDetails().subscribe({
      next: (response) => {
        if (response) {
          this.userDetails = response;
          this.isSuperUser = response.is_superuser || false;

          // Case-insensitive check for Admin role
          const roleName = response.role?.name || '';
          this.isAdmin = roleName.toLowerCase() === 'admin';

          console.log('User details from getUserDetails:', {
            userDetails: response,
            isSuperUser: this.isSuperUser,
            isAdmin: this.isAdmin,
            role: response.role?.name,
            roleLowerCase: roleName.toLowerCase(),
          });

          // Update admin access status
          this.updateAdminAccessStatus();

          // Update breadcrumb after user details are loaded
          this.updateBreadcrumb();
        }
      },
      error: (err) => {
        console.error('Error fetching user details:', err);
      },
      complete: () => {
        // After getting basic user details, also fetch detailed role information
        this.fetchUserRoleDetails();
      },
    });
  }

  // Method to navigate back to parent with correct query parameters
  navigateToBreadcrumb1() {
    if (this.breadcrumb1Link) {
      this.router.navigateByUrl(this.breadcrumb1Link);
    }
  }

  updateBreadcrumb() {
    let fullPath = this.router.url.split('?')[0]; // Remove query parameters
    const queryString = this.router.url.includes('?')
      ? this.router.url.split('?')[1]
      : '';
    console.log('Current Route (without queryParams):', fullPath);
    console.log('Query Parameters:', queryString);

    // Parse query parameters into an object
    const currentQueryParams: any = {};
    if (queryString) {
      queryString.split('&').forEach((param) => {
        const [key, value] = param.split('=');
        if (key && value) {
          // Only add if both key and value exist
          currentQueryParams[key] = decodeURIComponent(value);
        }
      });
    }
    console.log('Parsed Query Parameters:', currentQueryParams);

    const routeSegments = fullPath
      .split('/')
      .filter((segment) => segment)
      .map(decodeURIComponent);

    // Load previously stored page titles from localStorage
    const storedPageTitles = localStorage.getItem('pageTitles');
    if (storedPageTitles) {
      this.pageTitles = JSON.parse(storedPageTitles);
    }

    if (routeSegments.length === 1) {
      this.breadcrumb1 =
        this.pageTitles['/' + routeSegments[0]] ||
        this.capitalize(routeSegments[0]);
      this.breadcrumb1Link = '/' + encodeURIComponent(routeSegments[0]);
      this.breadcrumb2 = '';
      this.pageTitle = this.breadcrumb1;
    } else if (routeSegments.length > 1) {
      // Special handling for database sub-routes
      if (routeSegments.length >= 2 && routeSegments[0] === 'database') {
        if (routeSegments.length === 2) {
          // For /database/CRM
          this.breadcrumb1 = 'My Databases';
          this.breadcrumb1Link = '/database';
          this.breadcrumb2 = routeSegments[1]; // CRM
          this.pageTitle = this.breadcrumb2;

          // Store the current DB view URL for later use
          localStorage.setItem('lastDbViewUrl', this.router.url);
        } else if (routeSegments.length === 3) {
          // For /database/CRM/code review
          this.breadcrumb1 = routeSegments[1]; // CRM

          // Check if we have a stored URL, otherwise build it
          const lastDbViewUrl = localStorage.getItem('lastDbViewUrl');
          if (
            lastDbViewUrl &&
            lastDbViewUrl.includes(`/database/${routeSegments[1]}`)
          ) {
            this.breadcrumb1Link = lastDbViewUrl;
          } else {
            // Construct the link back to the database with query parameters
            let dbParams = '';
            if (currentQueryParams.id) {
              dbParams += `id=${currentQueryParams.id}`;
            }
            if (currentQueryParams.type) {
              dbParams += dbParams
                ? `&type=${currentQueryParams.type}`
                : `type=${currentQueryParams.type}`;
            }

            this.breadcrumb1Link = `/database/${encodeURIComponent(
              routeSegments[1]
            )}${dbParams ? '?' + dbParams : ''}`;
          }

          this.breadcrumb2 = this.capitalize(
            routeSegments[2].replace('%20', ' ')
          );
          this.pageTitle = this.breadcrumb2;
        }
      } else if (
        routeSegments.length >= 2 &&
        routeSegments[0] === 'admin' &&
        routeSegments[1] === 'organization'
      ) {
        // Special handling for organization details page
        if (routeSegments.length === 3) {
          // For /admin/organization/:id
          const activePath =
            '/' + routeSegments.map(encodeURIComponent).join('/');

          // Load stored page titles from localStorage to ensure we have the latest data
          const storedPageTitles = localStorage.getItem('pageTitles');
          if (storedPageTitles) {
            try {
              const parsedTitles = JSON.parse(storedPageTitles);
              // Only update if we have a title for this path
              if (parsedTitles[activePath]) {
                this.pageTitles[activePath] = parsedTitles[activePath];
              }
            } catch (e) {
              console.error('Error parsing pageTitles from localStorage:', e);
            }
          }

          // Set breadcrumb for all users (we'll handle visibility in the template)
          this.breadcrumb1 = 'Admin';
          this.breadcrumb1Link = '/admin';

          // Get organization name from pageTitles if available
          this.breadcrumb2 =
            this.pageTitles[activePath] || 'Organization Details';
          this.pageTitle = this.breadcrumb2;

          console.log('Organization breadcrumb set:', {
            path: activePath,
            breadcrumb1: this.breadcrumb1,
            breadcrumb2: this.breadcrumb2,
            isSuperUser: this.isSuperUser,
            storedTitle: this.pageTitles[activePath],
          });
        }
      } else {
        // Standard breadcrumb behavior for other routes
        const parentPath =
          '/' + routeSegments.slice(0, -1).map(encodeURIComponent).join('/');
        const activePath =
          '/' + routeSegments.map(encodeURIComponent).join('/');

        this.breadcrumb1 =
          this.pageTitles[parentPath] || this.capitalize(routeSegments[0]);
        this.breadcrumb1Link = parentPath;

        if (!this.pageTitles[activePath]) {
          this.pageTitles[activePath] = this.capitalize(
            routeSegments[routeSegments.length - 1]
          );

          // Save updated titles to localStorage
          localStorage.setItem('pageTitles', JSON.stringify(this.pageTitles));
        }

        this.breadcrumb2 = decodeURIComponent(this.pageTitles[activePath]);
        this.pageTitle = this.breadcrumb2 || this.pageTitles[parentPath];
      }

      // Store last visited page title to keep consistency across refreshes
      localStorage.setItem('lastPageTitle', this.pageTitle);
    } else {
      this.pageTitle = 'Dashboard';
    }

    // Update the document title dynamically
    document.title = this.pageTitle;

    console.log('Updated Page Title:', this.pageTitle);
    console.log('Breadcrumb 1:', this.breadcrumb1);
    console.log('Breadcrumb 1 Link:', this.breadcrumb1Link);
    console.log('Breadcrumb 2:', this.breadcrumb2);
  }

  capitalize(str: string) {
    return str ? str.charAt(0).toUpperCase() + str.slice(1) : '';
  }

  toggleSidebar() {
    this.collapsed = !this.collapsed;
    localStorage.setItem('sidebarCollapsed', JSON.stringify(this.collapsed));
  }

  logout() {
    localStorage.removeItem('sidebarCollapsed');
    this.authService.logout();
  }

  /**
   * Fetch detailed user role information
   * This helps ensure we have the most accurate role data
   */
  fetchUserRoleDetails(): void {
    // First try to get detailed role information
    this.authService.getUserRoleDetails().subscribe({
      next: (roleDetails) => {
        if (roleDetails) {
          console.log('User role details fetched:', roleDetails);

          // Update user role flags based on the detailed information
          if (roleDetails.is_superuser !== undefined) {
            this.isSuperUser = roleDetails.is_superuser;
          }

          // Check for admin role in the detailed role information
          if (roleDetails.role && roleDetails.role.name) {
            const roleName = roleDetails.role.name;
            this.isAdmin = roleName.toLowerCase() === 'admin';

            // Also update the role in userDetails if needed
            if (
              this.userDetails &&
              (!this.userDetails.role || !this.userDetails.role.name)
            ) {
              if (!this.userDetails.role) {
                this.userDetails.role = {};
              }
              this.userDetails.role.name = roleName;
            }
          }

          console.log('Updated user role flags from getUserRoleDetails:', {
            isSuperUser: this.isSuperUser,
            isAdmin: this.isAdmin,
          });

          // Update admin access status
          this.updateAdminAccessStatus();
        }
      },
      error: (err) => {
        console.error('Error fetching user role details:', err);
        // If detailed role fetch fails, try the getUserRole method
        this.fetchUserRoleFromAuthService();
      },
      complete: () => {
        // Also try getUserRole to ensure we have the most accurate information
        this.fetchUserRoleFromAuthService();
      },
    });
  }

  /**
   * Fetch user role information using the getUserRole method
   * This provides another way to get role information if the detailed fetch fails
   */
  fetchUserRoleFromAuthService(): void {
    this.authService.getUserRole().subscribe({
      next: (roleInfo) => {
        if (roleInfo) {
          console.log('User role info from getUserRole:', roleInfo);

          // Update flags based on the role information
          if (roleInfo.isSuperUser !== undefined) {
            this.isSuperUser = roleInfo.isSuperUser;
          }

          if (roleInfo.isAdmin !== undefined) {
            this.isAdmin = roleInfo.isAdmin;
          } else if (roleInfo.roleName) {
            // Fallback to checking role name
            this.isAdmin = roleInfo.roleName.toLowerCase() === 'admin';
          }

          console.log('Updated user role flags from getUserRole:', {
            isSuperUser: this.isSuperUser,
            isAdmin: this.isAdmin,
          });

          // Update admin access status
          this.updateAdminAccessStatus();
        }
      },
      error: (err) => {
        console.error('Error fetching user role from getUserRole:', err);
      },
    });
  }

  /**
   * Updates the admin access status and caches the result
   * This should be called whenever user role information changes
   */
  updateAdminAccessStatus(): void {
    // Get role name from userDetails if available
    const roleName = this.userDetails?.role?.name || '';

    // Check if role name is 'admin' (case-insensitive)
    const isAdminByRoleName = roleName.toLowerCase() === 'admin';

    // Check if user has admin access through any method
    this.hasAdminAccess = this.isSuperUser || this.isAdmin || isAdminByRoleName;

    // Log only once when the status is updated
    console.log('Admin access check details:', {
      isSuperUser: this.isSuperUser,
      isAdmin: this.isAdmin,
      roleName: roleName,
      isAdminByRoleName: isAdminByRoleName,
      finalResult: this.hasAdminAccess,
    });
  }

  // Check if user can access user management
  // This method now returns the cached result without logging
  canAccessUserManagement(): boolean {
    return this.hasAdminAccess;
  }
}

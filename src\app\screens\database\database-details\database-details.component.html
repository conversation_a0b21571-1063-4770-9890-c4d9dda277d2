<div>
  <!-- Table View Modal -->
  <input class="modal-state" id="table-view-modal" type="checkbox" />
  <div class="modal w-screen">
    <label class="modal-overlay" for="table-view-modal"></label>
    <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
      <!-- Loading overlay -->
      <div
        *ngIf="loading"
        class="absolute inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 rounded-lg"
      >
        <div class="flex flex-col items-center">
          <div
            class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"
          ></div>
          <p class="text-white mt-4">Loading table information...</p>
        </div>
      </div>

      <div class="modal-header">
        <h2 class="text-xl">Table Information</h2>
        <label for="table-view-modal" class="modal-close-btn">
          <i class="ti ti-x"></i>
        </label>
      </div>

      <div class="p-4 overflow-auto max-h-[70vh]">
        <div *ngIf="selectedTableInfo">
          <!-- Object Details Section -->
          <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="form-field">
              <label class="form-label">Object Name</label>
              <div class="input input-lg bg-backgroundSecondary">
                {{ selectedTableInfo.objectName }}
              </div>
            </div>
            <div class="form-field">
              <label class="form-label">Object Type</label>
              <div class="input input-lg bg-backgroundSecondary">
                {{ selectedTableInfo.objectType }}
              </div>
            </div>
            <div class="form-field">
              <label class="form-label">Transformation Status</label>
              <div
                class="input input-lg bg-backgroundSecondary flex items-center"
              >
                <span
                  class="badge mr-2"
                  [ngClass]="{
                    'badge-flat-success':
                      selectedTableInfo.transformed_into !== 'not transformed',
                    'badge-flat-error':
                      selectedTableInfo.transformed_into === 'not transformed'
                  }"
                >
                  {{
                    selectedTableInfo.transformed_into === "not transformed"
                      ? "Not Transformed"
                      : "Transformed"
                  }}
                </span>
              </div>
            </div>
            <div class="form-field">
              <label class="form-label">Documentation Status</label>
              <div
                class="input input-lg bg-backgroundSecondary flex items-center"
              >
                <span
                  class="badge mr-2"
                  [ngClass]="{
                    'badge-flat-success': selectedTableInfo.doc === 'generated',
                    'badge-flat-warning': selectedTableInfo.doc !== 'generated'
                  }"
                >
                  {{
                    selectedTableInfo.doc === "generated"
                      ? "Generated"
                      : "Not Generated"
                  }}
                </span>
              </div>
            </div>
            <div class="form-field" *ngIf="selectedTableInfo.description">
              <label class="form-label">Description</label>
              <div class="input input-lg bg-backgroundSecondary">
                {{
                  selectedTableInfo.description || "No description available"
                }}
              </div>
            </div>
            <div class="form-field" *ngIf="selectedTableInfo.created_at">
              <label class="form-label">Created At</label>
              <div class="input input-lg bg-backgroundSecondary">
                {{ selectedTableInfo.created_at | date : "medium" }}
              </div>
            </div>
          </div>

          <!-- Action Buttons Section -->
          <div class="flex gap-3 justify-end mb-6">
            <!-- Download Documentation Button - always allow downloading existing documents -->
            <button
              class="btn btn-outline-primary btn-sm rounded-md"
              *ngIf="
                selectedTableInfo.doc === 'generated' && canDownloadDocument
              "
              (click)="downloadDocumentation(selectedTableInfo.objectName)"
            >
              <i class="ti ti-download text-base pr-2"></i>
              Download Documentation
            </button>

            <!-- Generate Documentation Button -->
            <button
              class="btn btn-outline-primary btn-sm rounded-md"
              *ngIf="
                selectedTableInfo.doc !== 'generated' &&
                !generatingDocs[selectedTableInfo.objectName] &&
                canGenerateDocument &&
                isConnected
              "
              (click)="generateDoc(selectedTableInfo.objectName)"
            >
              <i class="ti ti-file-text text-base pr-2"></i>
              Generate Documentation
            </button>

            <!-- Disabled Generate Documentation Button when disconnected -->
            <button
              class="btn btn-outline-primary btn-sm rounded-md cursor-not-allowed opacity-60"
              *ngIf="
                selectedTableInfo.doc !== 'generated' &&
                !generatingDocs[selectedTableInfo.objectName] &&
                canGenerateDocument &&
                !isConnected
              "
              disabled
              title="Database must be connected to generate documentation"
            >
              <i class="ti ti-file-text text-base pr-2"></i>
              Generate Documentation
            </button>

            <!-- Generating Documentation Loading Button -->
            <button
              class="btn btn-outline-primary btn-sm rounded-md disabled"
              *ngIf="generatingDocs[selectedTableInfo.objectName]"
            >
              <i class="ti ti-loader animate-spin text-base pr-2"></i>
              Generating...
            </button>

            <!-- View Code Button - always allow viewing existing transformed code, regardless of connection status -->
            <button
              class="btn btn-primary btn-sm rounded-md"
              *ngIf="
                selectedTableInfo.transformed_into !== 'not transformed' &&
                canViewCode
              "
              (click)="
                goToCodeReview(
                  selectedTableInfo.objectName,
                  selectedTableInfo.objectType
                )
              "
            >
              <i class="ti ti-code text-base pr-2"></i>
              View Code
            </button>

            <!-- AI Transform Button -->
            <label
              class="btn btn-primary btn-sm rounded-md"
              for="transform"
              *ngIf="canTransformCode && isConnected"
              (click)="
                openTransformModal(
                  selectedTableInfo.objectName,
                  selectedTableInfo.objectType
                )
              "
            >
              <i class="ti ti-input-ai text-base pr-2"></i>
              AI Transform
            </label>

            <!-- Disabled AI Transform Button when disconnected -->
            <button
              class="btn btn-primary btn-sm rounded-md cursor-not-allowed opacity-60"
              *ngIf="canTransformCode && !isConnected"
              disabled
              title="Database must be connected to transform code"
            >
              <i class="ti ti-input-ai text-base pr-2"></i>
              AI Transform
            </button>
          </div>

          <!-- Error Message -->
          <div
            *ngIf="selectedTableInfo.error"
            class="bg-error bg-opacity-10 p-4 rounded-lg border border-error mb-4"
          >
            <div class="flex items-start">
              <i class="ti ti-alert-triangle text-error text-2xl mr-3 mt-1"></i>
              <div>
                <h3 class="text-lg font-semibold text-error mb-2">Error</h3>
                <p class="text-content1">
                  {{
                    selectedTableInfo.errorMessage ||
                      "An error occurred while fetching object details."
                  }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer Buttons -->
        <div class="flex justify-between mt-4">
          <!-- Direct link to code review page if code is available -->
          <div
            *ngIf="
              selectedTableInfo &&
              selectedTableInfo.transformed_into !== 'not transformed' &&
              canViewCode
            "
          >
            <a
              [routerLink]="['/database', connectionName, 'code review']"
              [queryParams]="{
                id: getStoredDatabaseId(),
                objectName: selectedTableInfo.objectName,
                objectType: selectedTableInfo.objectType.toLowerCase()
              }"
              class="btn btn-primary"
              (click)="closeTableViewModal()"
            >
              <i class="ti ti-code text-base pr-2"></i>
              Go to Code Review
            </a>
          </div>
          <div class="flex-grow"></div>
          <label for="table-view-modal" class="btn btn-outline-default">
            Close
          </label>
        </div>
      </div>
    </div>
  </div>

  <!-- Stats Cards Row -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-2 mb-4">
    <app-stats
      title="Objects"
      icon="ti ti-circle-check"
      iconSize="text-xl"
      iconColor="text-[#2CCE8A]"
      [onlyHeader]="true"
      [badge]="data?.objects"
      [attr.data-theme]="getTheme()"
    >
    </app-stats>

    <app-stats
      title="Transformed"
      icon="ti ti-circle-check"
      [badge]="data?.transformed"
      iconSize="text-xl"
      iconColor="text-[#2CCE8A]"
      [onlyHeader]="true"
      [attr.data-theme]="getTheme()"
    >
    </app-stats>
    <app-stats
      title="Pending"
      icon="ti ti-circle-check"
      [badge]="data?.pending"
      iconSize="text-xl"
      iconColor="text-[#2CCE8A]"
      [onlyHeader]="true"
      [attr.data-theme]="getTheme()"
    >
    </app-stats>
    <app-stats
      title="Deployed"
      icon="ti ti-circle-check"
      badge="0"
      iconSize="text-xl"
      iconColor="text-[#2CCE8A]"
      [onlyHeader]="true"
      [attr.data-theme]="getTheme()"
    >
    </app-stats>
  </div>

  <div class="mb-4">
    <!-- Header and Dependency Chart Button -->

    <!-- Search and Filter Bar -->
    <div class="flex flex-col justify-between md:flex-row gap-3 py-3 mb-4">
      <!-- Filters -->
      <div class="flex flex-row gap-3">
        <!-- Object Type Filter -->
        <h2 class="text-xl font-semibold">Database Objects</h2>
        <div class="form-control min-w-[200px]">
          <select
            class="select select-sm"
            [(ngModel)]="objectTypeFilter"
            (change)="currentPage = 1"
          >
            <option value="all">All Object Types</option>
            <option *ngFor="let type of uniqueObjectTypes" [value]="type">
              {{ type }}
            </option>
          </select>
        </div>

        <!-- Transform Status Filter -->
        <div class="form-control min-w-[200px]">
          <select
            class="select select-sm"
            [(ngModel)]="transformStatusFilter"
            (change)="currentPage = 1"
          >
            <option value="all">All Statuses</option>
            <option value="transformed">Transformed</option>
            <option value="not_transformed">Not Transformed</option>
          </select>
        </div>

        <!-- Reset Filters Button -->
        <button
          class="btn btn-sm btn-outline-secondary"
          *ngIf="
            searchTerm != '' ||
            objectTypeFilter != 'all' ||
            transformStatusFilter != 'all' ||
            currentPage > 1
          "
          (click)="resetFilters()"
        >
          Reset Filters
        </button>
      </div>

      <!-- Search Bar -->
      <div class="flex flex-row gap-3">
        <div class="form-control relative w-[270px]">
          <input
            type="text"
            class="input input-sm max-w-full pl-8"
            placeholder="Search by object name or type..."
            [(ngModel)]="searchTerm"
            (input)="currentPage = 1"
          />
          <span class="absolute inset-y-0 left-3 inline-flex items-center">
            <i class="ti ti-search"></i>
          </span>
        </div>
        <label
          class="btn btn-primary btn-sm rounded-md"
          for="connect-DB"
          (click)="goToDependencyChart()"
          ><i class="ti ti-binary-tree mr-2"></i>View Dependency Chart
        </label>
      </div>
    </div>
    <div *ngIf="loading" class="flex justify-center my-8">
      <div
        class="absolute animate-spin rounded-full h-8 w-8 border-b-2 border-primary top-[50%]"
      ></div>
    </div>
    <!-- Table implementation -->
    <div class="enhanced-table rounded-lg border shadow-sm" *ngIf="!loading">
      <div class="flex flex-col">
        <!-- Table with horizontal scroll and fixed columns -->
        <div class="relative overflow-x-auto">
          <table
            class="table table-compact table-hover w-full border-collapse custom-table border border-[var(--border-color)]"
            style="min-width: 1000px"
          >
            <thead>
              <tr>
                <!-- Dynamic Columns -->
                <th
                  *ngFor="let col of columns; let i = index; let isLast = last"
                  class="text-left font-medium text-content1 border-b border-[var(--border-color)] whitespace-nowrap"
                  [ngClass]="{
                    'border-r': !isLast,
                    'sticky-left': i === 0,
                    'sticky-right': col.custom && col.key === 'action'
                  }"
                  [style.min-width]="
                    i === 0
                      ? '200px'
                      : col.key === 'objectType'
                      ? '150px'
                      : col.key === 'businessDoc'
                      ? '140px'
                      : col.key === 'action'
                      ? '220px'
                      : '120px'
                  "
                  (click)="!col.custom && sort(col.key)"
                >
                  <div class="flex justify-between border-none">
                    <span
                      class="border-none truncate max-w-[150px] text-adaptive-primary"
                      >{{ col.label }}</span
                    >
                    <span
                      *ngIf="sortColumn === col.key"
                      class="border-none ml-1 text-adaptive-primary"
                    >
                      {{ sortAsc ? "↑" : "↓" }}
                    </span>
                  </div>
                </th>
              </tr>
            </thead>

            <tbody>
              <tr
                *ngFor="let item of paginatedData; let i = index"
                class="cursor-pointer border-b transition-all duration-200"
                [ngStyle]="{ 'animation-delay': i * 0.05 + 's' }"
                style="animation: fadeInRow 0.5s ease-out forwards"
              >
                <!-- Regular columns -->
                <ng-container *ngFor="let col of columns">
                  <!-- Regular column -->
                  <td
                    *ngIf="!col.custom && col.key === 'objectName'"
                    class="sticky-left whitespace-nowrap"
                    style="min-width: 200px"
                  >
                    <span
                      class="font-medium text-primary block cursor-pointer hover:underline"
                      [title]="item[col.key]"
                      (click)="viewTableInfo(item.objectName, item.objectType)"
                    >
                      {{ item[col.key] }}
                    </span>
                  </td>

                  <td
                    *ngIf="!col.custom && col.key !== 'objectName'"
                    class="whitespace-nowrap"
                    [style.min-width]="
                      col.key === 'objectType' ? '150px' : '120px'
                    "
                  >
                    <span
                      class="truncate block max-w-[150px] text-adaptive-primary"
                      [title]="item[col.key]"
                    >
                      {{ item[col.key] }}
                    </span>
                  </td>

                  <!-- Custom template columns -->
                  <ng-container *ngIf="col.custom">
                    <ng-container [ngSwitch]="col.key">
                      <ng-container *ngSwitchCase="'dependencyChart'">
                        <ng-container
                          *ngTemplateOutlet="
                            dependencyChartTemplate;
                            context: { $implicit: item }
                          "
                        ></ng-container>
                      </ng-container>

                      <ng-container *ngSwitchCase="'businessDoc'">
                        <ng-container
                          *ngTemplateOutlet="
                            businessDocTemplate;
                            context: { $implicit: item }
                          "
                        ></ng-container>
                      </ng-container>

                      <ng-container *ngSwitchCase="'code'">
                        <ng-container
                          *ngTemplateOutlet="
                            codeTemplate;
                            context: { $implicit: item }
                          "
                        ></ng-container>
                      </ng-container>

                      <ng-container *ngSwitchCase="'deployed'">
                        <ng-container
                          *ngTemplateOutlet="
                            deployedTemplate;
                            context: { $implicit: item }
                          "
                        ></ng-container>
                      </ng-container>

                      <ng-container
                        *ngSwitchCase="'action'"
                        style="min-width: 220px"
                      >
                        <ng-container
                          *ngTemplateOutlet="
                            actionTemplate;
                            context: { $implicit: item }
                          "
                        ></ng-container>
                      </ng-container>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </tr>

              <!-- Empty state row -->
              <tr *ngIf="paginatedData.length === 0">
                <td
                  [attr.colspan]="columns.length"
                  class="text-center py-4 text-adaptive-primary"
                >
                  No database objects found matching your filters.
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div
          class="flex justify-between items-center bg-backgroundPrimary p-4 border-t border-[var(--border-color)]"
        >
          <!-- Left: Pagination Info & Page Size Selection -->
          <div class="flex flex-row items-center space-x-4">
            <div
              class="flex items-center px-3 py-1.5 bg-backgroundPrimary rounded-md border border-[var(--border-color)]"
            >
              <i class="ti ti-list text-primary mr-2"></i>
              <h2 class="text-sm font-medium text-content2 whitespace-nowrap">
                Showing
                <span class="text-content1 font-semibold">{{
                  currentPageStart
                }}</span>
                to
                <span class="text-content1 font-semibold">{{
                  currentPageEnd
                }}</span>
                of
                <span class="text-content1 font-semibold">{{
                  totalEntries
                }}</span>
                objects
              </h2>
            </div>
            <div class="relative dropdown" style="width: 120px">
              <div
                class="flex items-center bg-backgroundPrimary rounded-md border border-[var(--border-color)]"
              >
                <select
                  class="select select-sm w-full rounded-md bg-backgroundPrimary text-content1 border-0 pl-3 pr-8 transition-all focus:ring-2 focus:ring-primary/20"
                  [(ngModel)]="pageSize"
                  (change)="changePageSize($event)"
                  style="
                    appearance: none;
                    -webkit-appearance: none;
                    -moz-appearance: none;
                  "
                >
                  <option *ngFor="let size of pageSizeOptions" [value]="size">
                    {{ size }} per page
                  </option>
                </select>
                <div
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-content2"
                ></div>
              </div>
            </div>
          </div>

          <!-- Right: Pagination Controls -->
          <div class="pagination flex items-center">
            <div
              class="flex items-center rounded-md bg-backgroundPrimary border border-[var(--border-color)] p-0.5"
            >
              <button
                class="btn btn-sm h-8 min-h-0 btn-ghost flex items-center text-content2 px-2 transition-all hover:bg-primary/10 hover:text-primary rounded-l-md rounded-r-none border-r border-[var(--border-color)]"
                (click)="prevPage()"
                [disabled]="currentPage === 1"
              >
                <span class="flex items-center"
                  ><i class="ti ti-chevron-left mr-1"></i>Prev</span
                >
              </button>

              <!-- Page numbers for larger screens -->
              <div class="hidden md:flex items-center px-1">
                <ng-container *ngIf="totalPages <= 7">
                  <button
                    *ngFor="let page of pageNumbers"
                    class="btn btn-sm h-7 min-h-0 w-7 p-0 mx-0.5 transition-all"
                    [ngClass]="{
                      'btn-primary': currentPage === page,
                      'btn-ghost ': currentPage !== page
                    }"
                    (click)="goToPage(page)"
                  >
                    {{ page }}
                  </button>
                </ng-container>

                <ng-container *ngIf="totalPages > 7">
                  <!-- First page -->
                  <button
                    *ngIf="currentPage > 3"
                    class="btn btn-sm h-7 min-h-0 w-7 p-0 mx-0.5 btn-ghost text-content2 transition-all hover:bg-primary/10 hover:text-primary"
                    (click)="goToPage(1)"
                  >
                    1
                  </button>

                  <!-- Ellipsis if needed -->
                  <span *ngIf="currentPage > 4" class="px-1 text-content2"
                    >...</span
                  >

                  <!-- Pages around current page -->
                  <ng-container *ngFor="let page of getVisiblePages()">
                    <button
                      class="btn btn-sm h-7 min-h-0 w-7 p-0 mx-0.5 transition-all"
                      [ngClass]="{
                        'btn-primary bg-primary': currentPage === page,
                        'btn-ghost': currentPage !== page
                      }"
                      (click)="goToPage(page)"
                    >
                      {{ page }}
                    </button>
                  </ng-container>
                </ng-container>
              </div>

              <!-- Simple page indicator for mobile -->
              <div class="md:hidden px-3 flex items-center">
                <span class="text-content2"
                  >Page {{ currentPage }} of {{ totalPages }}</span
                >
              </div>

              <button
                class="btn btn-sm h-8 min-h-0 btn-ghost flex items-center text-content2 px-2 transition-all hover:bg-primary/10 hover:text-primary rounded-r-md rounded-l-none border-l border-[var(--border-color)]"
                (click)="nextPage()"
                [disabled]="currentPage === totalPages"
              >
                <span class="flex items-center"
                  >Next<i class="ti ti-chevron-right ml-1"></i
                ></span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Templates for custom columns -->
  <ng-template #dependencyChart let-item>
    <td style="min-width: 120px">
      <label
        class="btn btn-outline btn-sm rounded-md"
        (click)="goToDependencyChart(item.objectType, item.objectName)"
        title="Open dependency chart for this object"
      >
        Open
      </label>
    </td>
  </ng-template>

  <ng-template #code let-item>
    <td style="min-width: 120px">
      <div class="flex gap-2">
        <!-- Always allow viewing existing transformed code, regardless of connection status -->
        <a
          class="btn btn-outline btn-sm rounded-md"
          [routerLink]="['/database', connectionName, 'code review']"
          [queryParams]="{
            id: getStoredDatabaseId(),
            objectName: item.objectName,
            objectType: item.objectType.toLowerCase()
          }"
          *ngIf="item.transformed_into != 'not transformed' && canViewCode"
          ><i class="ti ti-code text-base pr-2"></i> Open
        </a>

        <!-- No permission message -->
        <span
          class="text-content2 text-sm"
          *ngIf="item.transformed_into != 'not transformed' && !canViewCode"
        >
          <i class="ti ti-eye mr-1"></i> View only
        </span>
      </div>
      <span
        class="capitalize badge badge-flat-error"
        *ngIf="item.transformed_into == 'not transformed'"
        >{{ item.transformed_into }}</span
      >
    </td>
  </ng-template>

  <ng-template #businessDoc let-item>
    <td style="min-width: 140px">
      <div class="flex gap-2">
        <!-- When doc is not generated and not currently generating -->
        <button
          class="btn btn-sm btn-outline-primary"
          (click)="generateDoc(item.objectName)"
          *ngIf="
            item.doc != 'generated' &&
            !generatingDocs[item.objectName] &&
            canGenerateDocument &&
            isConnected
          "
        >
          Generate
        </button>

        <!-- Loading state when generating -->
        <button
          class="btn btn-sm btn-outline-primary disabled"
          *ngIf="generatingDocs[item.objectName]"
        >
          Generating...
        </button>

        <!-- When doc is generated - always allow downloading existing documents -->
        <button
          class="btn btn-sm btn-outline"
          *ngIf="item.doc == 'generated' && canDownloadDocument"
          (click)="downloadDocumentation(item.objectName)"
        >
          <i class="ti ti-download mr-1"></i> Download
        </button>

        <!-- Disconnected message -->
        <span
          class="text-content2 text-sm"
          *ngIf="
            item.doc != 'generated' &&
            !generatingDocs[item.objectName] &&
            canGenerateDocument &&
            !isConnected
          "
        >
          Database disconnected
        </span>

        <!-- No permission message -->
        <span
          class="text-content2 text-sm"
          *ngIf="
            item.doc != 'generated' &&
            !canGenerateDocument &&
            !generatingDocs[item.objectName]
          "
        >
          No permission
        </span>

        <!-- No permission message for download -->
        <span
          class="text-content2 text-sm"
          *ngIf="item.doc == 'generated' && !canDownloadDocument"
        >
          No permission
        </span>
      </div>
    </td>
  </ng-template>

  <ng-template #deployed let-item>
    <td style="min-width: 120px">
      <span class="capitalize badge badge-flat-warning" *ngIf="!item.deployed"
        >Pending</span
      >
    </td>
  </ng-template>

  <ng-template #actionTemplate let-item>
    <td class="sticky-right whitespace-nowrap" style="min-width: 220px">
      <!-- Deployment after transformed -->
      <div class="flex gap-2 justify-end">
        <!-- View button - always visible -->
        <button
          class="btn btn-outline-primary btn-sm rounded-md"
          (click)="viewTableInfo(item.objectName, item.objectType)"
        >
          <i class="ti ti-eye text-base pr-2"></i>
          View
        </button>

        <!-- Debug button - temporary for troubleshooting -->
        <button
          class="btn btn-outline-warning btn-sm rounded-md"
          (click)="getTransformButtonDebugInfo()"
          title="Debug transform button state"
        >
          <i class="ti ti-bug text-base pr-2"></i>
          Debug
        </button>

        <!-- AI Transform button - only visible with transform_code permission and when connected -->
        <label
          class="btn btn-primary btn-sm rounded-md"
          for="transform"
          *ngIf="canTransformCode && isConnected"
          (click)="openTransformModal(item.objectName, item.objectType)"
          ><i class="ti ti-input-ai text-base pr-2"></i>
          AI Transform
        </label>

        <!-- Disabled AI Transform button when disconnected -->
        <button
          class="btn btn-primary btn-sm rounded-md cursor-not-allowed opacity-60"
          *ngIf="canTransformCode && !isConnected"
          disabled
          title="Database must be connected to transform code"
        >
          <i class="ti ti-input-ai text-base pr-2"></i>
          AI Transform
        </button>

        <!-- Debug info - temporary for troubleshooting -->
        <div
          class="text-xs text-gray-500 mt-1"
          *ngIf="!canTransformCode || !isConnected"
        >
          Debug: canTransform={{ canTransformCode }}, connected={{
            isConnected
          }}, permissions={{ userPermissions | json }}
        </div>

        <!-- Deploy button - only visible with deploy_code permission and when connected -->
        <button
          class="btn btn-outline-default btn-sm"
          *ngIf="canDeployCode && isConnected"
        >
          Deploy
        </button>

        <!-- Disabled Deploy button when disconnected -->
        <button
          class="btn btn-outline-default btn-sm cursor-not-allowed opacity-60"
          *ngIf="canDeployCode && !isConnected"
          disabled
          title="Database must be connected to deploy"
        >
          Deploy
        </button>
      </div>
    </td>
  </ng-template>

  <!-- Transform Modal -->
  <input class="modal-state" id="transform" type="checkbox" />
  <div class="modal w-screen">
    <label class="modal-overlay"></label>
    <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
      <div class="modal-header">
        <h2 class="text-xl text-adaptive-primary">
          AI Transform: {{ selectedObjectName }}
        </h2>
        <label for="transform" class="modal-close-btn"
          ><i class="ti ti-x"></i
        ></label>
      </div>

      <!-- Legacy loading state for backward compatibility - hidden now -->
      <div *ngIf="loading && !isLoading" class="hidden">
        <div class="spinner"></div>
      </div>

      <div
        class="py-3 flex w-100 items-center justify-center border-b"
        *ngIf="!success && !isLoading"
      >
        <app-stepper
          [steps]="dynamicSteps"
          [activeStep]="stepIndex"
          [completedSteps]="completedSteps"
        ></app-stepper>
      </div>

      <!-- Loading State (inside modal body) -->
      <div class="p-3 py-2 max-h-[80%] overflow-auto" *ngIf="isLoading">
        <div
          class="flex flex-col justify-center items-center p-8 gap-4 animate-fade-in"
        >
          <!-- Colorful loader with gradient ring -->
          <div class="relative">
            <!-- Outer gradient ring with animation -->
            <div
              class="w-20 h-20 rounded-full bg-gradient-to-r from-primary via-secondary to-primary p-1 animate-spin-slow"
            >
              <!-- Inner background -->
              <div
                class="w-full h-full rounded-full bg-backgroundPrimary flex items-center justify-center"
              >
                <!-- Spinner -->
                <div
                  class="loading loading-spinner loading-md text-primary"
                ></div>
              </div>
            </div>

            <!-- Decorative elements -->
            <div class="absolute -top-2 -right-2 animate-bounce-slow">
              <div
                class="w-6 h-6 rounded-full bg-secondary flex items-center justify-center text-content1"
              >
                <i class="ti ti-sparkles text-xs"></i>
              </div>
            </div>

            <div class="absolute -bottom-1 -left-1 animate-pulse">
              <div
                class="w-5 h-5 rounded-full bg-primary flex items-center justify-center text-content1"
              >
                <i class="ti ti-brain text-xs"></i>
              </div>
            </div>

            <div class="absolute top-1 -left-2 animate-ping-slow">
              <div
                class="w-4 h-4 rounded-full bg-info flex items-center justify-center text-content1"
              >
                <i class="ti ti-bulb text-xs"></i>
              </div>
            </div>
          </div>

          <!-- Colorful progress indicator -->
          <div
            class="w-48 bg-backgroundSecondary rounded-full h-2.5 mt-2 overflow-hidden"
          >
            <div class="h-full w-full relative">
              <!-- Gradient progress bar -->
              <div
                class="absolute inset-0 bg-gradient-to-r from-primary via-secondary to-info animate-[progress_2s_ease-in-out_infinite]"
              ></div>
              <!-- Shimmer effect -->
              <div
                class="absolute inset-0 bg-gradient-to-r from-transparent via-content1 to-transparent opacity-20 animate-[shimmer_1.5s_ease-in-out_infinite]"
              ></div>
            </div>
          </div>

          <!-- Enhanced status text with gradient -->
          <div class="text-center">
            <div
              class="inline-block bg-gradient-to-r from-primary to-secondary text-transparent bg-clip-text"
            >
              <h3 class="text-xl font-bold">Transforming Code</h3>
            </div>
            <div class="flex items-center justify-center gap-1 mt-1">
              <p class="text-content2 text-sm">
                Processing {{ selectedObjectName }}
              </p>
              <div class="flex">
                <span class="text-primary animate-bounce-slow delay-100"
                  >.</span
                >
                <span class="text-secondary animate-bounce-slow delay-200"
                  >.</span
                >
                <span class="text-info animate-bounce-slow delay-300">.</span>
              </div>
            </div>
          </div>

          <!-- Show additional message if loading takes too long -->
          <div
            *ngIf="longLoadingMessage"
            class="mt-2 text-center max-w-md p-3 bg-backgroundSecondary rounded-lg border border-borderColor animate-fade-in"
          >
            <div class="flex items-center gap-2 mb-2">
              <i class="ti ti-info-circle text-primary"></i>
              <p class="text-content1 text-sm font-medium">
                Transformation in Progress
              </p>
            </div>
            <p class="text-content2 text-sm">
              Complex code transformations require deeper analysis to generate
              accurate results.
            </p>
            <p class="text-content2 text-xs mt-2 opacity-75">
              This may take a few moments to complete.
            </p>
          </div>
        </div>
      </div>

      <div
        class="p-3 py-2 max-h-[80%] overflow-auto"
        *ngIf="!success && !isLoading && !errorState"
      >
        <div *ngIf="stepIndex === 0">
          <!-- Step 1: Select API or DB Code -->
          <div class="mt-4">
            <div class="mt-2 flex gap-3 justify-center">
              <!-- API Option -->
              <label
                for="api"
                class="card p-4 rounded-lg border transition-all duration-300 cursor-pointer border-t-4"
                [ngClass]="{
                  'border-primary shadow-lg ': transformToApi,
                  'border-gray-300 text-gray-500 shadow-none ': !transformToApi
                }"
              >
                <input
                  type="checkbox"
                  id="api"
                  class="hidden"
                  [(ngModel)]="transformToApi"
                />
                <span class="text-adaptive-primary">API</span>
                <i
                  class="ti"
                  [ngClass]="
                    transformToApi
                      ? 'ti-check text-primary'
                      : 'ti-database text-content3'
                  "
                ></i>
              </label>

              <!-- DB Code Objects Option -->
              <label
                for="rdbms"
                class="card p-4 rounded-lg border transition-all duration-300 cursor-pointer border-t-4"
                [ngClass]="{
                  'border-primary shadow-lg ': transformToRdbms,
                  'border-gray-300 text-gray-500 shadow-none ':
                    !transformToRdbms
                }"
              >
                <input
                  type="checkbox"
                  id="rdbms"
                  class="hidden"
                  [(ngModel)]="transformToRdbms"
                />
                <span class="text-adaptive-primary">DB Code Objects</span>
                <i
                  class="ti"
                  [ngClass]="
                    transformToRdbms
                      ? 'ti-check text-primary'
                      : 'ti-database text-content3'
                  "
                ></i>
              </label>
            </div>
          </div>
        </div>

        <!-- Step 2: Select Database Type -->
        <div id="Step2" *ngIf="stepIndex === 1 && transformToRdbms">
          <div class="form-control relative w-full border-1 mb-3">
            <input
              type="text"
              [(ngModel)]="searchDB"
              class="input input-lg max-w-full pl-10"
              placeholder="Search"
            />
            <span class="absolute inset-y-0 left-3 inline-flex items-center">
              <i class="ti ti-search"></i>
            </span>
          </div>

          <div class="grid grid-cols-3 gap-3">
            <app-select-card
              *ngFor="let db of getFilteredDBList()"
              [isSelected]="selectedCard === db.id"
              (selected)="setSelectedCard(db.id, db.key)"
            >
              <img
                src="{{ db.icon }}"
                class="max-h-[45px] h-auto w-auto object-contain"
              />
              <p class="text-center font-semibold text-adaptive-primary">
                {{ db.name }}
              </p>
            </app-select-card>
          </div>
        </div>

        <!-- Step 3: Specify Additional Instructions -->
        <div class="" *ngIf="stepIndex === 2">
          <div class="relative w-full mb-3">
            <!-- Search Input -->
            <input
              type="text"
              [(ngModel)]="searchTerm"
              class="input input-lg max-w-full pl-10"
              placeholder="Search"
            />
            <span class="absolute inset-y-0 left-3 inline-flex items-center">
              <i class="ti ti-search"></i>
            </span>
          </div>

          <!-- Multi-Select Options -->
          <div class="mt-2 flex flex-col w-full gap-3 justify-center">
            <ng-container *ngFor="let item of filteredOptions">
              <label
                class="card p-2 rounded-lg border transition-all duration-300 cursor-pointer border-l-4 flex flex-row gap-2 min-w-full"
                [ngClass]="{
                  'border-primary shadow-lg bg-blue-50':
                    selectedOptions.includes(
                      item.title + ' ' + item.description
                    ),
                  'border-gray-300 text-gray-500 shadow-none':
                    !selectedOptions.includes(
                      item.title + ' ' + item.description
                    )
                }"
                [title]="item.title + ': ' + item.description"
              >
                <input
                  type="checkbox"
                  class="hidden"
                  [value]="item"
                  (change)="toggleSelection(item)"
                />
                <i
                  class="ti pt-1"
                  [ngClass]="
                    selectedOptions.includes(
                      item.title + ' ' + item.description
                    )
                      ? 'ti-check text-primary'
                      : 'ti-database text-content3'
                  "
                ></i>
                <div class="flex flex-col gap-1">
                  <span class="font-medium text-adaptive-primary">{{
                    item.title
                  }}</span>
                  <span
                    class="text-content2 text-sm line-clamp-1 text-adaptive-secondary"
                    >{{ item.description }}</span
                  >
                  <span
                    class="badge badge-xs col-auto text-wrap w-fit px-2"
                    [ngClass]="{
                      'badge-outline-primary': selectedOptions.includes(
                        item.title + ' ' + item.description
                      ),
                      'badge-outline': !selectedOptions.includes(
                        item.title + ' ' + item.description
                      )
                    }"
                    >{{ item.type }}</span
                  >
                </div>
              </label>
            </ng-container>
          </div>
        </div>
      </div>

      <!-- Success Message -->
      <div class="p-3 py-2 max-h-[80%] overflow-auto" *ngIf="success">
        <div class="animate-fade-in">
          <div class="flex items-center justify-center mb-4">
            <div class="relative">
              <!-- Success checkmark with animated circle -->
              <div
                class="w-16 h-16 rounded-full bg-success bg-opacity-20 flex items-center justify-center"
              >
                <i class="ti ti-check text-3xl text-success"></i>
              </div>

              <!-- Animated ring -->
              <div
                class="absolute inset-0 rounded-full border-4 border-success animate-[ping_1s_ease-out]"
              ></div>
            </div>
          </div>

          <div
            class="success-message bg-success bg-opacity-10 p-4 rounded-lg border border-success"
          >
            <div class="flex items-start">
              <i class="ti ti-circle-check text-success text-2xl mr-3 mt-1"></i>
              <div>
                <h3 class="text-lg font-semibold text-success mb-2">
                  Success!
                </h3>
                <p class="text-content1">{{ successMessage }}</p>
                <p class="text-content2 text-sm mt-2">
                  You can now view the transformed code or continue working with
                  other objects.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div class="p-3 py-2 max-h-[80%] overflow-auto" *ngIf="errorState">
        <div class="animate-fade-in">
          <div class="flex items-center justify-center mb-4">
            <div class="relative">
              <!-- Error icon with animated circle -->
              <div
                class="w-16 h-16 rounded-full bg-error bg-opacity-20 flex items-center justify-center"
              >
                <i class="ti ti-alert-triangle text-2xl text-error"></i>
              </div>

              <!-- Animated ring -->
              <div
                class="absolute inset-0 rounded-full border-4 border-error animate-[ping_1s_ease-out]"
              ></div>
            </div>
          </div>

          <div
            class="error-message bg-error bg-opacity-10 p-4 rounded-lg border border-error"
          >
            <div class="flex items-start">
              <i class="ti ti-alert-triangle text-error text-2xl mr-3 mt-1"></i>
              <div>
                <h3 class="text-lg font-semibold text-error mb-2">Error</h3>
                <p class="text-content1">{{ errorMessage }}</p>
                <div
                  *ngIf="errorDetails"
                  class="mt-2 p-3 bg-backgroundSecondary rounded-md border border-borderColor"
                >
                  <p class="text-content2 text-sm">{{ errorDetails }}</p>
                </div>
                <p class="text-content2 text-sm mt-2">
                  Please try again or contact support if the issue persists.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer Buttons -->
      <div class="flex gap-3 p-3 border-t justify-end">
        <!-- Buttons for steps (when not in success or error state and not loading) -->
        <div
          class="flex gap-3"
          id="step-btns"
          *ngIf="!success && !errorState && !isLoading"
        >
          <button *ngIf="stepIndex > 0" (click)="prevStep()" class="btn btn-sm">
            Back
          </button>

          <button
            *ngIf="stepIndex < 2"
            (click)="nextStep()"
            class="btn btn-primary btn-sm"
          >
            Next
          </button>

          <button
            *ngIf="
              stepIndex === 2 && selectedOptions.length > 0 && canTransformCode
            "
            class="btn btn-primary btn-sm"
            (click)="transformCode()"
          >
            <i class="ti ti-input-ai text-base pr-2"></i>Transform
          </button>

          <button
            *ngIf="
              stepIndex === 2 &&
              selectedOptions.length === 0 &&
              canTransformCode
            "
            class="btn btn-primary btn-sm"
            (click)="transformCode()"
          >
            <i class="ti ti-input-ai text-base pr-2"></i>Skip & Transform
          </button>
        </div>

        <!-- Buttons for loading state -->
        <div class="flex gap-3" id="loading-btns" *ngIf="isLoading">
          <button class="btn btn-sm btn-outline" disabled>
            <span class="loading loading-spinner loading-xs mr-2"></span>
            Processing...
          </button>
        </div>

        <!-- Buttons for success state -->
        <div class="flex gap-3" id="success-btns" *ngIf="success">
          <label for="transform" class="btn btn-sm btn-outline">Okay</label>
          <label
            class="btn btn-primary btn-sm rounded-md"
            *ngIf="canViewCode"
            (click)="goToCodeReview(selectedObjectName, selectedObjectType)"
            ><i class="ti ti-code text-base pr-2"></i> View Code
          </label>
        </div>

        <!-- Buttons for error state -->
        <div class="flex gap-3" id="error-btns" *ngIf="errorState">
          <label for="transform" class="btn btn-sm btn-outline">Close</label>
          <button class="btn btn-primary btn-sm" (click)="transformCode()">
            <i class="ti ti-refresh text-base pr-2"></i>Try Again
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DatabaseComponent } from './database.component';
import { DatabaseDetailsComponent } from './database-details/database-details.component';
import { DependencyChartComponent } from './dependency-chart/dependency-chart.component';
import { CodeReviewComponent } from './code-review/code-review.component';

const routes: Routes = [
  { path: '', component: DatabaseComponent },
  { path: ':connectionName', component: DatabaseDetailsComponent },
  {
    path: ':connectionName/dependency chart',
    component: DependencyChartComponent,
  },
  {
    path: ':connectionName/code review',
    component: CodeReviewComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DatabaseRoutingModule {}

table {
  border-collapse: collapse;
}

/* Override global table styles to ensure borders are visible */
.prompt-vault-table .table.table-compact > tbody > tr > td,
.prompt-vault-table .table.table-compact > thead > tr > th {
  border-right: 1px solid var(--border-color) !important;
}

/* Fix for the extra cell appearance */
.prompt-vault-table table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  table-layout: fixed !important;
  width: 100% !important;
  empty-cells: show !important;
}

/* Fix for table display */
.prompt-vault-table {
  display: block !important;
  overflow-x: hidden !important; /* Prevent horizontal scrolling */
  max-width: 100% !important;
}

/* Custom table styles to override global styles */
.custom-table {
  border-collapse: collapse !important;
  border: 1px solid var(--border-color) !important;
}

.custom-table th,
.custom-table td {
  border-bottom: 1px solid var(--border-color) !important;
  border-right: 1px solid var(--border-color) !important;
  padding: 0.75rem 1rem !important;
}

.custom-table tr:last-child td {
  border-bottom: none !important;
}

.custom-table tr td:last-child,
.custom-table tr th:last-child {
  border-right: none !important;
}

/* Remove any extra borders */
.custom-table::before,
.custom-table::after,
.custom-table tbody::before,
.custom-table tbody::after,
.custom-table thead::before,
.custom-table thead::after,
.custom-table tr::before,
.custom-table tr::after {
  display: none !important;
}

/* Character count styling with dark mode support */
.character-count {
  font-size: 0.8rem;
  transition: color 0.3s;
  color: var(--content2);
}

.character-count.warning {
  color: var(--warning);
}

.character-count.exceeded {
  color: var(--error);
}

/* Enhanced Table Styling */
.prompt-vault-table {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
  border: 1px solid var(--border-color);
}

.prompt-vault-table:hover {
  transform: none !important;
  box-shadow: none !important;
}

.prompt-vault-table::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0.5rem;
  pointer-events: none;
  border: 1px solid transparent;
  background: linear-gradient(
      to bottom right,
      rgba(148, 0, 255, 0.1),
      rgba(44, 206, 138, 0.1)
    )
    border-box;
  -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.prompt-vault-table:hover::after {
  opacity: 1;
}

/* Remove table container hover effect */

.prompt-vault-table th {
  font-weight: 600;
  letter-spacing: 0.01em;
  border-bottom: 1px solid var(--border-color) !important;
  background-color: rgba(148, 0, 255, 0.03);
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.08),
    rgba(44, 206, 138, 0.03)
  );
  box-sizing: border-box;
  white-space: nowrap;
  padding: 0.75rem 1rem !important;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s ease;
}

/* Ensure table cells have correct width */
.prompt-vault-table table th:first-child,
.prompt-vault-table table td:first-child {
  width: 35% !important;
}

.prompt-vault-table table th:nth-child(2),
.prompt-vault-table table td:nth-child(2),
.prompt-vault-table table th:nth-child(3),
.prompt-vault-table table td:nth-child(3),
.prompt-vault-table table th:nth-child(4),
.prompt-vault-table table td:nth-child(4) {
  width: 12% !important;
}

.prompt-vault-table table th:last-child,
.prompt-vault-table table td:last-child {
  width: 12% !important;
  text-align: right !important;
}

/* Fix for cell alignment */
.prompt-vault-table table th,
.prompt-vault-table table td {
  box-sizing: border-box !important;
  position: relative !important;
}

.prompt-vault-table tbody tr {
  transition: all 0.3s ease;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.prompt-vault-table tbody tr {
  position: relative;
  overflow: hidden;
}

/* Override RippleUI table-hover class */
.prompt-vault-table .table-hover tbody tr {
  transition: background-color 0.2s ease-in-out;
  transform: none !important;
}

/* Direct override for RippleUI table hover styles */
.prompt-vault-table .table.table-hover > tbody > tr:hover > td,
.prompt-vault-table .table.table-hover > tbody > tr:hover > th {
  --tw-bg-opacity: 1;
  background-color: rgba(148, 0, 255, 0.03) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.03),
    transparent
  ) !important;
}

.prompt-vault-table .table-hover tbody tr:hover {
  background-color: rgba(148, 0, 255, 0.03) !important;
  transform: none !important;
  box-shadow: none !important;
  background-image: none !important;
  border-color: var(--border-color) !important;
  z-index: 1;
  position: relative;
}

/* Button animations */
.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn-ghost:hover {
  background-color: var(--bg-muted);
}

/* Rounded button styling */
button.rounded-full {
  transition: all 0.3s ease;
}

button.rounded-full:hover {
  transform: translateY(-1px);
  background-color: var(--bg-muted);
}

/* Dark mode specific styles */
[data-theme="dark"] .prompt-vault-table {
  border-color: var(--border-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .prompt-vault-table::after {
  background: linear-gradient(
      to bottom right,
      rgba(148, 0, 255, 0.2),
      rgba(44, 206, 138, 0.2)
    )
    border-box;
}

/* Remove dark mode table container hover effect */

/* Dark mode hover effects */
/* Dark mode direct override for RippleUI table hover styles */
[data-theme="dark"]
  .prompt-vault-table
  .table.table-hover
  > tbody
  > tr:hover
  > td,
[data-theme="dark"]
  .prompt-vault-table
  .table.table-hover
  > tbody
  > tr:hover
  > th {
  --tw-bg-opacity: 1;
  background-color: rgba(148, 0, 255, 0.05) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.05),
    transparent
  ) !important;
}

[data-theme="dark"] .prompt-vault-table .table-hover tbody tr:hover {
  background-color: rgba(148, 0, 255, 0.05) !important;
  box-shadow: none !important;
  border-color: var(--border-color) !important;
  background-image: none !important;
  z-index: 1;
  position: relative;
  transform: none !important;
}

[data-theme="dark"] .prompt-vault-table th {
  color: var(--content1);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  background-color: rgba(148, 0, 255, 0.05);
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.12),
    rgba(44, 206, 138, 0.05)
  );
}

[data-theme="dark"] .custom-table tr td,
[data-theme="dark"] .custom-table tr th {
  border-right-color: var(--border-color) !important;
  border-bottom-color: var(--border-color) !important;
}

.prompt-vault-table .table-hover tbody tr:hover td {
  border-color: var(--border-color) !important;
}

[data-theme="dark"] .prompt-vault-table .table-hover tbody tr:hover td {
  border-color: var(--border-color) !important;
}

[data-theme="dark"] .btn-ghost:hover {
  background-color: var(--bg-muted);
}

[data-theme="dark"] button.rounded-full:hover {
  background-color: var(--bg-muted);
}

/* Dark mode pagination styling */
[data-theme="dark"] .pagination > div {
  background-color: var(--backgroundPrimary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  border-color: rgba(75, 75, 75, 0.8);
}

[data-theme="dark"] .pagination > div:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .pagination button.btn-primary {
  box-shadow: 0 1px 3px rgba(148, 0, 255, 0.3);
}

[data-theme="dark"] .pagination button.btn-primary:hover {
  box-shadow: 0 2px 5px rgba(148, 0, 255, 0.4);
}

[data-theme="dark"] .pagination button:not(.btn-primary):hover {
  background-color: rgba(148, 0, 255, 0.15) !important;
}

[data-theme="dark"] .pagination button.border-r,
[data-theme="dark"] .pagination button.border-l {
  border-color: rgba(75, 75, 75, 0.8) !important;
}

/* Dark mode select styling */
[data-theme="dark"] .select:focus {
  box-shadow: 0 0 0 3px rgba(148, 0, 255, 0.25);
}

[data-theme="dark"] .select:hover {
  border-color: rgba(148, 0, 255, 0.7);
}

/* Enhanced pagination styling */
.pagination {
  user-select: none;
}

.pagination button {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.pagination button:hover:not([disabled]) {
  transform: translateY(-1px);
}

.pagination button.btn-primary {
  box-shadow: 0 1px 3px rgba(148, 0, 255, 0.2);
}

.pagination button.btn-primary:hover {
  box-shadow: 0 2px 5px rgba(148, 0, 255, 0.3);
}

/* Pagination container styling */
.pagination > div {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.pagination > div:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border-color: rgba(148, 0, 255, 0.3);
}

/* Ripple effect for pagination buttons */
.pagination button::after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 10%,
    transparent 10.01%
  );
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.3s, opacity 0.5s;
}

.pagination button:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

/* Line clamp for description */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Empty state styling */
.empty-state-icon {
  animation: pulse 2s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

/* Gradient animation for headers */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Dark mode empty state */
[data-theme="dark"] .empty-state-icon {
  filter: drop-shadow(0 0 3px rgba(148, 0, 255, 0.5));
}

[data-theme="dark"] .rounded-full.bg-backgroundSecondary {
  box-shadow: 0 0 15px rgba(148, 0, 255, 0.2);
}

/* Enhanced Modal styling */
.modal-content {
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  margin: auto; /* Center the modal */
}

/* Fix modal positioning for all screen sizes */
.modal {
  align-items: center;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  justify-content: center;
}

/* Responsive modal adjustments */
@media (max-width: 768px) {
  .modal-content {
    min-width: 90% !important;
    max-width: 95% !important;
    margin: 1rem auto;
  }

  .modal {
    padding: 0.5rem;
    align-items: flex-start;
    padding-top: 2rem;
  }
}

.modal-content > div:nth-child(2) {
  overflow-y: auto;
  max-height: calc(90vh - 120px); /* Adjust based on header/footer height */
  flex: 1;
  padding-top: 0.5rem; /* Reduce top padding */
}

/* View Details Modal */
.view-details-modal {
  width: 100%;
  max-width: 700px;
  background-color: var(--backgroundPrimary);
  animation: modalFadeIn 0.3s ease-out forwards;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-overlay {
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
  animation: overlayFadeIn 0.2s ease-out forwards;
}

/* Delete confirmation modal animation */
.modal-overlay .modal-content {
  animation: modalFadeIn 0.3s ease-out forwards;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modal header styling - slightly darker than table header */
.modal-header {
  background-color: rgba(148, 0, 255, 0.05);
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.12),
    rgba(44, 206, 138, 0.05)
  );
}

/* Update text color for light mode */
.modal-header h2,
.modal-header h3 {
  color: var(--content1);
}

/* Delete confirmation modal specific styling */
.modal-content .bg-error\/10 {
  box-shadow: 0 0 15px rgba(var(--error), 0.2);
  transition: all 0.3s ease;
}

.modal-content .bg-error\/10:hover {
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(var(--error), 0.3);
}

.modal-content .btn-error {
  transition: all 0.3s ease;
}

.modal-content .btn-error:hover {
  box-shadow: 0 0 10px rgba(var(--error), 0.4);
}

/* Dark mode modal styling */
[data-theme="dark"] .modal-content {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(75, 75, 75, 0.5);
}

/* Dark mode delete confirmation modal */
[data-theme="dark"] .modal-content .bg-error\/10 {
  background-color: rgba(var(--error), 0.15) !important;
  box-shadow: 0 0 15px rgba(var(--error), 0.25);
}

/* Dark mode modal header styling to match table header */
[data-theme="dark"] .modal-header {
  background-color: rgba(148, 0, 255, 0.05) !important;
  background-image: linear-gradient(
    to bottom,
    rgba(148, 0, 255, 0.12),
    rgba(44, 206, 138, 0.05)
  ) !important;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Consistent form controls */
.select,
.input,
.textarea {
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.select:focus,
.input:focus,
.textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(148, 0, 255, 0.1);
}

/* Select dropdown animation */
select {
  cursor: pointer;
}

select:hover {
  border-color: var(--primary);
}

/* Custom dropdown icon animation */
.pointer-events-none i.ti-chevron-down {
  transition: transform 0.3s ease;
}

select:focus + .pointer-events-none i.ti-chevron-down {
  transform: rotate(180deg);
}

.dropdown:hover .pointer-events-none i.ti-chevron-down {
  transform: translateY(2px);
}

/* Enhanced select styling */
.select {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
  position: relative;
  z-index: 1;
}

.select:hover {
  border-color: var(--primary);
}

.select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(148, 0, 255, 0.15);
  outline: none;
}

/* Dropdown open animation */
@keyframes dropdownOpen {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-menu {
  animation: dropdownOpen 0.3s ease-out forwards;
}

/* Selected row styling */
.prompt-vault-table tbody tr.selected {
  background-color: rgba(148, 0, 255, 0.1) !important;
  border-left: 3px solid rgb(var(--primary));
}

/* Row fade in animation */
@keyframes fadeInRow {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

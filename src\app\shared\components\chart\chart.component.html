<div class="chart-wrapper">
  <!-- Legend and controls -->
  <div class="legend" *ngIf="!hasError">
    <div>
      <span class="legend-box view"></span>
      <span>View</span>
    </div>
    <div>
      <span class="legend-box table"></span>
      <span>Table</span>
    </div>
    <div>
      <span class="legend-box procedure"></span>
      <span>Procedure</span>
    </div>
    <div>
      <span class="legend-box function"></span>
      <span>Function</span>
    </div>
    <div>
      <span class="legend-box trigger"></span>
      <span>Trigger</span>
    </div>

    <div class="control-buttons">
      <button class="btn btn-sm btn-outline" (click)="zoomIn()">+</button>
      <button class="btn btn-sm btn-outline" (click)="zoomOut()">−</button>
      <button class="btn btn-sm btn-outline" (click)="showEntireGraph()">
        Reset
      </button>
    </div>
  </div>

  <!-- Loading state -->
  <div
    *ngIf="isLoading"
    class="flex flex-col justify-center items-center p-8 gap-4"
  >
    <!-- Colorful loader with gradient ring -->
    <div class="relative">
      <!-- Outer gradient ring with animation -->
      <div
        class="w-20 h-20 rounded-full bg-gradient-to-r from-primary via-secondary to-primary p-1 animate-spin-slow"
      >
        <!-- Inner background -->
        <div
          class="w-full h-full rounded-full bg-backgroundPrimary flex items-center justify-center"
        >
          <!-- Spinner -->
          <div class="loading loading-spinner loading-md text-primary"></div>
        </div>
      </div>

      <!-- Decorative elements -->
      <div class="absolute -top-2 -right-2 animate-bounce-slow">
        <div
          class="w-6 h-6 rounded-full bg-secondary flex items-center justify-center text-content1"
        >
          <i class="ti ti-sparkles text-xs"></i>
        </div>
      </div>

      <div class="absolute -bottom-1 -left-1 animate-pulse">
        <div
          class="w-5 h-5 rounded-full bg-primary flex items-center justify-center text-content1"
        >
          <i class="ti ti-brain text-xs"></i>
        </div>
      </div>

      <div class="absolute top-1 -left-2 animate-ping-slow">
        <div
          class="w-4 h-4 rounded-full bg-info flex items-center justify-center text-content1"
        >
          <i class="ti ti-bulb text-xs"></i>
        </div>
      </div>
    </div>

    <!-- Colorful progress indicator -->
    <div
      class="w-48 bg-backgroundSecondary rounded-full h-2.5 mt-2 overflow-hidden"
    >
      <div class="h-full w-full relative">
        <!-- Gradient progress bar -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-primary via-secondary to-info animate-[progress_2s_ease-in-out_infinite]"
        ></div>
        <!-- Shimmer effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-transparent via-content1 to-transparent opacity-20 animate-[shimmer_1.5s_ease-in-out_infinite]"
        ></div>
      </div>
    </div>

    <!-- Enhanced status text with gradient -->
    <div class="text-center">
      <div
        class="inline-block bg-gradient-to-r from-primary to-secondary text-transparent bg-clip-text"
      >
        <h3 class="text-xl font-bold">Generating Chart</h3>
      </div>
      <div class="flex items-center justify-center gap-1 mt-1">
        <p class="text-content2 text-sm">Processing database relationships</p>
        <div class="flex">
          <span class="text-primary animate-bounce-slow delay-100">.</span>
          <span class="text-secondary animate-bounce-slow delay-200">.</span>
          <span class="text-info animate-bounce-slow delay-300">.</span>
        </div>
      </div>
    </div>

    <!-- Show additional message if loading takes too long -->
    <div
      *ngIf="longLoadingMessage"
      class="mt-2 text-center max-w-md p-3 bg-backgroundSecondary rounded-lg border border-borderColor"
    >
      <div class="flex items-center gap-2 mb-2">
        <i class="ti ti-info-circle text-primary"></i>
        <p class="text-content1 text-sm font-medium">
          Chart Processing in Progress
        </p>
      </div>
      <p class="text-content2 text-sm">
        Complex database relationships require deeper analysis to generate
        accurate charts.
      </p>
      <p class="text-content2 text-xs mt-2 opacity-75">
        This may take a few moments to complete.
      </p>
    </div>
  </div>

  <!-- Error state -->
  <div
    *ngIf="hasError"
    class="flex flex-col justify-center items-center p-8 gap-4 h-full"
  >
    <div
      class="text-center max-w-md p-6 bg-backgroundSecondary rounded-lg border border-borderColor"
    >
      <div class="flex justify-center mb-4">
        <div
          class="w-16 h-16 rounded-full bg-error/10 flex items-center justify-center"
        >
          <i class="ti ti-chart-bar-off text-error text-2xl"></i>
        </div>
      </div>
      <h3 class="text-xl font-semibold text-content1 mb-2">Chart Error</h3>
      <div
        class="text-content2 mb-4 text-center whitespace-pre-line overflow-auto max-h-40"
      >
        {{ errorMessage }}
      </div>
      <button class="btn btn-primary" (click)="deferGraphRender()">
        Try Again
      </button>
    </div>
  </div>

  <!-- Cytoscape container -->
  <div
    #cyContainer
    class="graph-container"
    [class.hidden]="isLoading || hasError"
  ></div>
</div>

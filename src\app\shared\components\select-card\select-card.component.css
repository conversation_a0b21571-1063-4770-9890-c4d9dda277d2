/* Styling for select cards */
:host {
  display: block;
}

/* Dark mode text color fixes */
[data-theme="dark"] .card {
  color: var(--content1);
}

[data-theme="dark"] .card:not(.border-primary) {
  color: var(--content2);
}

/* Selected card styling */
.card.border-primary {
  background-color: rgba(var(--primary), 0.05);
}

[data-theme="dark"] .card.border-primary {
  background-color: rgba(var(--primary), 0.15);
}

/* Hover effects */
.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

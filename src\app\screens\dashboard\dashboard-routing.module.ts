import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashboardComponent } from './dashboard/dashboard.component';
import { LayoutComponent } from 'src/app/layout/layout/layout.component';
import { AuthGuard } from '../auth/auth.guard';

const routes: Routes = [
  { path: '', component: DashboardComponent }, // No LayoutComponent here
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DashboardRoutingModule {}

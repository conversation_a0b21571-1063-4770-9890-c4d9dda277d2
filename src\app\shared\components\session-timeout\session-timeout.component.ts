import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { AuthService } from 'src/app/core/auth/auth.service';

@Component({
  selector: 'app-session-timeout',
  templateUrl: './session-timeout.component.html',
})
export class SessionTimeoutComponent implements OnInit, OnDestroy {
  showModal: boolean = false;
  countdown: number = 15;
  interval: any;
  // Track if the user has been warned in the current session
  private hasBeenWarned: boolean = false;
  // Store the event listener for cleanup
  private sessionWarningListener: any;

  constructor(private authService: AuthService) {}

  ngOnInit() {
    // Create a reference to the event listener for cleanup
    this.sessionWarningListener = () => {
      // Only show the warning if the user hasn't been warned already
      // or if they've been active since the last warning
      if (!this.hasBeenWarned) {
        this.showWarning();
      }
    };

    window.addEventListener('session-warning', this.sessionWarningListener);

    // Listen for user activity to reset the warning flag
    window.addEventListener('mousemove', () => this.resetWarningFlag());
    window.addEventListener('keydown', () => this.resetWarningFlag());
    window.addEventListener('click', () => this.resetWarningFlag());
  }

  ngOnDestroy() {
    // Clean up event listeners
    window.removeEventListener('session-warning', this.sessionWarningListener);
    clearInterval(this.interval);
  }

  resetWarningFlag() {
    // Reset the warning flag when the user is active
    if (this.hasBeenWarned && !this.showModal) {
      this.hasBeenWarned = false;
    }
  }

  showWarning() {
    this.showModal = true;
    this.countdown = 15;
    this.hasBeenWarned = true;

    // Clear any existing interval
    if (this.interval) {
      clearInterval(this.interval);
    }

    this.interval = setInterval(() => {
      this.countdown--;
      if (this.countdown <= 0) {
        clearInterval(this.interval);
        this.logout();
      }
    }, 1000);
  }

  stayLoggedIn() {
    this.authService.extendSession();
    this.closeModal();
  }

  logout() {
    this.authService.logout();
    this.closeModal();
  }

  closeModal() {
    this.showModal = false;
    clearInterval(this.interval);
  }
}

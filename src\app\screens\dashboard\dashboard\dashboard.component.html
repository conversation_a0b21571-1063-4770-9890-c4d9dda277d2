<!-- <p>dashboard works!</p>
<button (click)="testSessionWarning()">Test Session Timeout</button>
<div class="divider"></div> -->

<div
  class="grid grid-cols-1 md:grid-cols-3 gap-4"
  [@staggerFadeIn]="stats?.total_databases"
>
  <app-stats
    title="Connections"
    icon="ti ti-database"
    [badge]="stats?.total_databases"
    iconSize="text-xl"
    iconColor="text-gray-500"
    [onlyHeader]="false"
  >
    <div class="my-0">
      <div class="flex flex-fill justify-between text-content3 gap-2">
        <div
          class="badge badge-flat-success flex justify-between flex-1 rounded-md py-2 px-4"
          [attr.data-theme]="getTheme()"
        >
          <p class="text-base font-semibold text-success">Connected</p>
          <p class="text-base font-semibold text-success">
            {{ stats?.total_databases }}
          </p>
        </div>
        <div
          class="badge badge-flat-error flex justify-between flex-1 rounded-md py-2 px-4"
          [attr.data-theme]="getTheme()"
        >
          <p class="text-base font-semibold text-error">Disconnected</p>
          <p class="text-base font-semibold text-error">
            {{ disconnectedCount }}
          </p>
        </div>
      </div>
    </div>
  </app-stats>

  <app-stats
    title="Analyzed Database"
    icon="ti ti-hourglass-empty"
    [badge]="stats?.analyzed_databases"
    iconSize="text-xl"
    iconColor="text-[#FB6340]"
    [onlyHeader]="false"
  >
    <div class="my-0">
      <div class="flex flex-fill flex-col justify-between gap-2">
        <div
          class="relative w-full h-2 rounded"
          [ngClass]="{
            'bg-gray-200': getTheme() === 'light',
            'bg-backgroundSecondary': getTheme() === 'dark'
          }"
        >
          <div
            class="absolute top-0 left-0 h-2 rounded bg-gradient-to-r from-orange-400 to-green-500"
            [ngStyle]="{
              width:
                calculatePercentage(
                  stats?.analyzed_databases,
                  stats?.total_databases
                ) + '%'
            }"
          ></div>
        </div>
        <div
          class="flex justify-between text-sm"
          [ngClass]="{
            'text-gray-700': getTheme() === 'light',
            'text-content1 opacity-90': getTheme() === 'dark'
          }"
        >
          <p class="text-base">
            {{
              calculatePercentage(
                stats?.analyzed_databases,
                stats?.total_databases
              )
            }}%
          </p>
          <p class="text-base">
            {{ stats?.analyzed_databases || 0 }}/{{
              stats?.total_databases || 0
            }}
          </p>
        </div>
      </div>
    </div>
  </app-stats>

  <app-stats
    title="DB Objects Transformed"
    icon="ti ti-discount-check"
    [badge]="stats?.summary?.transformed_objects"
    iconSize="text-xl"
    iconColor="text-[#2CCE8A]"
    [onlyHeader]="false"
  >
    <div class="my-0">
      <div class="flex flex-fill flex-col justify-between gap-2">
        <div
          class="relative w-full h-2 rounded"
          [ngClass]="{
            'bg-gray-200': getTheme() === 'light',
            'bg-backgroundSecondary': getTheme() === 'dark'
          }"
        >
          <div
            class="absolute top-0 left-0 h-2 rounded bg-gradient-to-r from-orange-400 to-green-500"
            [ngStyle]="{
              width:
                calculatePercentage(
                  stats?.summary?.transformed_objects,
                  stats?.summary?.total_objects
                ) + '%'
            }"
          ></div>
        </div>
        <div
          class="flex justify-between text-sm"
          [ngClass]="{
            'text-gray-700': getTheme() === 'light',
            'text-content1 opacity-90': getTheme() === 'dark'
          }"
        >
          <p class="text-base">
            {{
              calculatePercentage(
                stats?.summary?.transformed_objects,
                stats?.summary?.total_objects
              )
            }}%
          </p>
          <p class="text-base">
            {{ stats?.summary?.transformed_objects || 0 }}/
            {{ stats?.summary?.total_objects || 0 }}
          </p>
        </div>
      </div>
    </div>
  </app-stats>
</div>

<div
  class="flex flex-col gap-4 py-5 mt-2"
  [@staggerFadeIn]="stats?.transformed_databases"
>
  <div
    class="flex justify-between items-center bg-backgroundPrimary py-2 px-1 border-b mb-2"
    [attr.data-theme]="getTheme()"
  >
    <h2 class="text-xl font-semibold text-content1">Project Statistics</h2>
  </div>
  <app-stats
    title="DB Transform - Under Progress"
    icon="ti ti-progress"
    iconSize="text-xl"
    iconColor="text-[#9400FF]"
    [onlyHeader]="true"
    badge="1"
  >
  </app-stats>
  <app-stats
    title="Waiting for Revision (Re-analyze)"
    icon="ti ti-hourglass-empty"
    iconSize="text-xl"
    iconColor="text-[#FB6340]"
    [onlyHeader]="true"
    badge="0"
  >
  </app-stats>
  <app-stats
    title="Db Transformed"
    icon="ti ti-discount-check"
    iconSize="text-xl"
    iconColor="text-[#2CCE8A]"
    [onlyHeader]="true"
    [badge]="stats?.transformed_databases"
  >
  </app-stats>
</div>
<div
  class="max-w-[700px] mt-5 rounded-lg p-4 bg-backgroundPrimary border shadow-sm"
  [@fadeIn]
  [attr.data-theme]="getTheme()"
>
  <h2
    class="text-xl font-semibold mb-3 border-b pb-2 text-content1"
    [attr.data-theme]="getTheme()"
    style="border-color: var(--border-color)"
  >
    Features Overview
  </h2>
  <app-carousel [images]="images" [autoSlider]="true"></app-carousel>
</div>
<!-- <app-table
  [columns]="tableColumns"
  [data]="tableData"
  (selectionChanged)="selectionChanged($event)"
>
</app-table> -->

import { Injectable } from '@angular/core';
import {
  CanActivate,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
} from '@angular/router';
import { AuthService } from '../../core/auth/auth.service';
import { Observable, map, of, catchError, switchMap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AdminGuard implements CanActivate {
  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    _route: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot
  ): Observable<boolean> {
    console.log('AdminGuard - canActivate called');
    
    // First check if user is logged in
    if (!this.authService.isLoggedIn()) {
      console.log('AdminGuard - User not logged in, redirecting to login');
      this.router.navigate(['/']);
      return of(false);
    }

    // First try to get detailed role information
    return this.authService.getUserRoleDetails().pipe(
      switchMap((roleDetails) => {
        console.log('AdminGuard - User role details:', roleDetails);
        
        if (roleDetails) {
          const isSuperUser = roleDetails.is_superuser || false;
          
          // Check for admin role in the detailed role information
          let isAdminRole = false;
          if (roleDetails.role && roleDetails.role.name) {
            const roleName = roleDetails.role.name;
            isAdminRole = roleName.toLowerCase() === 'admin';
          }
          
          console.log('AdminGuard - Role checks from getUserRoleDetails:', {
            isSuperUser,
            isAdminRole,
            roleName: roleDetails.role?.name
          });
          
          // Allow access if user is superuser or has admin role
          if (isSuperUser || isAdminRole) {
            console.log('AdminGuard - Access granted based on getUserRoleDetails');
            return of(true);
          }
          
          // If not granted access yet, try getUserRole as a fallback
          return this.checkUserRoleFromAuthService();
        }
        
        // If no role details, fall back to getUserRole
        console.log('AdminGuard - No role details, falling back to getUserRole');
        return this.checkUserRoleFromAuthService();
      }),
      catchError((error) => {
        console.error('AdminGuard - Error in getUserRoleDetails:', error);
        // If there's an error, try the fallback method
        return this.checkUserRoleFromAuthService();
      })
    );
  }
  
  /**
   * Check user role using the getUserRole method
   * This provides another way to check admin access if getUserRoleDetails fails
   */
  private checkUserRoleFromAuthService(): Observable<boolean> {
    console.log('AdminGuard - Checking user role from getUserRole');
    
    return this.authService.getUserRole().pipe(
      switchMap((roleInfo) => {
        console.log('AdminGuard - User role info from getUserRole:', roleInfo);
        
        if (!roleInfo) {
          console.log('AdminGuard - No role info, redirecting to dashboard');
          this.router.navigate(['/dashboard']);
          return of(false);
        }
        
        // Check if user is superuser or has admin role
        const isSuperUser = roleInfo.isSuperUser || false;
        const isAdmin = roleInfo.isAdmin || false;
        
        // Also check roleName as a fallback
        let isAdminByName = false;
        if (roleInfo.roleName) {
          isAdminByName = roleInfo.roleName.toLowerCase() === 'admin';
        }
        
        console.log('AdminGuard - Role checks from getUserRole:', {
          isSuperUser,
          isAdmin,
          isAdminByName,
          roleName: roleInfo.roleName
        });
        
        // Allow access if user is superuser or has admin role
        if (isSuperUser || isAdmin || isAdminByName) {
          console.log('AdminGuard - Access granted based on getUserRole');
          return of(true);
        }
        
        // If still no access, fall back to getUserDetails as a last resort
        return this.checkUserDetailsDirectly();
      }),
      catchError((error) => {
        console.error('AdminGuard - Error in getUserRole:', error);
        // If there's an error, try the last fallback method
        return this.checkUserDetailsDirectly();
      })
    );
  }
  
  /**
   * Check user details directly as a last resort
   * This is the original implementation and serves as a final fallback
   */
  private checkUserDetailsDirectly(): Observable<boolean> {
    console.log('AdminGuard - Checking user details directly');
    
    return this.authService.getUserDetails().pipe(
      map((user) => {
        console.log('AdminGuard - User details:', user);
        
        if (!user) {
          console.log('AdminGuard - No user details, redirecting to dashboard');
          this.router.navigate(['/dashboard']);
          return false;
        }

        const isSuperUser = user.is_superuser || false;

        // Case-insensitive check for Admin role
        const roleName = user.role?.name || '';
        const isAdminRole = roleName.toLowerCase() === 'admin';
        
        console.log('AdminGuard - Role checks from getUserDetails:', {
          isSuperUser,
          isAdminRole,
          roleName
        });

        // Only allow superusers or users with admin role
        if (isSuperUser || isAdminRole) {
          console.log('AdminGuard - Access granted based on getUserDetails');
          return true;
        } else {
          // Redirect to not-authorized page
          console.log('AdminGuard - Access denied, redirecting to not-authorized');
          this.router.navigate(['/not-authorized']);
          return false;
        }
      }),
      catchError((error) => {
        console.error('AdminGuard - Error in getUserDetails:', error);
        this.router.navigate(['/dashboard']);
        return of(false);
      })
    );
  }
}

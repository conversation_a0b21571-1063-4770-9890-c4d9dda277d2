import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-button',
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.css'],
})
export class ButtonComponent {
  @Input() type: 'primary' | 'secondary' | 'success' | 'error' | 'warning' =
    'primary';
  @Input() variant: 'default' | 'outline' | 'solid' | 'ghost' = 'default';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'sm';
  @Input() rounded: boolean = false;
  @Input() block: boolean = false;
  @Input() disabled: boolean = false;
  @Input() loading: boolean = false;

  get buttonClasses(): string {
    let classes = 'btn'; // Base class

    // Default button style if no variant is specified
    if (this.variant === 'default') {
      classes += ` btn-${this.type}`;
    } else {
      classes += ` btn-${this.variant}-${this.type}`;
    }

    // Add size class
    if (this.size) {
      classes += ` btn-${this.size}`;
    }

    // Additional classes
    if (this.rounded) {
      classes += ' btn-rounded';
    }
    if (this.block) {
      classes += ' btn-block';
    }
    if (this.loading) {
      classes += ' btn-loading';
    }
    if (this.disabled) {
      classes += ' opacity-50 cursor-not-allowed';
    }

    return classes;
  }
}

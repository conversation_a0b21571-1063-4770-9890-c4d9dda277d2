<div
  class="flex justify-between top-0 sticky bg-backgroundPrimary mb-4"
  *ngIf="!loadingData"
>
  <div class="flex flex-col">
    <h2 class="text-xl mb-2">
      {{ transformationData.object_name }}:<span
        *ngIf="transformationData.has_api_transformation"
      >
        API</span
      >
      <span
        *ngIf="
          transformationData.has_rdbms_transformation &&
          transformationData.has_api_transformation
        "
        class="ml-1"
        >and</span
      >
      <span *ngIf="transformationData.has_rdbms_transformation">
        Code Object</span
      >
    </h2>
    <div class="flex gap-3 items-center">
      <h2 class="text-base text-nowrap">From: {{ sourceDB }}</h2>

      <h2 class="text-base">To:</h2>

      <select
        class="select select-solid-secondary select-sm min-w-[100px] max-w-[130px]"
        [(ngModel)]="selectedTarget"
        (change)="onTargetChange()"
        [disabled]="availableTargets.length === 1"
        [ngClass]="{ 'w-[130px]': availableTargets.length === 1 }"
      >
        <option
          *ngIf="transformationData.has_rdbms_transformation"
          value="code"
        >
          Code Object
        </option>
        <option *ngIf="transformationData.has_api_transformation" value="api">
          API
        </option>
      </select>
    </div>
  </div>
  <div class="flex gap-3">
    <label class="btn btn-outline btn-sm" for="summary">
      See What's Changed!
    </label>

    <!-- Push to Github button - only visible with push_to_github permission and when connected -->
    <button
      class="btn btn-primary btn-sm"
      *ngIf="canPushToGithub && isConnected"
      (click)="pushToGithub()"
    >
      Push to Github
    </button>

    <!-- Disabled Push to Github button when disconnected -->
    <button
      class="btn btn-primary btn-sm cursor-not-allowed opacity-60"
      *ngIf="canPushToGithub && !isConnected"
      disabled
      title="Database must be connected to push to Github"
    >
      Push to Github
    </button>

    <!-- Finetune Code button - only visible with regenerate_code permission and when connected -->
    <label
      class="btn btn-primary btn-sm rounded-md"
      for="transform"
      *ngIf="canRegenerateCode && isConnected"
    >
      <i class="ti ti-input-ai text-base pr-2"></i>
      Finetune Code
    </label>

    <!-- Disabled Finetune Code button when disconnected -->
    <button
      class="btn btn-primary btn-sm rounded-md cursor-not-allowed opacity-60"
      *ngIf="canRegenerateCode && !isConnected"
      disabled
      title="Database must be connected to fine-tune code"
    >
      <i class="ti ti-input-ai text-base pr-2"></i>
      Finetune Code
    </button>
  </div>
</div>

<div *ngIf="loadingData" class="flex justify-center my-8">
  <div
    class="absolute animate-spin rounded-full h-8 w-8 border-b-2 border-primary top-[50%]"
  ></div>
</div>

<div
  class="flex w-full h-[calc(100vh-180px)] relative"
  #container
  *ngIf="!loadingData"
>
  <!-- Left Panel: Always present -->
  <div
    class="resizable-panel border rounded-md rounded-r-none flex flex-col h-full"
    [style.width.%]="leftWidth"
  >
    <div class="top-0 sticky bg-backgroundPrimary p-3 z-10 border-b">
      <div class="flex justify-between items-center">
        <p class="text-uppercase text-capitalize">Existing - {{ sourceDB }}</p>

        <!-- Copy Button for Original Code -->
        <div class="flex items-center">
          <button
            class="btn btn-sm"
            [ngClass]="{
              'btn-outline-secondary': !originalCodeCopySuccess,
              'btn-success': originalCodeCopySuccess,
              'opacity-75': isOriginalCodeCopying
            }"
            (click)="copyOriginalCode()"
            [disabled]="isOriginalCodeCopying"
            title="Copy original code"
          >
            <i
              class="ti mr-1"
              [ngClass]="originalCodeCopySuccess ? 'ti-check' : 'ti-copy'"
            ></i>
            {{ originalCodeCopySuccess ? "Copied!" : "Copy" }}
          </button>
        </div>
      </div>
    </div>

    <div class="flex-1 overflow-auto p-3">
      <pre
        class="h-full m-0"
      ><code class="text-wrap hljs" [innerHTML]="getSanitizedOriginalCode()"></code></pre>
    </div>
  </div>

  <!-- Resizer Divider -->
  <div class="resizer" (mousedown)="startResizing($event)"></div>

  <!-- Right Panel: RDBMS Code Object View -->
  <div
    class="resizable-panel border rounded-md rounded-l-none flex flex-col h-full"
    [style.width.%]="100 - leftWidth"
  >
    <div class="top-0 sticky bg-backgroundPrimary p-3 z-10 border-b">
      <div class="flex justify-between items-center">
        <div>
          <p
            class="text-uppercase text-capitalize"
            *ngIf="selectedTarget === 'code'"
          >
            <i class="ti ti-sparkles mr-1"></i> AI Generated -
            {{ transformationData.target_db_type }}
          </p>
          <p
            class="text-uppercase text-capitalize"
            *ngIf="selectedTarget === 'api'"
          >
            AI Generated - API
          </p>
        </div>

        <!-- Copy Button -->
        <div class="flex items-center">
          <button
            class="btn btn-sm"
            [ngClass]="{
              'btn-outline-secondary': !codeAreaCopySuccess,
              'btn-success': codeAreaCopySuccess,
              'opacity-75': isCodeAreaCopying
            }"
            (click)="copyGeneratedCode()"
            [disabled]="isCodeAreaCopying"
            title="Copy code"
          >
            <i
              class="ti mr-1"
              [ngClass]="codeAreaCopySuccess ? 'ti-check' : 'ti-copy'"
            ></i>
            {{ codeAreaCopySuccess ? "Copied!" : "Copy" }}
          </button>
        </div>
      </div>
    </div>
    <div class="flex-1 overflow-auto p-3" *ngIf="!loading">
      <pre
        class="h-full m-0"
        *ngIf="selectedTarget === 'code'"
      ><code class="text-wrap hljs bg-backgroundPrimary" [innerHTML]="getSanitizedRdbmsCode()"></code></pre>
      <pre
        class="h-full m-0"
        *ngIf="selectedTarget === 'api'"
      ><code class="text-wrap hljs bg-backgroundPrimary" [innerHTML]="getSanitizedApiCode()"></code></pre>
    </div>
  </div>
</div>

<!-- Hidden button for external scripts to find -->
<button
  id="hiddenCopyButton"
  style="display: none; position: absolute; visibility: hidden"
>
  Copy
</button>

<input class="modal-state" id="summary" type="checkbox" />
<div class="modal w-screen">
  <label class="modal-overlay"></label>
  <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
    <div class="modal-header">
      <h2 class="text-xl">See What's Changed</h2>
      <label for="summary" class="modal-close-btn"
        ><i class="ti ti-x"></i
      ></label>
    </div>

    <div class="p-3 py-2 max-h-[60vh] overflow-auto prose prose-sm">
      <!-- Add null check -->
      <markdown
        *ngIf="transformationData.summary"
        [data]="transformationData.summary"
      ></markdown>
      <p *ngIf="!transformationData?.summary">No summary available</p>
    </div>

    <div class="flex gap-3 p-3 border-t justify-end">
      <div class="flex gap-3">
        <!-- Enhanced copy button with integrated success state -->
        <button
          class="btn btn-sm rounded-md"
          [ngClass]="{
            'btn-secondary': !copySuccess,
            'btn-success': copySuccess,
            'opacity-75': isCopying
          }"
          id="copySummaryBtn"
          (click)="copySummary()"
          [disabled]="isCopying"
        >
          <i
            class="ti mr-1"
            [ngClass]="copySuccess ? 'ti-check' : 'ti-copy'"
          ></i>
          {{ copySuccess ? "Copied!" : "Copy" }}
        </button>
        <label class="btn btn-primary btn-sm rounded-md" for="summary">
          Okay
        </label>
      </div>
    </div>
  </div>
</div>

<input class="modal-state" id="transform" type="checkbox" />
<div class="modal w-screen">
  <label class="modal-overlay"></label>
  <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
    <div class="modal-header">
      <h2 class="text-xl">Finetune Code</h2>
      <label for="transform" class="modal-close-btn"
        ><i class="ti ti-x"></i
      ></label>
    </div>

    <div
      *ngIf="loading"
      class="absolute inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50"
    >
      <div class="spinner"></div>
    </div>

    <div class="p-3 py-2 max-h-[60vh] overflow-auto" *ngIf="!success">
      <!-- Step 3: Specify Additional Instructions For The Transformation from list of prompts -->

      <div class="">
        <div class="relative w-full mb-3">
          <!-- Search Input -->
          <input
            type="text"
            [(ngModel)]="searchTerm"
            class="input input-lg max-w-full pl-10"
            placeholder="Search"
          />
          <span class="absolute inset-y-0 left-3 inline-flex items-center">
            <i class="ti ti-search"></i>
          </span>
        </div>

        <!-- Multi-Select Options -->
        <!-- Fixed HTML template matching the new toggleSelection logic -->
        <div class="mt-2 flex flex-col w-full gap-3 justify-center">
          <ng-container *ngFor="let item of filteredOptions">
            <label
              class="card p-2 rounded-lg border transition-all duration-300 cursor-pointer border-l-4 flex flex-row gap-2 min-w-full"
              [ngClass]="{
                'border-primary shadow-lg bg-blue-50': selectedOptions.includes(
                  item.title + ' ' + item.description
                ),
                'border-gray-300 text-gray-500 shadow-none':
                  !selectedOptions.includes(item.title + ' ' + item.description)
              }"
            >
              <input
                type="checkbox"
                class="hidden"
                [value]="item"
                (change)="toggleSelection(item)"
              />
              <i
                class="ti pt-1"
                [ngClass]="
                  selectedOptions.includes(item.title + ' ' + item.description)
                    ? 'ti-check text-primary'
                    : 'ti-database text-content3'
                "
              ></i>
              <div class="flex flex-col gap-1">
                <span class="font-medium">{{ item.title }}</span>
                <span class="text-content2 text-sm line-clamp-1">{{
                  item.description
                }}</span>
                <span
                  class="badge badge-xs col-auto text-wrap w-fit px-2"
                  [ngClass]="{
                    'badge-outline-primary': selectedOptions.includes(
                      item.title + ' ' + item.description
                    ),
                    'badge-outline': !selectedOptions.includes(
                      item.title + ' ' + item.description
                    )
                  }"
                  >{{ item.type }}</span
                >
              </div>
            </label>
          </ng-container>
        </div>
      </div>
    </div>
    <div class="p-3 py-2 max-h-[60vh] overflow-auto" *ngIf="success">
      <div class="success-message bg-green-100 p-3 rounded-md">
        <p class="text-green-700">{{ successMessage }}</p>
      </div>
    </div>

    <div class="flex gap-3 p-3 border-t justify-end">
      <div class="flex gap-3" id="step1-btns" *ngIf="!success">
        <!-- Transform button with options - only visible with regenerate_code permission and when connected -->
        <button
          *ngIf="selectedOptions.length > 0 && canRegenerateCode && isConnected"
          class="btn btn-primary btn-sm"
          (click)="fineTuneCode()"
        >
          <i class="ti ti-input-ai text-base pr-2"></i>Transform
        </button>

        <!-- Disabled Transform button when disconnected -->
        <button
          *ngIf="
            selectedOptions.length > 0 && canRegenerateCode && !isConnected
          "
          class="btn btn-primary btn-sm cursor-not-allowed opacity-60"
          disabled
          title="Database must be connected to transform code"
        >
          <i class="ti ti-input-ai text-base pr-2"></i>Transform
        </button>

        <!-- Skip & Transform button - only visible with regenerate_code permission and when connected -->
        <button
          *ngIf="
            selectedOptions.length === 0 && canRegenerateCode && isConnected
          "
          class="btn btn-primary btn-sm"
          (click)="fineTuneCode()"
        >
          <i class="ti ti-input-ai text-base pr-2"></i>Skip & Transform
        </button>

        <!-- Disabled Skip & Transform button when disconnected -->
        <button
          *ngIf="
            selectedOptions.length === 0 && canRegenerateCode && !isConnected
          "
          class="btn btn-primary btn-sm cursor-not-allowed opacity-60"
          disabled
          title="Database must be connected to transform code"
        >
          <i class="ti ti-input-ai text-base pr-2"></i>Skip & Transform
        </button>

        <!-- No permission message -->
        <span
          *ngIf="!canRegenerateCode"
          class="text-content2 text-sm flex items-center"
        >
          <i class="ti ti-lock mr-1"></i> You don't have permission to
          regenerate code
        </span>

        <!-- Database disconnected message -->
        <span
          *ngIf="canRegenerateCode && !isConnected"
          class="text-content2 text-sm flex items-center"
        >
          <i class="ti ti-plug-connected-x mr-1"></i> Database must be connected
          to transform code
        </span>
      </div>
      <div class="flex gap-3" id="step1-btns" *ngIf="success">
        <label
          for="transform"
          class="btn btn-sm btn-outline"
          (click)="getCodeReviewData()"
          >Okay</label
        >
      </div>
    </div>
  </div>
</div>

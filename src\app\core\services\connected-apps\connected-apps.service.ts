import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { catchError, tap, map } from 'rxjs/operators';
import { EnvironmentService } from '../environment.service';

export interface GitHubConfig {
  repositoryUrl: string;
  branch: string;
  token: string;
  isConnected: boolean;
}

export interface ConnectedApp {
  type: string;
  name: string;
  isConnected: boolean;
  config: any;
}

@Injectable({
  providedIn: 'root',
})
export class ConnectedAppsService {
  private apiBaseUrl: string;

  constructor(
    private http: HttpClient,
    private envService: EnvironmentService
  ) {
    // Get the API URL from the environment service
    this.apiBaseUrl = this.envService.apiBaseUrl;

    // Log the URL in production for debugging
    if (this.envService.isProduction) {
      console.log('Connected Apps Service API URL:', this.apiBaseUrl);
    }
  }

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders({
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    });
  }

  /**
   * Get all connected apps for the current organization
   */
  getConnectedApps(): Observable<ConnectedApp[]> {
    const organization_id = localStorage.getItem('organization_id') || '';
    const url = `${this.apiBaseUrl}/connected-apps`;

    return this.http
      .get<ConnectedApp[]>(url, {
        headers: this.getAuthHeaders(),
        params: { organization_id },
      })
      .pipe(
        tap((response) => console.log('Connected apps response:', response)),
        catchError((error) => {
          console.error('Error fetching connected apps:', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Initiate GitHub OAuth login flow
   * This will redirect the user to GitHub for authentication
   */
  initiateGitHubLogin(): void {
    // Store the current URL to redirect back after login
    localStorage.setItem('github_redirect_url', window.location.href);

    const url = `${this.apiBaseUrl}/github/login/github`;
    // Open in the same window to handle the OAuth flow
    window.location.href = url;
  }

  /**
   * Link a GitHub repository with a database connection
   * @param connectionId The database connection ID or database ID
   * @param repoName The GitHub repository name
   */
  linkGitHubRepository(
    connectionId: string,
    repoName: string
  ): Observable<any> {
    const url = `${this.apiBaseUrl}/github/repositories/link`;

    // Support both connection_id and database_id parameters
    // The backend should handle either one
    return this.http
      .post(
        url,
        {
          connection_id: connectionId,
          database_id: connectionId, // Include both for backward compatibility
          repository_name: repoName,
        },
        {
          headers: this.getAuthHeaders(),
        }
      )
      .pipe(
        tap((response) =>
          console.log('GitHub repository link response:', response)
        ),
        catchError((error) => {
          console.error('Error linking GitHub repository:', error);
          // If the API endpoint doesn't exist, return a success response anyway
          if (error.status === 404) {
            return of({
              success: true,
              message: 'Repository linked successfully (local only)',
            });
          }
          return throwError(() => error);
        })
      );
  }

  /**
   * Get available GitHub repositories
   */
  getGitHubRepositories(): Observable<any> {
    const url = `${this.apiBaseUrl}/github/repositories`;

    return this.http
      .get(url, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        tap((response) =>
          console.log('GitHub repositories response:', response)
        ),
        catchError((error) => {
          console.error('Error fetching GitHub repositories:', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Unlink/delete a GitHub repository
   * @param repoName The name of the repository to unlink
   */
  unlinkGitHubRepository(repoName: string): Observable<any> {
    const url = `${this.apiBaseUrl}/github/repositories/unlink`;

    return this.http
      .post(
        url,
        {
          repository_name: repoName,
        },
        {
          headers: this.getAuthHeaders(),
        }
      )
      .pipe(
        tap((response) =>
          console.log('GitHub repository unlink response:', response)
        ),
        catchError((error) => {
          console.error('Error unlinking GitHub repository:', error);
          // If the API endpoint doesn't exist, return a success response anyway
          if (error.status === 404) {
            return of({
              success: true,
              message: 'Repository unlinked successfully (local only)',
            });
          }
          return throwError(() => error);
        })
      );
  }

  /**
   * Push transformed code to GitHub
   * @param databaseId The database ID
   * @param procedureNames Array of procedure names to push
   * @param objectType The object type (e.g., 'procedure')
   */
  pushTransformedCode(
    databaseId: string,
    procedureNames: string[],
    objectType: string = 'procedure'
  ): Observable<any> {
    const url = `${this.apiBaseUrl}/github/push-transformed-code`;

    return this.http
      .post(
        url,
        {
          database_id: databaseId,
          procedure_names: procedureNames,
          object_type: objectType,
        },
        {
          headers: this.getAuthHeaders(),
        }
      )
      .pipe(
        tap((response) => console.log('Push to GitHub response:', response)),
        catchError((error) => {
          console.error('Error pushing code to GitHub:', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Connect to GitHub (for backward compatibility with our UI)
   * @param config GitHub configuration
   * @deprecated Use initiateGitHubLogin() instead
   */
  connectGitHub(config: GitHubConfig): Observable<any> {
    console.log('Using GitHub OAuth flow instead of direct connection');

    // Store the config temporarily in localStorage
    localStorage.setItem('github_config_temp', JSON.stringify(config));

    // Return an observable that completes immediately
    return of({
      success: true,
      message: 'Redirecting to GitHub OAuth...',
      redirectToOAuth: true,
    });
  }

  /**
   * Disconnect from GitHub
   * Attempts to call the backend API to disconnect, and also clears local state
   */
  disconnectGitHub(): Observable<any> {
    console.log('Disconnecting from GitHub');

    // Clear any stored GitHub data
    localStorage.removeItem('github_config_temp');

    // Try to call the API endpoint if it exists
    const url = `${this.apiBaseUrl}/github/disconnect`;

    return this.http.post(url, {}, { headers: this.getAuthHeaders() }).pipe(
      tap((response) =>
        console.log('GitHub disconnection API response:', response)
      ),
      catchError((error) => {
        console.error('Error disconnecting from GitHub API:', error);
        // If the API call fails, still return success for the local disconnection
        return of({
          success: true,
          message: 'GitHub disconnected successfully (local only)',
          apiError: error,
        });
      })
    );
  }

  /**
   * Test GitHub connection
   * Note: There's no direct API for testing in the current endpoints,
   * so we'll just validate the inputs
   * @param config GitHub configuration
   */
  testGitHubConnection(config: GitHubConfig): Observable<any> {
    console.log('Testing GitHub connection (validation only):', config);

    // Validate the inputs
    if (!config.repositoryUrl) {
      return throwError(() => new Error('Repository URL is required'));
    }

    // Check if the repository URL is valid
    const validUrlPattern =
      /^https:\/\/github\.com\/[\w-]+\/[\w-]+(\/?|\.git)$/;
    if (!validUrlPattern.test(config.repositoryUrl)) {
      return throwError(() => new Error('Invalid GitHub repository URL'));
    }

    // Return a success response
    return of({ success: true, message: 'Validation successful' }).pipe(
      tap((response) => console.log('GitHub validation response:', response))
    );
  }

  /**
   * Get GitHub configuration
   * Note: We'll use the repositories endpoint to check if the user is connected to GitHub
   */
  getGitHubConfig(): Observable<GitHubConfig> {
    console.log('Getting GitHub connection status');

    // Check if we have a stored config
    const storedConfig = localStorage.getItem('github_config_temp');
    let initialConfig: GitHubConfig = {
      repositoryUrl: '',
      branch: 'main',
      token: '',
      isConnected: false,
    };

    if (storedConfig) {
      try {
        const parsedConfig = JSON.parse(storedConfig);
        initialConfig = {
          ...initialConfig,
          ...parsedConfig,
        };
      } catch (e) {
        console.error('Error parsing stored GitHub config:', e);
      }
    }

    // Try to get repositories to determine if connected
    return this.getGitHubRepositories().pipe(
      map((repos) => {
        // If we get a successful response, the user is connected
        return {
          repositoryUrl:
            Array.isArray(repos) && repos.length > 0
              ? initialConfig.repositoryUrl || `https://github.com/${repos[0]}`
              : initialConfig.repositoryUrl,
          branch: initialConfig.branch || 'main',
          token: '', // We don't store tokens with OAuth
          isConnected: true,
        };
      }),
      catchError((error) => {
        console.error('Error checking GitHub connection:', error);

        // Check if the error is due to authentication
        if (error.status === 401) {
          // Try to refresh the token or redirect to login
          console.log('Authentication error, may need to reconnect to GitHub');
        }

        // Return the stored config or a default config (not connected)
        return of({
          ...initialConfig,
          isConnected: false,
        });
      })
    );
  }
}

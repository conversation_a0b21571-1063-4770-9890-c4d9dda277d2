import { Component } from '@angular/core';
interface FaqItem {
  question: string;
  answer: string;
  isOpen: boolean;
  category: string;
}

interface FaqCategory {
  name: string;
  icon: string;
}

@Component({
  selector: 'app-faq',
  templateUrl: './faq.component.html',
  styleUrls: ['./faq.component.css'],
})
export class FaqComponent {
  searchQuery: string = '';
  activeCategory: string = 'all';

  categories: FaqCategory[] = [
    { name: 'all', icon: 'ti-category' },
    { name: 'general', icon: 'ti-info-circle' },
    { name: 'database', icon: 'ti-database' },
    { name: 'migration', icon: 'ti-exchange' },
    { name: 'accounts', icon: 'ti-user' },
    { name: 'billing', icon: 'ti-credit-card' },
  ];

  faqs: FaqItem[] = [
    {
      question: 'What is RevMigrate?',
      answer:
        'RevMigrate is a comprehensive platform designed to help businesses migrate their database systems efficiently and securely. It provides tools for database management, documentation, and collaborative migration planning.',
      isOpen: false,
      category: 'general',
    },
    {
      question: 'How do I connect my database to RevMigrate?',
      answer:
        'To connect your database, navigate to the "My Databases" section and click on "Add Connection". Follow the step-by-step guide to provide your database credentials securely. We support all major database systems including MySQL, PostgreSQL, SQL Server, Oracle, and MongoDB.',
      isOpen: false,
      category: 'database',
    },
    {
      question: 'Is my database information secure?',
      answer:
        'Yes, we take security very seriously. All connections are encrypted using industry standard SSL/TLS protocols. Your credentials are stored using strong encryption and we never access your actual data without explicit permission. RevMigrate is SOC 2 Type II compliant and undergoes regular security audits.',
      isOpen: false,
      category: 'database',
    },
    {
      question: 'What migration strategies does RevMigrate support?',
      answer:
        'RevMigrate supports various migration strategies including lift-and-shift, rearchitecting, phased migration, and parallel runs. Our platform provides analysis tools to help you determine the most suitable strategy for your specific needs and constraints.',
      isOpen: false,
      category: 'migration',
    },
    {
      question: 'How can I create a migration plan?',
      answer:
        'In the Dashboard, you can create a new migration plan by clicking on "New Migration Project". This will guide you through defining your source and target environments, mapping your schemas, setting up validation rules, and creating a timeline for your migration.',
      isOpen: false,
      category: 'migration',
    },
    {
      question: 'How do I add team members to my project?',
      answer:
        'Navigate to the "User Management" section and click on "Invite Team Members". You can specify roles and permissions for each team member. Once invited, they will receive an email with instructions to join your project.',
      isOpen: false,
      category: 'accounts',
    },
    {
      question: 'What subscription plans are available?',
      answer:
        'RevMigrate offers several plans tailored to different needs: Free (for small projects), Professional, Business, and Enterprise. Each plan varies in terms of database connections, storage, team members, and support level. Visit our pricing page for detailed information.',
      isOpen: false,
      category: 'billing',
    },
    {
      question: 'How can I upgrade or downgrade my plan?',
      answer:
        'To change your subscription, go to your Account Settings, select "Billing", and choose "Change Plan". Changes to a higher tier take effect immediately, while downgrades will apply at the end of your current billing cycle.',
      isOpen: false,
      category: 'billing',
    },
    {
      question: 'Do you offer a trial period?',
      answer:
        'Yes, we offer a 14-day free trial of our Professional plan for new users. No credit card is required to start the trial, and you can upgrade to a paid plan at any time during or after the trial period.',
      isOpen: false,
      category: 'billing',
    },
    {
      question: 'What kind of support does RevMigrate provide?',
      answer:
        'We offer various support options depending on your subscription tier. All users have access to our documentation, FAQs, and community forums. Professional and higher plans include email support, while Business and Enterprise plans come with dedicated support channels and faster response times.',
      isOpen: false,
      category: 'general',
    },
  ];

  filteredFaqs: FaqItem[] = [];

  constructor() {}

  ngOnInit(): void {
    this.filteredFaqs = [...this.faqs];
  }

  toggleFaq(faq: FaqItem): void {
    faq.isOpen = !faq.isOpen;
  }

  filterByCategory(category: string): void {
    this.activeCategory = category;
    this.applyFilters();
  }

  search(event: Event): void {
    this.searchQuery = (event.target as HTMLInputElement).value;
    this.applyFilters();
  }

  applyFilters(): void {
    this.filteredFaqs = this.faqs.filter((faq) => {
      // Filter by category
      const categoryMatch =
        this.activeCategory === 'all' || faq.category === this.activeCategory;

      // Filter by search query
      const searchMatch =
        this.searchQuery === '' ||
        faq.question.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(this.searchQuery.toLowerCase());

      return categoryMatch && searchMatch;
    });
  }
}

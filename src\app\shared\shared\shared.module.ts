import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ButtonComponent } from '../components/button/button.component';
import { StatsComponent } from '../components/stats/stats.component';
import { TableComponent } from '../components/table/table.component';
import { HttpClientModule } from '@angular/common/http';
import { StepperComponent } from '../components/stepper/stepper.component';
import { SelectCardComponent } from '../components/select-card/select-card.component';
import { ChartComponent } from '../components/chart/chart.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { CarouselComponent } from '../components/carousel/carousel.component';
import { MarkdownModule } from 'ngx-markdown';
import { AvatarPlaceholderComponent } from '../components/avatar-placeholder/avatar-placeholder.component';
@NgModule({
  declarations: [
    ButtonComponent,
    StatsComponent,
    TableComponent,
    StepperComponent,
    SelectCardComponent,
    ChartComponent,
    CarouselComponent,
    AvatarPlaceholderComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    NgSelectModule,
    MarkdownModule.forChild(),
  ], // Import
  exports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonComponent,
    StatsComponent,
    TableComponent,
    HttpClientModule,
    StepperComponent,
    SelectCardComponent,
    ChartComponent,
    NgSelectModule,
    CarouselComponent,
    MarkdownModule,
    AvatarPlaceholderComponent,
  ], // Export
})
export class SharedModule {}

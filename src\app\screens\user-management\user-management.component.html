<div>
  <!-- Organization Table -->
  <div *ngIf="activeTab == 'organization'">
    <div class="flex justify-between items-center mb-3">
      <div class="flex items-center gap-3">
        <h3 class="text-lg">Organization Management</h3>

        <div class="popover popover-hover">
          <label class="popover-trigger my-2 text-primary font-medium"
            >Tip</label
          >
          <div class="popover-content">
            <div class="popover-arrow"></div>
            <div class="p-2 text-sm">
              Click on an organization to view and manage its users
            </div>
          </div>
        </div>
      </div>
      <div>
        <ng-container *ngIf="activeTab == 'organization'">
          <label
            class="btn btn-primary btn-sm rounded-md"
            for="AddOrganizationModal"
            *ngIf="canCreateOrganization()"
          >
            <i class="ti ti-plus text-base pr-2"></i> Add Organization
          </label>
          <label
            class="btn btn-secondary btn-sm rounded-md ml-2"
            for="AddSuperUserModal"
            *ngIf="isSuperUser"
          >
            <i class="ti ti-user-plus text-base pr-2"></i> Manage Super User
          </label>
        </ng-container>
      </div>
    </div>

    <div class="enhanced-table overflow-hidden rounded-lg border shadow-sm">
      <!-- Error message -->
      <div *ngIf="errorMessage && !isLoading" class="alert alert-error mb-4">
        <i class="ti ti-circle-x mr-2"></i>
        {{ errorMessage }}
        <button class="ml-auto" (click)="loadOrganizations()">
          <i class="ti ti-refresh"></i> Retry
        </button>
      </div>

      <!-- Loading indicator -->
      <div *ngIf="isLoading" class="flex justify-center items-center p-8">
        <div class="loading loading-spinner loading-lg"></div>
      </div>

      <!-- Table -->
      <div
        *ngIf="!isLoading"
        class="enhanced-table overflow-hidden rounded-lg border shadow-sm"
      >
        <table
          class="table table-compact table-hover w-full border-collapse custom-table border border-[var(--border-color)]"
        >
          <thead>
            <tr>
              <th
                class="text-left font-medium text-content1 border-r border-b border-[var(--border-color)]"
              >
                Organization Name
              </th>
              <th
                class="text-left font-medium text-content1 border-r border-b border-[var(--border-color)]"
              >
                Email
              </th>
              <th
                class="text-left font-medium text-content1 border-r border-b border-[var(--border-color)]"
              >
                Type
              </th>
              <th
                class="text-left font-medium text-content1 border-r border-b border-[var(--border-color)]"
              >
                Status
              </th>
              <th
                class="text-right font-medium text-content1 border-b border-[var(--border-color)]"
              >
                Action
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="let org of paginatedOrganizations; let i = index"
              class="cursor-pointer border-b transition-all duration-200"
              [ngStyle]="{ 'animation-delay': i * 0.05 + 's' }"
              style="animation: fadeInRow 0.5s ease-out forwards"
              [ngClass]="{
                'bg-primary/10 hover:bg-primary/15': selectedOrgId === org.id,
                'hover:bg-primary/5': selectedOrgId !== org.id
              }"
              (click)="navigateToOrganizationDetails(org.id || '')"
            >
              <td class="border-r border-[var(--border-color)]">
                <div class="font-semibold text-content1">
                  {{ org.name }}
                  <span
                    *ngIf="selectedOrgId === org.id"
                    class="text-xs bg-primary text-white px-2 py-0.5 rounded-full ml-2"
                    >Selected</span
                  >
                </div>
              </td>
              <td class="text-content1 border-r border-[var(--border-color)]">
                {{ org.email }}
              </td>
              <td class="text-content1 border-r border-[var(--border-color)]">
                <span
                  class="text-white px-2 py-1 rounded text-xs"
                  [ngClass]="{
                    'bg-[#7A08FA]': org.type === 'Enterprise',
                    'bg-[#22C55E]': org.type === 'SMB',
                    'bg-[#FA6340]': org.type === 'Startup'
                  }"
                >
                  {{ org.type }}
                </span>
              </td>
              <td class="text-content1 border-r border-[var(--border-color)]">
                <span
                  class="text-white px-2 py-1 rounded text-xs"
                  [ngClass]="{
                    'bg-[#22C55E]': org.status === 'Active',
                    'bg-[#FA6340]': org.status === 'Inactive',
                    'bg-[#FCA5A5]': org.status === 'Pending'
                  }"
                >
                  {{ org.status }}
                </span>
              </td>
              <td class="text-right">
                <div class="flex justify-end space-x-2">
                  <button
                    class="btn btn-sm btn-ghost h-9 w-9 min-h-0 p-0 rounded-full transition-all"
                    title="Edit Organization"
                    (click)="$event.stopPropagation(); openEditOrgModal(org)"
                  >
                    <i class="ti ti-pencil text-content1 text-lg"></i>
                  </button>
                  <button
                    class="btn btn-sm btn-ghost h-9 w-9 min-h-0 p-0 rounded-full transition-all"
                    title="Manage Organization Users"
                    (click)="
                      $event.stopPropagation();
                      navigateToOrganizationDetails(org.id || '')
                    "
                  >
                    <i class="ti ti-users text-primary text-lg"></i>
                  </button>
                </div>
              </td>
            </tr>

            <!-- Empty state -->
            <tr *ngIf="paginatedOrganizations.length === 0">
              <td colspan="5" class="py-16 text-center">
                <div class="flex flex-col items-center justify-center gap-4">
                  <div
                    class="rounded-full bg-backgroundSecondary p-4 mb-2 shadow-md"
                  >
                    <i
                      class="ti ti-building text-4xl text-primary empty-state-icon"
                    ></i>
                  </div>
                  <h3 class="text-xl font-medium text-content1">
                    No organizations found
                  </h3>
                  <p class="text-content2">
                    There are no organizations available yet. Add an
                    organization to get started.
                  </p>
                  <label
                    class="btn btn-primary btn-sm mt-4 transition-all hover:shadow-md"
                    for="AddOrganizationModal"
                  >
                    <i class="ti ti-plus text-base mr-1"></i>Add Organization
                  </label>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Pagination controls -->
        <div
          *ngIf="paginatedOrganizations && paginatedOrganizations.length > 0"
          class="flex justify-between items-center bg-backgroundPrimary p-4 border-t border-[var(--border-color)]"
        >
          <div class="flex flex-row items-center space-x-4">
            <div
              class="flex items-center px-3 py-1.5 bg-backgroundPrimary rounded-md border border-[var(--border-color)]"
            >
              <i class="ti ti-list text-primary mr-2"></i>
              <h2 class="text-sm font-medium text-content2 whitespace-nowrap">
                Showing
                <span class="text-content1 font-semibold">{{
                  currentPageStart
                }}</span>
                to
                <span class="text-content1 font-semibold">{{
                  currentPageEnd
                }}</span>
                of
                <span class="text-content1 font-semibold">{{
                  totalEntries
                }}</span>
                organizations
              </h2>
            </div>
            <div class="relative dropdown" style="width: 120px">
              <div
                class="flex items-center bg-backgroundPrimary rounded-md border border-[var(--border-color)]"
              >
                <select
                  class="select select-sm w-full rounded-md bg-backgroundPrimary text-content1 border-0 pl-3 pr-8 transition-all focus:ring-2 focus:ring-primary/20"
                  [(ngModel)]="pageSize"
                  (change)="changePageSize($event)"
                  style="
                    appearance: none;
                    -webkit-appearance: none;
                    -moz-appearance: none;
                  "
                >
                  <option *ngFor="let size of pageSizeOptions" [value]="size">
                    {{ size }} per page
                  </option>
                </select>
                <div
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-content2"
                >
                  <i
                    class="ti ti-chevron-down transition-transform text-primary"
                  ></i>
                </div>
              </div>
            </div>
          </div>

          <!-- Updated pagination section -->
          <div class="pagination flex items-center">
            <div
              class="flex items-center rounded-md bg-backgroundPrimary border border-[var(--border-color)] p-0.5"
            >
              <button
                class="btn btn-sm h-8 min-h-0 btn-ghost flex items-center text-content2 px-2 transition-all hover:bg-primary/10 hover:text-primary rounded-l-md rounded-r-none border-r border-[var(--border-color)]"
                (click)="prevPage()"
                [disabled]="currentPage === 1"
              >
                <span class="flex items-center"
                  ><i class="ti ti-chevron-left mr-1"></i>Prev</span
                >
              </button>

              <div class="flex items-center px-1">
                <ng-container *ngIf="totalPages <= 7">
                  <button
                    *ngFor="let page of [1, 2, 3]"
                    class="btn btn-sm h-7 min-h-0 w-7 p-0 mx-0.5 transition-all"
                    [ngClass]="{
                      'btn-primary text-white shadow-sm': currentPage === page,
                      'btn-ghost text-content2 hover:bg-primary/10 hover:text-primary':
                        currentPage !== page
                    }"
                    (click)="goToPage(page)"
                  >
                    {{ page }}
                  </button>
                </ng-container>
              </div>

              <button
                class="btn btn-sm h-8 min-h-0 btn-ghost flex items-center text-content2 px-2 transition-all hover:bg-primary/10 hover:text-primary rounded-r-md rounded-l-none border-l border-[var(--border-color)]"
                (click)="nextPage()"
                [disabled]="currentPage === totalPages"
              >
                <span class="flex items-center"
                  >Next <i class="ti ti-chevron-right ml-1"></i
                ></span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Organization Modal -->
    <input class="modal-state" id="EditOrganizationModal" type="checkbox" />
    <div class="modal w-screen">
      <label class="modal-overlay"></label>
      <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
        <div class="modal-header">
          <h2 class="text-xl">Edit Organization</h2>
          <label for="EditOrganizationModal" class="modal-close-btn"
            ><i class="ti ti-x"></i
          ></label>
        </div>

        <div class="p-3 py-2 max-h-[80%] overflow-auto">
          <!-- Alert messages -->
          <div *ngIf="errorMessage" class="alert alert-error mb-4">
            <i class="ti ti-circle-x mr-2"></i>
            {{ errorMessage }}
          </div>
          <div *ngIf="successMessage" class="alert alert-success mb-4">
            <i class="ti ti-circle-check mr-2"></i>
            {{ successMessage }}
          </div>

          <div class="p-3 py-2">
            <form [formGroup]="organizationForm">
              <section class="py-3">
                <div class="form-group">
                  <div class="form-field mb-4">
                    <label class="form-label">Organization Name*</label>
                    <input
                      type="text"
                      placeholder="Enter organization name"
                      class="input max-w-full"
                      formControlName="name"
                    />
                    <div
                      *ngIf="
                        organizationForm.get('name')?.invalid &&
                        organizationForm.get('name')?.touched
                      "
                      class="text-error text-sm mt-1"
                    >
                      Organization name is required
                    </div>
                  </div>

                  <div class="form-field mb-4">
                    <label class="form-label">Email*</label>
                    <input
                      type="email"
                      placeholder="Enter organization email"
                      class="input max-w-full"
                      formControlName="email"
                    />
                    <div
                      *ngIf="
                        organizationForm.get('email')?.invalid &&
                        organizationForm.get('email')?.touched
                      "
                      class="text-error text-sm mt-1"
                    >
                      <span
                        *ngIf="organizationForm.get('email')?.errors?.['required']"
                      >
                        Email is required
                      </span>
                      <span
                        *ngIf="organizationForm.get('email')?.errors?.['email']"
                      >
                        Please enter a valid email
                      </span>
                    </div>
                  </div>

                  <div class="form-field mb-4">
                    <label class="form-label">Domain</label>
                    <input
                      type="text"
                      placeholder="Enter domain (e.g., example.com)"
                      class="input max-w-full"
                      formControlName="domain"
                    />
                    <div class="text-content2 text-sm mt-1">
                      If left empty, domain will be extracted from the email
                    </div>
                  </div>

                  <div class="form-field mb-4">
                    <label class="form-label">Type*</label>
                    <select class="select max-w-full" formControlName="type">
                      <option value="Enterprise">Enterprise</option>
                      <option value="SMB">SMB</option>
                      <option value="Startup">Startup</option>
                    </select>
                  </div>

                  <div class="form-field mb-4">
                    <label class="form-label">Status*</label>
                    <select class="select max-w-full" formControlName="status">
                      <option value="Active">Active</option>
                      <option value="Inactive">Inactive</option>
                      <option value="Pending">Pending</option>
                    </select>
                  </div>

                  <div class="form-field mb-4">
                    <label class="form-label">Website</label>
                    <input
                      type="url"
                      placeholder="Enter website URL"
                      class="input max-w-full"
                      formControlName="website"
                    />
                  </div>

                  <div class="form-field mb-4">
                    <label class="form-label">Address</label>
                    <textarea
                      placeholder="Enter organization address"
                      class="textarea max-w-full"
                      formControlName="address"
                    ></textarea>
                  </div>

                  <div class="form-field">
                    <label class="form-label">Description</label>
                    <textarea
                      placeholder="Enter organization description"
                      class="textarea max-w-full"
                      formControlName="description"
                    ></textarea>
                  </div>
                </div>
              </section>
            </form>
          </div>
        </div>
        <div class="flex gap-3 p-3 border-t justify-end">
          <div class="flex gap-3">
            <label for="EditOrganizationModal" class="btn btn-ghost btn-sm">
              Cancel
            </label>
            <button
              class="btn btn-primary btn-sm"
              (click)="updateOrganization()"
              [disabled]="isLoading"
            >
              <span
                *ngIf="isLoading"
                class="loading loading-spinner loading-xs mr-2"
              ></span>
              Update Organization
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Organization Modal -->
    <input class="modal-state" id="AddOrganizationModal" type="checkbox" />
    <div class="modal w-screen">
      <label class="modal-overlay"></label>
      <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
        <div class="modal-header">
          <h2 class="text-xl">Add Organization</h2>
          <label for="AddOrganizationModal" class="modal-close-btn"
            ><i class="ti ti-x"></i
          ></label>
        </div>

        <div class="p-3 py-2 max-h-[80%] overflow-auto">
          <!-- Alert messages -->
          <div *ngIf="errorMessage" class="alert alert-error mb-4">
            <i class="ti ti-circle-x mr-2"></i>
            {{ errorMessage }}
          </div>
          <div *ngIf="successMessage" class="alert alert-success mb-4">
            <i class="ti ti-circle-check mr-2"></i>
            {{ successMessage }}
          </div>

          <div class="p-3 py-2">
            <form [formGroup]="organizationForm">
              <section class="py-3">
                <div class="form-group">
                  <div class="form-field mb-4">
                    <label class="form-label">Organization Name*</label>
                    <input
                      type="text"
                      placeholder="Enter organization name"
                      class="input max-w-full"
                      formControlName="name"
                    />
                    <div
                      *ngIf="
                        organizationForm.get('name')?.invalid &&
                        organizationForm.get('name')?.touched
                      "
                      class="text-error text-sm mt-1"
                    >
                      Organization name is required
                    </div>
                  </div>

                  <div class="form-field mb-4">
                    <label class="form-label">Email*</label>
                    <input
                      type="email"
                      placeholder="Enter organization email"
                      class="input max-w-full"
                      formControlName="email"
                    />
                    <div
                      *ngIf="
                        organizationForm.get('email')?.invalid &&
                        organizationForm.get('email')?.touched
                      "
                      class="text-error text-sm mt-1"
                    >
                      <span
                        *ngIf="organizationForm.get('email')?.errors?.['required']"
                      >
                        Email is required
                      </span>
                      <span
                        *ngIf="organizationForm.get('email')?.errors?.['email']"
                      >
                        Please enter a valid email
                      </span>
                    </div>
                  </div>

                  <div class="form-field mb-4">
                    <label class="form-label">Domain</label>
                    <input
                      type="text"
                      placeholder="Enter domain (e.g., example.com)"
                      class="input max-w-full"
                      formControlName="domain"
                    />
                    <div class="text-content2 text-sm mt-1">
                      If left empty, domain will be extracted from the email
                    </div>
                  </div>

                  <div class="form-field mb-4">
                    <label class="form-label">Type*</label>
                    <select class="select max-w-full" formControlName="type">
                      <option value="Enterprise">Enterprise</option>
                      <option value="SMB">SMB</option>
                      <option value="Startup">Startup</option>
                    </select>
                  </div>

                  <div class="form-field mb-4">
                    <label class="form-label">Status*</label>
                    <select class="select max-w-full" formControlName="status">
                      <option value="Active">Active</option>
                      <option value="Inactive">Inactive</option>
                      <option value="Pending">Pending</option>
                    </select>
                  </div>

                  <div class="form-field mb-4">
                    <label class="form-label">Website</label>
                    <input
                      type="url"
                      placeholder="Enter website URL"
                      class="input max-w-full"
                      formControlName="website"
                    />
                  </div>

                  <div class="form-field mb-4">
                    <label class="form-label">Address</label>
                    <textarea
                      placeholder="Enter organization address"
                      class="textarea max-w-full"
                      formControlName="address"
                    ></textarea>
                  </div>

                  <div class="form-field">
                    <label class="form-label">Description</label>
                    <textarea
                      placeholder="Enter organization description"
                      class="textarea max-w-full"
                      formControlName="description"
                    ></textarea>
                  </div>
                </div>
              </section>
            </form>
          </div>
        </div>
        <div class="flex gap-3 p-3 border-t justify-end">
          <div class="flex gap-3">
            <label for="AddOrganizationModal" class="btn btn-ghost btn-sm">
              Cancel
            </label>
            <button
              class="btn btn-primary btn-sm"
              (click)="createOrganization()"
              [disabled]="isLoading || !organizationForm.valid"
            >
              <span
                *ngIf="isLoading"
                class="loading loading-spinner loading-xs mr-2"
              ></span>
              Create Organization
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Add User Modal -->
  <input class="modal-state" id="AddUserModal" type="checkbox" />
  <div class="modal w-screen">
    <label class="modal-overlay"></label>
    <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
      <div class="modal-header">
        <h2 class="text-xl">Add User</h2>
        <label for="AddUserModal" class="modal-close-btn"
          ><i class="ti ti-x"></i
        ></label>
      </div>

      <div class="p-3 py-2 max-h-[80%] overflow-auto">
        <!-- Alert messages -->
        <div *ngIf="errorMessage" class="alert alert-error mb-4">
          <i class="ti ti-circle-x mr-2"></i>
          {{ errorMessage }}
        </div>
        <div *ngIf="successMessage" class="alert alert-success mb-4">
          <i class="ti ti-circle-check mr-2"></i>
          {{ successMessage }}
        </div>

        <div class="p-3 py-2">
          <form [formGroup]="userForm">
            <section class="py-3">
              <div class="form-group">
                <div class="form-field mb-4">
                  <label class="form-label">Full Name*</label>
                  <input
                    type="text"
                    placeholder="Enter full name"
                    class="input max-w-full"
                    formControlName="full_name"
                  />
                  <div
                    *ngIf="
                      userForm.get('full_name')?.invalid &&
                      userForm.get('full_name')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    Full name is required
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Email*</label>
                  <input
                    type="email"
                    placeholder="Enter email"
                    class="input max-w-full"
                    formControlName="email"
                  />
                  <div
                    *ngIf="
                      userForm.get('email')?.invalid &&
                      userForm.get('email')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    <span *ngIf="userForm.get('email')?.errors?.['required']">
                      Email is required
                    </span>
                    <span *ngIf="userForm.get('email')?.errors?.['email']">
                      Please enter a valid email
                    </span>
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Password*</label>
                  <input
                    type="password"
                    placeholder="Enter password"
                    class="input max-w-full"
                    formControlName="password"
                  />
                  <div
                    *ngIf="
                      userForm.get('password')?.invalid &&
                      userForm.get('password')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    <span
                      *ngIf="userForm.get('password')?.errors?.['required']"
                    >
                      Password is required
                    </span>
                    <span
                      *ngIf="userForm.get('password')?.errors?.['minlength']"
                    >
                      Password must be at least 8 characters
                    </span>
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Confirm Password*</label>
                  <input
                    type="password"
                    placeholder="Confirm password"
                    class="input max-w-full"
                    formControlName="confirm_password"
                  />
                  <div
                    *ngIf="
                      userForm.get('confirm_password')?.invalid &&
                      userForm.get('confirm_password')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    <span
                      *ngIf="userForm.get('confirm_password')?.errors?.['required']"
                    >
                      Please confirm your password
                    </span>
                    <span
                      *ngIf="userForm.get('confirm_password')?.errors?.['passwordMismatch']"
                    >
                      Passwords do not match
                    </span>
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Role*</label>
                  <select class="select max-w-full" formControlName="role_id">
                    <option value="" disabled>Select a role</option>
                    <option *ngFor="let role of roles" [value]="role.id">
                      {{ role.role }}
                    </option>
                  </select>
                  <div
                    *ngIf="
                      userForm.get('role_id')?.invalid &&
                      userForm.get('role_id')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    Please select a role
                  </div>
                </div>

                <div class="form-field">
                  <label class="form-label flex items-center gap-2">
                    <input
                      type="checkbox"
                      class="checkbox"
                      formControlName="send_welcome_email"
                    />
                    <span>Send welcome email</span>
                  </label>
                </div>
              </div>
            </section>
          </form>
        </div>
      </div>
      <div class="flex gap-3 p-3 border-t justify-end">
        <div class="flex gap-3">
          <label for="AddUserModal" class="btn btn-ghost btn-sm">
            Cancel
          </label>
          <button
            class="btn btn-primary btn-sm"
            (click)="createUser()"
            [disabled]="isLoading || !userForm.valid"
          >
            <span
              *ngIf="isLoading"
              class="loading loading-spinner loading-xs mr-2"
            ></span>
            Create User
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Edit User Modal -->
  <input class="modal-state" id="EditUserModal" type="checkbox" />
  <div class="modal w-screen">
    <label class="modal-overlay"></label>
    <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
      <div class="modal-header">
        <h2 class="text-xl">Edit User</h2>
        <label for="EditUserModal" class="modal-close-btn"
          ><i class="ti ti-x"></i
        ></label>
      </div>

      <div class="p-3 py-2 max-h-[80%] overflow-auto">
        <!-- Alert messages -->
        <div *ngIf="errorMessage" class="alert alert-error mb-4">
          <i class="ti ti-circle-x mr-2"></i>
          {{ errorMessage }}
        </div>
        <div *ngIf="successMessage" class="alert alert-success mb-4">
          <i class="ti ti-circle-check mr-2"></i>
          {{ successMessage }}
        </div>

        <div class="p-3 py-2">
          <form [formGroup]="userForm">
            <section class="py-3">
              <div class="form-group">
                <div class="form-field mb-4">
                  <label class="form-label">Full Name*</label>
                  <input
                    type="text"
                    placeholder="Enter full name"
                    class="input max-w-full"
                    formControlName="full_name"
                  />
                  <div
                    *ngIf="
                      userForm.get('full_name')?.invalid &&
                      userForm.get('full_name')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    Full name is required
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Email*</label>
                  <input
                    type="email"
                    placeholder="Enter email"
                    class="input max-w-full"
                    formControlName="email"
                  />
                  <div
                    *ngIf="
                      userForm.get('email')?.invalid &&
                      userForm.get('email')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    <span *ngIf="userForm.get('email')?.errors?.['required']">
                      Email is required
                    </span>
                    <span *ngIf="userForm.get('email')?.errors?.['email']">
                      Please enter a valid email
                    </span>
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Role*</label>
                  <select class="select max-w-full" formControlName="role_id">
                    <option value="" disabled>Select a role</option>
                    <option *ngFor="let role of roles" [value]="role.id">
                      {{ role.role }}
                    </option>
                  </select>
                  <div
                    *ngIf="
                      userForm.get('role_id')?.invalid &&
                      userForm.get('role_id')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    Please select a role
                  </div>
                </div>
              </div>
            </section>
          </form>
        </div>
      </div>
      <div class="flex gap-3 p-3 border-t justify-end">
        <div class="flex gap-3">
          <label for="EditUserModal" class="btn btn-ghost btn-sm">
            Cancel
          </label>
          <button
            class="btn btn-primary btn-sm"
            (click)="updateUser()"
            [disabled]="isLoading || !userForm.valid"
          >
            <span
              *ngIf="isLoading"
              class="loading loading-spinner loading-xs mr-2"
            ></span>
            Update User
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Select User Type Modal -->
  <input class="modal-state" id="SelectUserTypeModal" type="checkbox" />
  <div class="modal w-screen">
    <label class="modal-overlay"></label>
    <div class="modal-content p-0 flex flex-col max-w-md bg-backgroundPrimary">
      <div class="modal-header">
        <h2 class="text-xl">Select User Type</h2>
        <label for="SelectUserTypeModal" class="modal-close-btn"
          ><i class="ti ti-x"></i
        ></label>
      </div>

      <div class="p-6">
        <div class="flex flex-col gap-4">
          <button
            *ngIf="canCreateAdmins()"
            class="btn btn-primary"
            (click)="openAddAdminModal()"
          >
            <i class="ti ti-user-shield mr-2"></i>
            Create Organization Admin
          </button>
          <button class="btn btn-secondary" (click)="openAddUserModal()">
            <i class="ti ti-user-plus mr-2"></i>
            Create Regular User
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Role Modal -->
  <input class="modal-state" id="AddRoleModal" type="checkbox" />
  <div class="modal w-screen">
    <label class="modal-overlay"></label>
    <div
      class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%] bg-backgroundPrimary"
    >
      <div class="modal-header">
        <h2 class="text-xl">Add Custom Role</h2>
        <label for="AddRoleModal" class="modal-close-btn"
          ><i class="ti ti-x"></i
        ></label>
      </div>

      <div class="p-3 py-2 max-h-[80%] overflow-auto">
        <!-- Alert messages -->
        <div *ngIf="errorMessage" class="alert alert-error mb-4">
          <i class="ti ti-circle-x mr-2"></i>
          {{ errorMessage }}
        </div>
        <div *ngIf="successMessage" class="alert alert-success mb-4">
          <i class="ti ti-circle-check mr-2"></i>
          {{ successMessage }}
        </div>

        <div class="p-3 py-2">
          <form [formGroup]="roleForm">
            <section class="py-3">
              <div class="form-group">
                <div class="form-field mb-4">
                  <label class="form-label">Role Name*</label>
                  <input
                    type="text"
                    placeholder="Enter role name"
                    class="input max-w-full"
                    formControlName="name"
                  />
                  <div
                    *ngIf="
                      roleForm.get('name')?.invalid &&
                      roleForm.get('name')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    Role name is required
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Description*</label>
                  <textarea
                    placeholder="Enter role description"
                    class="textarea max-w-full"
                    formControlName="description"
                  ></textarea>
                  <div
                    *ngIf="
                      roleForm.get('description')?.invalid &&
                      roleForm.get('description')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    Description is required
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label flex items-center gap-2">
                    <input
                      type="checkbox"
                      class="checkbox"
                      formControlName="is_sharable"
                    />
                    <span>Sharable with other organizations</span>
                  </label>
                </div>

                <div class="form-field">
                  <label class="form-label">Permissions*</label>
                  <div
                    class="border rounded-md p-4 max-h-[300px] overflow-y-auto"
                  >
                    <div
                      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3"
                    >
                      <div
                        *ngFor="let privilege of privileges$ | async"
                        class="flex items-center gap-2"
                      >
                        <input
                          type="checkbox"
                          class="checkbox"
                          [id]="'privilege-' + privilege.id"
                          [checked]="selectedPrivileges.includes(privilege.id)"
                          (change)="togglePrivilege(privilege.id, $event)"
                        />
                        <label
                          [for]="'privilege-' + privilege.id"
                          class="cursor-pointer text-sm"
                        >
                          {{ formatPermissionName(privilege.name) }}
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </form>
        </div>
      </div>
      <div class="flex gap-3 p-3 border-t justify-end">
        <div class="flex gap-3">
          <label for="AddRoleModal" class="btn btn-ghost btn-sm">
            Cancel
          </label>
          <button
            class="btn btn-primary btn-sm"
            (click)="createRole()"
            [disabled]="isLoading || !roleForm.valid"
          >
            <span
              *ngIf="isLoading"
              class="loading loading-spinner loading-xs mr-2"
            ></span>
            Create Role
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Edit Role Modal -->
  <input class="modal-state" id="EditRoleModal" type="checkbox" />
  <div class="modal w-screen">
    <label class="modal-overlay"></label>
    <div
      class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%] bg-backgroundPrimary"
    >
      <div class="modal-header">
        <h2 class="text-xl">Edit Role</h2>
        <label for="EditRoleModal" class="modal-close-btn"
          ><i class="ti ti-x"></i
        ></label>
      </div>

      <div class="p-3 py-2 max-h-[80%] overflow-auto">
        <!-- Alert messages -->
        <div *ngIf="errorMessage" class="alert alert-error mb-4">
          <i class="ti ti-circle-x mr-2"></i>
          {{ errorMessage }}
        </div>
        <div *ngIf="successMessage" class="alert alert-success mb-4">
          <i class="ti ti-circle-check mr-2"></i>
          {{ successMessage }}
        </div>

        <div class="p-3 py-2">
          <form [formGroup]="roleForm">
            <section class="py-3">
              <div class="form-group">
                <div class="form-field mb-4">
                  <label class="form-label">Role Name*</label>
                  <input
                    type="text"
                    placeholder="Enter role name"
                    class="input max-w-full"
                    formControlName="name"
                  />
                  <div
                    *ngIf="
                      roleForm.get('name')?.invalid &&
                      roleForm.get('name')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    Role name is required
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Description*</label>
                  <textarea
                    placeholder="Enter role description"
                    class="textarea max-w-full"
                    formControlName="description"
                  ></textarea>
                  <div
                    *ngIf="
                      roleForm.get('description')?.invalid &&
                      roleForm.get('description')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    Description is required
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label flex items-center gap-2">
                    <input
                      type="checkbox"
                      class="checkbox"
                      formControlName="is_sharable"
                    />
                    <span>Sharable with other organizations</span>
                  </label>
                </div>

                <div class="form-field">
                  <label class="form-label">Permissions*</label>
                  <div
                    class="border rounded-md p-4 max-h-[300px] overflow-y-auto"
                  >
                    <div
                      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3"
                    >
                      <div
                        *ngFor="let privilege of privileges$ | async"
                        class="flex items-center gap-2"
                      >
                        <input
                          type="checkbox"
                          class="checkbox"
                          [id]="'edit-privilege-' + privilege.id"
                          [checked]="selectedPrivileges.includes(privilege.id)"
                          (change)="togglePrivilege(privilege.id, $event)"
                        />
                        <label
                          [for]="'edit-privilege-' + privilege.id"
                          class="cursor-pointer text-sm"
                          [class.opacity-50]="!canEditRole(selectedRole)"
                        >
                          {{ formatPermissionName(privilege.name) }}
                        </label>
                      </div>
                    </div>
                  </div>
                  <div
                    *ngIf="!canEditRole(selectedRole)"
                    class="text-warning text-sm mt-2"
                  >
                    <i class="ti ti-alert-circle mr-1"></i>
                    <span *ngIf="isPredefinedRole(selectedRole?.role)"
                      >Predefined roles can only be edited by super users</span
                    >
                    <span *ngIf="!isPredefinedRole(selectedRole?.role)"
                      >You don't have permission to edit this role</span
                    >
                  </div>
                </div>
              </div>
            </section>
          </form>
        </div>
      </div>
      <div class="flex gap-3 p-3 border-t justify-end">
        <div class="flex gap-3">
          <label for="EditRoleModal" class="btn btn-ghost btn-sm">
            Cancel
          </label>
          <button
            class="btn btn-primary btn-sm"
            (click)="updateRole()"
            [disabled]="
              isLoading || !roleForm.valid || !canEditRole(selectedRole)
            "
          >
            <span
              *ngIf="isLoading"
              class="loading loading-spinner loading-xs mr-2"
            ></span>
            Update Role
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- View Role Modal -->
  <input class="modal-state" id="ViewRoleModal" type="checkbox" />
  <div class="modal w-screen">
    <label class="modal-overlay"></label>
    <div
      class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%] bg-backgroundPrimary"
    >
      <div class="modal-header">
        <h2 class="text-xl">Role Details</h2>
        <label for="ViewRoleModal" class="modal-close-btn"
          ><i class="ti ti-x"></i
        ></label>
      </div>

      <div class="p-6 max-h-[80%] overflow-auto">
        <div *ngIf="selectedRole" class="space-y-6">
          <div class="flex items-center gap-3">
            <h3 class="text-xl font-semibold">{{ selectedRole.role }}</h3>
            <span
              *ngIf="isPredefinedRole(selectedRole.role)"
              class="text-xs bg-[#3B82F6] text-white px-2 py-1 rounded-full"
            >
              Predefined
            </span>
            <span
              *ngIf="!isPredefinedRole(selectedRole.role)"
              class="text-xs bg-[#10B981] text-white px-2 py-1 rounded-full"
            >
              Custom
            </span>
          </div>

          <div>
            <h4 class="text-sm font-medium text-content2 mb-1">Description</h4>
            <p class="text-content1">{{ selectedRole.description }}</p>
          </div>

          <div>
            <h4 class="text-sm font-medium text-content2 mb-2">Permissions</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <div
                *ngFor="
                  let permission of getPermissionsList(selectedRole.permissions)
                "
                class="flex items-center gap-2"
              >
                <i class="ti ti-check text-success"></i>
                <span class="text-content1">{{
                  formatPermissionName(permission)
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex gap-3 p-3 border-t justify-end">
        <div class="flex gap-3">
          <label for="ViewRoleModal" class="btn btn-ghost btn-sm">
            Close
          </label>
          <button
            *ngIf="canEditRole(selectedRole)"
            class="btn btn-primary btn-sm"
            (click)="openEditRoleModal(selectedRole)"
          >
            <i class="ti ti-pencil mr-1"></i> Edit Role
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- Delete Role Modal -->
  <input class="modal-state" id="DeleteRoleModal" type="checkbox" />
  <div class="modal w-screen">
    <label class="modal-overlay"></label>
    <div class="modal-content p-0 flex flex-col max-w-md bg-backgroundPrimary">
      <div class="modal-header">
        <h2 class="text-xl">Delete Role</h2>
        <label for="DeleteRoleModal" class="modal-close-btn"
          ><i class="ti ti-x"></i
        ></label>
      </div>

      <div class="p-6">
        <!-- Alert messages -->
        <div *ngIf="errorMessage" class="alert alert-error mb-4">
          <i class="ti ti-circle-x mr-2"></i>
          {{ errorMessage }}
        </div>
        <div *ngIf="successMessage" class="alert alert-success mb-4">
          <i class="ti ti-circle-check mr-2"></i>
          {{ successMessage }}
        </div>

        <div *ngIf="selectedRole" class="space-y-4">
          <div class="flex items-center gap-2 text-error">
            <i class="ti ti-alert-triangle text-2xl"></i>
            <h3 class="text-lg font-medium">Confirm Deletion</h3>
          </div>

          <p class="text-content1">
            Are you sure you want to delete the role
            <span class="font-semibold">{{ selectedRole.role }}</span
            >?
          </p>

          <div
            class="bg-error/10 border border-error/20 rounded-md p-4 text-sm"
          >
            <p class="text-error">
              <i class="ti ti-info-circle mr-1"></i>
              This action cannot be undone. Users with this role will lose their
              permissions.
            </p>
          </div>
        </div>
      </div>

      <div class="flex gap-3 p-3 border-t justify-end">
        <div class="flex gap-3">
          <label for="DeleteRoleModal" class="btn btn-ghost btn-sm">
            Cancel
          </label>
          <button
            class="btn btn-error btn-sm"
            (click)="deleteRole()"
            [disabled]="isLoading"
          >
            <span
              *ngIf="isLoading"
              class="loading loading-spinner loading-xs mr-2"
            ></span>
            Delete Role
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Admin Modal -->
  <input class="modal-state" id="AddAdminModal" type="checkbox" />
  <div class="modal w-screen">
    <label class="modal-overlay"></label>
    <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
      <div class="modal-header">
        <h2 class="text-xl">Add Organization Admin</h2>
        <label for="AddAdminModal" class="modal-close-btn"
          ><i class="ti ti-x"></i
        ></label>
      </div>

      <div class="p-3 py-2 max-h-[80%] overflow-auto">
        <!-- Alert messages -->
        <div *ngIf="errorMessage" class="alert alert-error mb-4">
          <i class="ti ti-circle-x mr-2"></i>
          {{ errorMessage }}
        </div>
        <div *ngIf="successMessage" class="alert alert-success mb-4">
          <i class="ti ti-circle-check mr-2"></i>
          {{ successMessage }}
        </div>

        <div class="p-3 py-2">
          <form [formGroup]="adminForm">
            <section class="py-3">
              <div class="form-group">
                <div class="form-field mb-4">
                  <label class="form-label">Full Name*</label>
                  <input
                    type="text"
                    placeholder="Enter full name"
                    class="input max-w-full"
                    formControlName="full_name"
                  />
                  <div
                    *ngIf="
                      adminForm.get('full_name')?.invalid &&
                      adminForm.get('full_name')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    Full name is required
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Email*</label>
                  <input
                    type="email"
                    placeholder="Enter email"
                    class="input max-w-full"
                    formControlName="email"
                  />
                  <div
                    *ngIf="
                      adminForm.get('email')?.invalid &&
                      adminForm.get('email')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    <span *ngIf="adminForm.get('email')?.errors?.['required']">
                      Email is required
                    </span>
                    <span *ngIf="adminForm.get('email')?.errors?.['email']">
                      Please enter a valid email
                    </span>
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Password*</label>
                  <input
                    type="password"
                    placeholder="Enter password"
                    class="input max-w-full"
                    formControlName="password"
                  />
                  <div
                    *ngIf="
                      adminForm.get('password')?.invalid &&
                      adminForm.get('password')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    <span
                      *ngIf="adminForm.get('password')?.errors?.['required']"
                    >
                      Password is required
                    </span>
                    <span
                      *ngIf="adminForm.get('password')?.errors?.['minlength']"
                    >
                      Password must be at least 8 characters
                    </span>
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Confirm Password*</label>
                  <input
                    type="password"
                    placeholder="Confirm password"
                    class="input max-w-full"
                    formControlName="confirm_password"
                  />
                  <div
                    *ngIf="
                      adminForm.get('confirm_password')?.invalid &&
                      adminForm.get('confirm_password')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    <span
                      *ngIf="adminForm.get('confirm_password')?.errors?.['required']"
                    >
                      Please confirm your password
                    </span>
                    <span
                      *ngIf="adminForm.get('confirm_password')?.errors?.['passwordMismatch']"
                    >
                      Passwords do not match
                    </span>
                  </div>
                </div>

                <div class="form-field">
                  <label class="form-label flex items-center gap-2">
                    <input
                      type="checkbox"
                      class="checkbox"
                      formControlName="send_welcome_email"
                    />
                    <span>Send welcome email</span>
                  </label>
                </div>
              </div>
            </section>
          </form>
        </div>
      </div>
      <div class="flex gap-3 p-3 border-t justify-end">
        <div class="flex gap-3">
          <label for="AddAdminModal" class="btn btn-ghost btn-sm">
            Cancel
          </label>
          <button
            class="btn btn-primary btn-sm"
            (click)="createAdmin()"
            [disabled]="isLoading || !adminForm.valid"
          >
            <span
              *ngIf="isLoading"
              class="loading loading-spinner loading-xs mr-2"
            ></span>
            Create Admin
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Super User Modal -->
  <input class="modal-state" id="AddSuperUserModal" type="checkbox" />
  <div class="modal w-screen">
    <label class="modal-overlay"></label>
    <div class="modal-content p-0 flex flex-col max-w-3xl min-w-[50%]">
      <div class="modal-header">
        <h2 class="text-xl">Manage Super User</h2>
        <label for="AddSuperUserModal" class="modal-close-btn"
          ><i class="ti ti-x"></i
        ></label>
      </div>

      <div class="p-3 py-2 max-h-[80%] overflow-auto">
        <!-- Alert messages -->
        <div *ngIf="errorMessage" class="alert alert-error mb-4">
          <i class="ti ti-circle-x mr-2"></i>
          {{ errorMessage }}
        </div>
        <div *ngIf="successMessage" class="alert alert-success mb-4">
          <i class="ti ti-circle-check mr-2"></i>
          {{ successMessage }}
        </div>

        <div class="p-3 py-2">
          <div *ngIf="superUserExists" class="alert alert-info mb-4">
            <i class="ti ti-info-circle mr-2"></i>
            A super user already exists in the system. You cannot create another
            one.
          </div>

          <form [formGroup]="superUserForm" *ngIf="!superUserExists">
            <section class="py-3">
              <div class="form-group">
                <div class="form-field mb-4">
                  <label class="form-label">Full Name*</label>
                  <input
                    type="text"
                    placeholder="Enter full name"
                    class="input max-w-full"
                    formControlName="full_name"
                  />
                  <div
                    *ngIf="
                      superUserForm.get('full_name')?.invalid &&
                      superUserForm.get('full_name')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    Full name is required
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Email*</label>
                  <input
                    type="email"
                    placeholder="Enter email"
                    class="input max-w-full"
                    formControlName="email"
                  />
                  <div
                    *ngIf="
                      superUserForm.get('email')?.invalid &&
                      superUserForm.get('email')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    <span
                      *ngIf="superUserForm.get('email')?.errors?.['required']"
                    >
                      Email is required
                    </span>
                    <span *ngIf="superUserForm.get('email')?.errors?.['email']">
                      Please enter a valid email
                    </span>
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Password*</label>
                  <input
                    type="password"
                    placeholder="Enter password"
                    class="input max-w-full"
                    formControlName="password"
                  />
                  <div
                    *ngIf="
                      superUserForm.get('password')?.invalid &&
                      superUserForm.get('password')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    <span
                      *ngIf="superUserForm.get('password')?.errors?.['required']"
                    >
                      Password is required
                    </span>
                    <span
                      *ngIf="superUserForm.get('password')?.errors?.['minlength']"
                    >
                      Password must be at least 8 characters
                    </span>
                  </div>
                </div>

                <div class="form-field mb-4">
                  <label class="form-label">Confirm Password*</label>
                  <input
                    type="password"
                    placeholder="Confirm password"
                    class="input max-w-full"
                    formControlName="confirm_password"
                  />
                  <div
                    *ngIf="
                      superUserForm.get('confirm_password')?.invalid &&
                      superUserForm.get('confirm_password')?.touched
                    "
                    class="text-error text-sm mt-1"
                  >
                    <span
                      *ngIf="superUserForm.get('confirm_password')?.errors?.['required']"
                    >
                      Please confirm your password
                    </span>
                    <span
                      *ngIf="superUserForm.get('confirm_password')?.errors?.['passwordMismatch']"
                    >
                      Passwords do not match
                    </span>
                  </div>
                </div>
              </div>
            </section>
          </form>
        </div>
      </div>
      <div class="flex gap-3 p-3 border-t justify-end">
        <div class="flex gap-3">
          <label for="AddSuperUserModal" class="btn btn-ghost btn-sm">
            Cancel
          </label>
          <button
            class="btn btn-primary btn-sm"
            (click)="createSuperUser()"
            [disabled]="isLoading || !superUserForm.valid || superUserExists"
            *ngIf="!superUserExists"
          >
            <span
              *ngIf="isLoading"
              class="loading loading-spinner loading-xs mr-2"
            ></span>
            Create Super User
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Change Role Modal -->
  <input class="modal-state" id="ChangeRoleModal" type="checkbox" />
  <div class="modal w-screen">
    <label class="modal-overlay"></label>
    <div class="modal-content p-0 flex flex-col max-w-md">
      <div class="modal-header">
        <h2 class="text-xl">Change User Role</h2>
        <label for="ChangeRoleModal" class="modal-close-btn"
          ><i class="ti ti-x"></i
        ></label>
      </div>

      <div class="p-6">
        <!-- Alert messages -->
        <div *ngIf="errorMessage" class="alert alert-error mb-4">
          <i class="ti ti-circle-x mr-2"></i>
          {{ errorMessage }}
        </div>
        <div *ngIf="successMessage" class="alert alert-success mb-4">
          <i class="ti ti-circle-check mr-2"></i>
          {{ successMessage }}
        </div>

        <div *ngIf="selectedUser" class="space-y-4">
          <div class="flex items-center gap-2">
            <i class="ti ti-user text-2xl text-primary"></i>
            <h3 class="text-lg font-medium">{{ selectedUser.displayName }}</h3>
          </div>

          <div class="form-field">
            <label class="form-label">Select Role</label>
            <select id="roleSelect" class="select max-w-full">
              <option value="" disabled selected>Select a role</option>
              <option *ngFor="let role of roles" [value]="role.id">
                {{ role.role }}
              </option>
            </select>
          </div>
        </div>
      </div>

      <div class="flex gap-3 p-3 border-t justify-end">
        <div class="flex gap-3">
          <label for="ChangeRoleModal" class="btn btn-ghost btn-sm">
            Cancel
          </label>
          <button
            class="btn btn-primary btn-sm"
            (click)="updateUserRole()"
            [disabled]="isLoading"
          >
            <span
              *ngIf="isLoading"
              class="loading loading-spinner loading-xs mr-2"
            ></span>
            Update Role
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
